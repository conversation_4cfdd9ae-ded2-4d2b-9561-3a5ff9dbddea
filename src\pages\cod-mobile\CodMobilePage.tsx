@@ .. @@
                 <h1 className="text-4xl font-bold text-white mb-2">{game.title}</h1>
                 <div className="flex items-center gap-4 text-gray-300">
                  <button
                    onClick={() => navigate('/external-games/cod-mobile/matchmaking')}
                    className="bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold px-8 py-3 rounded-xl flex items-center gap-2 hover:opacity-90"
                  >
                    <Gamepad2 className="w-5 h-5" />
                    Jogar 1v1
                  </button>
                  <span className="flex items-center gap-1">
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTriggers() {
  console.log('🔍 Verificando triggers no banco de dados...\n');

  try {
    // Método direto para verificar triggers
    console.log('📋 Verificando triggers na tabela transactions...');
    
    // Primeiro, vamos verificar se conseguimos executar uma query simples
    const { data: testQuery, error: testError } = await supabase
      .from('transactions')
      .select('count')
      .limit(1);

    if (testError) {
      console.error('❌ Erro na conexão básica:', testError);
      return;
    }

    console.log('✅ Conexão com banco funcionando');

    // Vamos tentar recriar o trigger
    console.log('🔧 Tentando recriar o trigger...');
    
    // Primeiro, vamos verificar se a função existe
    console.log('1. Verificando se a função update_wallet_balance existe...');
    
    // Vamos tentar executar uma transação de teste para ver se o trigger funciona
    console.log('2. Testando trigger com transação real...');
    
    // Buscar um usuário para teste
    const { data: testUser, error: userError } = await supabase
      .from('users')
      .select('id, username')
      .limit(1)
      .single();

    if (userError || !testUser) {
      console.error('❌ Erro ao buscar usuário para teste:', userError);
      return;
    }

    console.log(`👤 Usando usuário: ${testUser.username}`);
    
    // Buscar wallet do usuário
    const { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .select('id, balance')
      .eq('user_id', testUser.id)
      .single();

    if (walletError || !wallet) {
      console.error('❌ Erro ao buscar wallet:', walletError);
      return;
    }

    console.log(`💰 Saldo inicial: R$ ${wallet.balance}`);

    // Criar transação de teste com status pending primeiro
    const testAmount = 5.00;
    const { data: transaction, error: txError } = await supabase
      .from('transactions')
      .insert({
        user_id: testUser.id,
        wallet_id: wallet.id,
        type: 'win',
        amount: testAmount,
        description: 'Teste de trigger - prêmio',
        status: 'pending'
      })
      .select()
      .single();

    if (txError) {
      console.error('❌ Erro ao criar transação:', txError);
      return;
    }

    console.log(`📝 Transação criada com status pending: ${transaction.id}`);

    // Aguardar um pouco
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Verificar saldo (não deve ter mudado)
    const { data: walletAfterPending, error: walletPendingError } = await supabase
      .from('wallets')
      .select('balance')
      .eq('user_id', testUser.id)
      .single();

    if (walletPendingError) {
      console.error('❌ Erro ao verificar saldo após pending:', walletPendingError);
    } else {
      console.log(`💰 Saldo após pending: R$ ${walletAfterPending.balance} (deve ser igual ao inicial)`);
    }

    // Agora atualizar para completed para testar o trigger
    console.log('🔄 Atualizando transação para completed...');
    const { error: updateError } = await supabase
      .from('transactions')
      .update({ status: 'completed' })
      .eq('id', transaction.id);

    if (updateError) {
      console.error('❌ Erro ao atualizar transação:', updateError);
    } else {
      console.log('✅ Transação atualizada para completed');
      
      // Aguardar trigger processar
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Verificar saldo final
      const { data: finalWallet, error: finalError } = await supabase
        .from('wallets')
        .select('balance, updated_at')
        .eq('user_id', testUser.id)
        .single();

      if (finalError) {
        console.error('❌ Erro ao verificar saldo final:', finalError);
      } else {
        const expectedBalance = wallet.balance + testAmount;
        console.log(`💰 Saldo final: R$ ${finalWallet.balance}`);
        console.log(`💰 Saldo esperado: R$ ${expectedBalance}`);
        console.log(`🕐 Wallet atualizada: ${finalWallet.updated_at}`);
        
        if (Math.abs(finalWallet.balance - expectedBalance) < 0.01) {
          console.log('✅ TRIGGER FUNCIONANDO!');
        } else {
          console.log('❌ TRIGGER NÃO FUNCIONANDO!');
          console.log('🔧 Será necessário recriar o trigger...');
        }
      }
    }

    // Limpar transação de teste
    await supabase
      .from('transactions')
      .delete()
      .eq('id', transaction.id);

    // Reverter saldo se necessário
    if (finalWallet && Math.abs(finalWallet.balance - (wallet.balance + testAmount)) < 0.01) {
      await supabase
        .from('wallets')
        .update({ balance: wallet.balance })
        .eq('user_id', testUser.id);
      console.log('🔄 Saldo revertido para o valor original');
    }

  } catch (error) {
    console.error('❌ Erro durante verificação:', error);
  }
}

// Executar verificação
checkTriggers()
  .then(() => {
    console.log('\n✅ Verificação concluída!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

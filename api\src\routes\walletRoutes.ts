import { Router } from 'express';
import {
  getWallet,
  createDeposit,
  createWithdrawal,
  getTransactions,
  getTransactionById
} from '../controllers/walletController';
import { validate, schemas } from '../middleware/validation';
import { authenticate } from '../middleware/auth';
import { transactionRateLimit } from '../middleware/rateLimiting';

const router = Router();

/**
 * @route   GET /api/wallet
 * @desc    Get wallet balance and information
 * @access  Private
 */
router.get('/',
  authenticate,
  getWallet
);

/**
 * @route   GET /api/wallet/balance
 * @desc    Get wallet balance only
 * @access  Private
 */
router.get('/balance',
  authenticate,
  getWallet
);

/**
 * @route   POST /api/wallet/deposit
 * @desc    Create deposit transaction
 * @access  Private
 */
router.post('/deposit',
  authenticate,
  transactionRateLimit,
  validate(schemas.deposit),
  createDeposit
);

/**
 * @route   POST /api/wallet/withdraw
 * @desc    Create withdrawal transaction
 * @access  Private
 */
router.post('/withdraw',
  authenticate,
  transactionRateLimit,
  validate(schemas.withdrawal),
  createWithdrawal
);

/**
 * @route   GET /api/wallet/transactions
 * @desc    Get transaction history
 * @access  Private
 */
router.get('/transactions',
  authenticate,
  validate(schemas.pagination, 'query'),
  getTransactions
);

/**
 * @route   GET /api/wallet/transactions/:id
 * @desc    Get transaction by ID
 * @access  Private
 */
router.get('/transactions/:id',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  getTransactionById
);

/**
 * @route   GET /api/wallet/payment-methods
 * @desc    Get available payment methods
 * @access  Private
 */
router.get('/payment-methods',
  authenticate,
  async (req, res) => {
    // Return available payment methods
    res.json({
      success: true,
      data: {
        payment_methods: [
          {
            type: 'pix',
            name: 'PIX',
            description: 'Transferência instantânea via PIX',
            min_amount: 10.00,
            max_amount: 5000.00,
            processing_time: 'Instantâneo',
            fees: 0,
            available_for: ['deposit', 'withdrawal']
          },
          {
            type: 'credit_card',
            name: 'Cartão de Crédito',
            description: 'Pagamento via cartão de crédito',
            min_amount: 10.00,
            max_amount: 5000.00,
            processing_time: 'Instantâneo',
            fees: 3.99,
            available_for: ['deposit']
          }
        ]
      }
    });
  }
);

/**
 * @route   POST /api/wallet/payment-methods
 * @desc    Add payment method
 * @access  Private
 */
router.post('/payment-methods',
  authenticate,
  async (req, res) => {
    // TODO: Implement add payment method
    res.status(501).json({
      success: false,
      error: 'Add payment method not implemented yet'
    });
  }
);

/**
 * @route   DELETE /api/wallet/payment-methods/:id
 * @desc    Remove payment method
 * @access  Private
 */
router.delete('/payment-methods/:id',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  async (req, res) => {
    // TODO: Implement remove payment method
    res.status(501).json({
      success: false,
      error: 'Remove payment method not implemented yet'
    });
  }
);

/**
 * @route   GET /api/wallet/limits
 * @desc    Get wallet limits and restrictions
 * @access  Private
 */
router.get('/limits',
  authenticate,
  async (req, res) => {
    res.json({
      success: true,
      data: {
        limits: {
          deposit: {
            min_amount: 10.00,
            max_amount: 5000.00,
            daily_limit: 10000.00,
            monthly_limit: 50000.00
          },
          withdrawal: {
            min_amount: 10.00,
            max_amount: 10000.00,
            daily_limit: 20000.00,
            monthly_limit: 100000.00
          }
        }
      }
    });
  }
);

/**
 * @route   GET /api/wallet/summary
 * @desc    Get wallet summary with statistics
 * @access  Private
 */
router.get('/summary',
  authenticate,
  async (req, res) => {
    // TODO: Implement wallet summary with statistics
    res.status(501).json({
      success: false,
      error: 'Wallet summary not implemented yet'
    });
  }
);

export default router;

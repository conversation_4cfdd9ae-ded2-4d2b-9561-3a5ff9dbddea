// Script simples para criar tabelas básicas no Supabase
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createBasicTables() {
  console.log('🚀 Criando tabelas básicas no Supabase...\n');
  
  // 1. Criar extensões
  console.log('📦 Criando extensões...');
  try {
    await supabase.rpc('exec_sql', { 
      sql: 'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";' 
    });
    await supabase.rpc('exec_sql', { 
      sql: 'CREATE EXTENSION IF NOT EXISTS "pgcrypto";' 
    });
    console.log('✅ Extensões criadas');
  } catch (err) {
    console.log('⚠️ Extensões podem já existir');
  }
  
  // 2. Criar tipos ENUM
  console.log('\n🏷️ Criando tipos ENUM...');
  const enums = [
    "CREATE TYPE user_status AS ENUM ('active', 'inactive', 'banned', 'suspended');",
    "CREATE TYPE transaction_type AS ENUM ('deposit', 'withdrawal', 'bet', 'win', 'commission', 'refund');",
    "CREATE TYPE transaction_status AS ENUM ('pending', 'completed', 'failed', 'cancelled');",
    "CREATE TYPE payment_method AS ENUM ('pix', 'credit_card', 'bank_transfer');",
    "CREATE TYPE match_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled', 'disputed');",
    "CREATE TYPE tournament_status AS ENUM ('upcoming', 'registration_open', 'in_progress', 'completed', 'cancelled');",
    "CREATE TYPE game_type AS ENUM ('internal', 'external');",
    "CREATE TYPE room_status AS ENUM ('waiting', 'starting', 'playing', 'finished');",
    "CREATE TYPE friend_request_status AS ENUM ('pending', 'accepted', 'declined');",
    "CREATE TYPE notification_type AS ENUM ('friend_request', 'match_found', 'tournament_update', 'system', 'achievement');"
  ];
  
  for (const enumSql of enums) {
    try {
      await supabase.rpc('exec_sql', { sql: enumSql });
      console.log('✅ Tipo ENUM criado');
    } catch (err) {
      console.log('⚠️ Tipo pode já existir');
    }
  }
  
  // 3. Criar tabela users
  console.log('\n👥 Criando tabela users...');
  const usersTable = `
    CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(50) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        avatar_url TEXT,
        status user_status DEFAULT 'active',
        email_verified BOOLEAN DEFAULT FALSE,
        phone VARCHAR(20),
        birth_date DATE,
        country VARCHAR(2),
        timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',
        language VARCHAR(5) DEFAULT 'pt-BR',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_login_at TIMESTAMP WITH TIME ZONE,
        CONSTRAINT username_length CHECK (LENGTH(username) >= 3),
        CONSTRAINT display_name_length CHECK (LENGTH(display_name) >= 2)
    );
  `;
  
  try {
    await supabase.rpc('exec_sql', { sql: usersTable });
    console.log('✅ Tabela users criada');
  } catch (err) {
    console.error('❌ Erro ao criar tabela users:', err.message);
  }
  
  // 4. Criar tabela wallets
  console.log('\n💰 Criando tabela wallets...');
  const walletsTable = `
    CREATE TABLE IF NOT EXISTS wallets (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        balance DECIMAL(12,2) DEFAULT 0.00 CHECK (balance >= 0),
        frozen_balance DECIMAL(12,2) DEFAULT 0.00 CHECK (frozen_balance >= 0),
        total_deposited DECIMAL(12,2) DEFAULT 0.00,
        total_withdrawn DECIMAL(12,2) DEFAULT 0.00,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;
  
  try {
    await supabase.rpc('exec_sql', { sql: walletsTable });
    console.log('✅ Tabela wallets criada');
  } catch (err) {
    console.error('❌ Erro ao criar tabela wallets:', err.message);
  }
  
  // 5. Criar tabela games
  console.log('\n🎮 Criando tabela games...');
  const gamesTable = `
    CREATE TABLE IF NOT EXISTS games (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        type game_type NOT NULL,
        image_url TEXT,
        icon_url TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        min_players INTEGER DEFAULT 2,
        max_players INTEGER DEFAULT 2,
        default_entry_fee DECIMAL(10,2) DEFAULT 10.00,
        supported_platforms TEXT[],
        game_modes TEXT[],
        settings_schema JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;
  
  try {
    await supabase.rpc('exec_sql', { sql: gamesTable });
    console.log('✅ Tabela games criada');
  } catch (err) {
    console.error('❌ Erro ao criar tabela games:', err.message);
  }
  
  // 6. Inserir alguns dados básicos
  console.log('\n📊 Inserindo dados básicos...');
  
  // Inserir jogos básicos
  const insertGames = `
    INSERT INTO games (name, slug, description, type, is_active, min_players, max_players, default_entry_fee)
    VALUES 
    ('Counter-Strike 2', 'cs2', 'Jogo de tiro tático competitivo', 'external', true, 2, 10, 50.00),
    ('FIFA 24', 'fifa24', 'Simulador de futebol', 'external', true, 2, 2, 30.00),
    ('Call of Duty', 'cod', 'Jogo de tiro em primeira pessoa', 'external', true, 2, 4, 40.00),
    ('Jogo da Reação', 'reaction-game', 'Teste de reflexos', 'internal', true, 2, 2, 10.00),
    ('Flap Rocket', 'flap-rocket', 'Jogo de habilidade', 'internal', true, 2, 2, 15.00)
    ON CONFLICT (slug) DO NOTHING;
  `;
  
  try {
    await supabase.rpc('exec_sql', { sql: insertGames });
    console.log('✅ Jogos básicos inseridos');
  } catch (err) {
    console.error('❌ Erro ao inserir jogos:', err.message);
  }
  
  console.log('\n🎉 CONFIGURAÇÃO BÁSICA CONCLUÍDA!');
  console.log('✅ Tabelas principais criadas');
  console.log('✅ Dados básicos inseridos');
  console.log('✅ Banco pronto para testes');
}

// Executar
createBasicTables()
  .then(() => {
    console.log('\n✅ Sucesso! Banco configurado.');
    process.exit(0);
  })
  .catch(err => {
    console.error('\n❌ Erro:', err.message);
    process.exit(1);
  });

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from the correct path
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Validate required environment variables
const requiredEnvVars = [
  'NODE_ENV',
  'PORT',
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'JWT_SECRET',
  'OPENAI_API_KEY'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

export const config = {
  // Server configuration
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT || '3001', 10),
  HOST: process.env.HOST || 'localhost',

  // Database configuration
  SUPABASE_URL: process.env.SUPABASE_URL!,
  SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY!,
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY!,

  // Authentication
  JWT_SECRET: process.env.JWT_SECRET!,
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
  REFRESH_TOKEN_EXPIRES_IN: process.env.REFRESH_TOKEN_EXPIRES_IN || '30d',

  // OpenAI configuration
  OPENAI_API_KEY: process.env.OPENAI_API_KEY!,
  OPENAI_ORGANIZATION_ID: process.env.OPENAI_ORGANIZATION_ID,
  OPENAI_MODEL: process.env.OPENAI_MODEL || 'gpt-4-vision-preview',

  // Payment gateway configuration
  PAYMENT_GATEWAY_API_KEY: process.env.PAYMENT_GATEWAY_API_KEY,
  PAYMENT_GATEWAY_SECRET: process.env.PAYMENT_GATEWAY_SECRET,
  PAYMENT_GATEWAY_WEBHOOK_SECRET: process.env.PAYMENT_GATEWAY_WEBHOOK_SECRET,

  // Redis configuration
  REDIS_URL: process.env.REDIS_URL || 'redis://localhost:6379',
  REDIS_PASSWORD: process.env.REDIS_PASSWORD,

  // Email configuration
  SMTP_HOST: process.env.SMTP_HOST,
  SMTP_PORT: parseInt(process.env.SMTP_PORT || '587', 10),
  SMTP_USER: process.env.SMTP_USER,
  SMTP_PASS: process.env.SMTP_PASS,
  EMAIL_FROM: process.env.EMAIL_FROM || '<EMAIL>',

  // File upload configuration
  MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10), // 10MB
  UPLOAD_PATH: process.env.UPLOAD_PATH || './uploads',

  // Rate limiting
  RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '1000', 10),
  AUTH_RATE_LIMIT_MAX: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '1000', 10),
  AUTH_RATE_LIMIT_WINDOW_MS: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS || '900000', 10),

  // CORS configuration
  CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:3000',
  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:5173',

  // Payment Configuration
  PAYMENT_WEBHOOK_SECRET: process.env.PAYMENT_WEBHOOK_SECRET || 'default-webhook-secret',
  TOURNAMENT_WEBHOOK_SECRET: process.env.TOURNAMENT_WEBHOOK_SECRET || 'default-tournament-secret',

  // Logging
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  LOG_FILE: process.env.LOG_FILE || 'logs/app.log',

  // Game configuration
  GAME_ROOM_TIMEOUT: parseInt(process.env.GAME_ROOM_TIMEOUT || '1800', 10), // 30 minutes
  MATCHMAKING_TIMEOUT: parseInt(process.env.MATCHMAKING_TIMEOUT || '300', 10), // 5 minutes
  MATCH_AUTO_CONFIRM_TIME: parseInt(process.env.MATCH_AUTO_CONFIRM_TIME || '300', 10), // 5 minutes

  // Affiliate program
  DEFAULT_COMMISSION_RATE: parseFloat(process.env.DEFAULT_COMMISSION_RATE || '0.15'), // 15%
  MIN_PAYOUT_AMOUNT: parseFloat(process.env.MIN_PAYOUT_AMOUNT || '10.00'), // R$ 10

  // Wallet limits
  MIN_DEPOSIT_AMOUNT: parseFloat(process.env.MIN_DEPOSIT_AMOUNT || '10.00'), // R$ 10
  MAX_DEPOSIT_AMOUNT: parseFloat(process.env.MAX_DEPOSIT_AMOUNT || '5000.00'), // R$ 5000
  MIN_WITHDRAWAL_AMOUNT: parseFloat(process.env.MIN_WITHDRAWAL_AMOUNT || '10.00'), // R$ 10
  MAX_WITHDRAWAL_AMOUNT: parseFloat(process.env.MAX_WITHDRAWAL_AMOUNT || '10000.00'), // R$ 10000

  // Security
  BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
  SESSION_SECRET: process.env.SESSION_SECRET || 'your-session-secret',

  // External APIs
  TWITCH_CLIENT_ID: process.env.TWITCH_CLIENT_ID,
  TWITCH_CLIENT_SECRET: process.env.TWITCH_CLIENT_SECRET,
  DISCORD_CLIENT_ID: process.env.DISCORD_CLIENT_ID,
  DISCORD_CLIENT_SECRET: process.env.DISCORD_CLIENT_SECRET,

  // Monitoring
  SENTRY_DSN: process.env.SENTRY_DSN,
  ANALYTICS_API_KEY: process.env.ANALYTICS_API_KEY,

  // Feature flags
  ENABLE_TOURNAMENTS: process.env.ENABLE_TOURNAMENTS !== 'false',
  ENABLE_AFFILIATE_PROGRAM: process.env.ENABLE_AFFILIATE_PROGRAM !== 'false',
  ENABLE_CLUBS: process.env.ENABLE_CLUBS !== 'false',
  ENABLE_STREAMS: process.env.ENABLE_STREAMS !== 'false',
  ENABLE_CHAT: process.env.ENABLE_CHAT !== 'false',

  // Development
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test'
};

// Validate numeric configurations
const numericValidations = [
  { key: 'PORT', value: config.PORT, min: 1, max: 65535 },
  { key: 'BCRYPT_ROUNDS', value: config.BCRYPT_ROUNDS, min: 4, max: 20 },
  { key: 'RATE_LIMIT_MAX_REQUESTS', value: config.RATE_LIMIT_MAX_REQUESTS, min: 1, max: 10000 },
  { key: 'DEFAULT_COMMISSION_RATE', value: config.DEFAULT_COMMISSION_RATE, min: 0, max: 1 }
];

numericValidations.forEach(({ key, value, min, max }) => {
  if (value < min || value > max) {
    throw new Error(`${key} must be between ${min} and ${max}, got ${value}`);
  }
});

// Log configuration in development
if (config.isDevelopment) {
  console.log('Configuration loaded:', {
    NODE_ENV: config.NODE_ENV,
    PORT: config.PORT,
    SUPABASE_URL: config.SUPABASE_URL,
    CORS_ORIGIN: config.CORS_ORIGIN,
    LOG_LEVEL: config.LOG_LEVEL
  });
}

export default config;

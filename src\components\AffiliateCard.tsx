import React, { useState } from 'react';
import { <PERSON>, Co<PERSON>, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface AffiliateCardProps {
  affiliateLink: string;
  invitedFriends: number;
  totalEarnings: number;
}

export default function AffiliateCard({ affiliateLink, invitedFriends, totalEarnings }: AffiliateCardProps) {
  const [linkCopied, setLinkCopied] = useState(false);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(affiliateLink);
    setLinkCopied(true);
    setTimeout(() => setLinkCopied(false), 2000);
  };

  return (
    <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-xl p-4 sm:p-6 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mt-10 -mr-10 blur-xl"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -mb-8 -ml-8 blur-xl"></div>

      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center gap-2 mb-2">
          <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          <h2 className="text-white font-bold text-base sm:text-lg">Programa de Afiliados</h2>
        </div>
        <p className="text-white/80 text-sm sm:text-base mb-3 sm:mb-4">
          Convide amigos e ganhe até 15% das taxas deles
        </p>

        {/* Affiliate Link */}
        <div className="bg-white/20 backdrop-blur-sm rounded-lg p-2 sm:p-3 mb-3 sm:mb-4 flex items-center justify-between">
          <span className="text-white text-xs sm:text-sm truncate mr-2">{affiliateLink}</span>
          <button
            onClick={handleCopyLink}
            className={`${
              linkCopied ? 'bg-green-500/50' : 'bg-white/30 hover:bg-white/40'
            } transition-colors p-1.5 rounded-md flex items-center justify-center`}
            title={linkCopied ? 'Copiado!' : 'Copiar link'}
          >
            <Copy className="w-4 h-4 text-white" />
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 text-center sm:text-left">
          <div>
            <p className="text-white/70 text-xs">Amigos convidados</p>
            <p className="text-white font-bold text-lg sm:text-xl">{invitedFriends}</p>
          </div>
          <div>
            <p className="text-white/70 text-xs">Ganhos totais</p>
            <p className="text-white font-bold text-lg sm:text-xl">
              R$ {totalEarnings.toFixed(2).replace('.', ',')}
            </p>
          </div>
        </div>

        {/* Action Button */}
        <Link
          to="/affiliate-program"
          className="bg-white/20 hover:bg-white/30 transition-colors text-white px-3 py-2 rounded-lg flex items-center justify-center gap-1 text-xs sm:text-sm font-medium mt-4 w-full"
        >
          Ver mais
          <ChevronRight className="w-4 h-4" />
        </Link>
      </div>
    </div>
  );
}

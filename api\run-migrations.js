// Script para executar migrations do Supabase
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

console.log('🚀 Executando Migrations do Supabase...\n');

// Configurações do Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Erro: Variáveis de ambiente do Supabase não estão configuradas!');
  process.exit(1);
}

// Criar cliente Supabase com privilégios admin
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Lista de migrations na ordem correta
const migrations = [
  '001_initial_schema.sql',
  '002_matches_and_rooms.sql',
  '003_tournaments.sql',
  '004_social_features.sql',
  '005_affiliate_program.sql',
  '006_notifications_and_system.sql',
  '007_rls_policies.sql',
  '008_functions_and_triggers.sql',
  '009_seed_data.sql'
];

async function executeMigration(migrationFile) {
  try {
    console.log(`📄 Executando: ${migrationFile}...`);
    
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', migrationFile);
    
    if (!fs.existsSync(migrationPath)) {
      console.log(`⚠️ Arquivo não encontrado: ${migrationFile} - Pulando...`);
      return true;
    }
    
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    // Dividir o SQL em comandos individuais (separados por ';')
    const commands = sql
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));
    
    console.log(`   📊 ${commands.length} comandos SQL encontrados`);
    
    // Executar cada comando
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      
      if (command.length < 10) continue; // Pular comandos muito pequenos
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: command });
        
        if (error) {
          // Alguns erros são esperados (como extensões já existentes)
          if (error.message.includes('already exists') || 
              error.message.includes('does not exist') ||
              error.message.includes('permission denied')) {
            console.log(`   ⚠️ Aviso (comando ${i + 1}): ${error.message}`);
          } else {
            console.error(`   ❌ Erro (comando ${i + 1}): ${error.message}`);
            return false;
          }
        } else {
          console.log(`   ✅ Comando ${i + 1} executado com sucesso`);
        }
      } catch (err) {
        console.error(`   ❌ Erro ao executar comando ${i + 1}:`, err.message);
        return false;
      }
    }
    
    console.log(`✅ Migration ${migrationFile} concluída com sucesso!\n`);
    return true;
    
  } catch (err) {
    console.error(`❌ Erro ao processar ${migrationFile}:`, err.message);
    return false;
  }
}

async function runAllMigrations() {
  console.log('🔍 Verificando conexão com Supabase...');
  
  try {
    // Teste de conexão básico
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1);
    
    if (error) {
      console.error('❌ Erro de conexão:', error.message);
      return false;
    }
    
    console.log('✅ Conexão estabelecida com sucesso!\n');
  } catch (err) {
    console.error('❌ Erro de conexão:', err.message);
    return false;
  }
  
  console.log('📋 Executando migrations...\n');
  
  let successCount = 0;
  let failCount = 0;
  
  for (const migration of migrations) {
    const success = await executeMigration(migration);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  console.log('📊 RESUMO DA EXECUÇÃO:');
  console.log(`✅ Migrations executadas com sucesso: ${successCount}`);
  console.log(`❌ Migrations com erro: ${failCount}`);
  console.log(`📄 Total de migrations: ${migrations.length}\n`);
  
  if (failCount === 0) {
    console.log('🎉 TODAS AS MIGRATIONS FORAM EXECUTADAS COM SUCESSO!');
    console.log('✅ Banco de dados configurado e pronto para uso');
    return true;
  } else {
    console.log('⚠️ ALGUMAS MIGRATIONS FALHARAM');
    console.log('🔧 Verifique os erros acima e tente novamente');
    return false;
  }
}

// Executar migrations
runAllMigrations()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(err => {
    console.error('❌ Erro fatal:', err.message);
    process.exit(1);
  });

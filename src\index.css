@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom breakpoint for extra small devices */
@layer utilities {
  @variants responsive {
    .xs\:inline {
      display: inline;
    }
    .xs\:hidden {
      display: none;
    }
    .xs\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  10% {
    opacity: 1;
    transform: translateY(0);
  }
  80% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-40px);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Responsive font sizes */
html {
  font-size: 14px;
}

@media (min-width: 640px) {
  html {
    font-size: 16px;
  }
}

/* Improved touch targets for mobile */
@media (max-width: 640px) {
  button,
  [role="button"],
  input,
  select,
  a {
    min-height: 44px;
  }
}

/* Prevent content from being hidden under fixed headers on anchor links */
html {
  scroll-padding-top: 80px;
}
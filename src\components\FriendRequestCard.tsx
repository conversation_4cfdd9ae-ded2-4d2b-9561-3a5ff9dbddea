import React from 'react';
import { Check, X } from 'lucide-react';

interface FriendRequestCardProps {
  id: string;
  name: string;
  avatar: string;
  mutualFriends: number;
  onAccept: (id: string) => void;
  onReject: (id: string) => void;
}

export default function FriendRequestCard({ 
  id,
  name, 
  avatar, 
  mutualFriends,
  onAccept,
  onReject
}: FriendRequestCardProps) {
  return (
    <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 hover:bg-gray-700/50 transition-all duration-300">
      <div className="flex items-center gap-3 mb-3">
        <img
          src={avatar}
          alt={name}
          className="w-12 h-12 rounded-full object-cover ring-2 ring-white/10"
        />
        <div>
          <h3 className="text-white font-semibold">{name}</h3>
          <p className="text-sm text-gray-400">{mutualFriends} amigos em comum</p>
        </div>
      </div>
      <div className="flex gap-2">
        <button 
          onClick={() => onAccept(id)}
          className="flex-1 bg-gradient-to-r from-rose-400 to-pink-400 text-white font-semibold rounded-lg py-2 px-4 flex items-center justify-center gap-2 hover:opacity-90 transition-opacity"
        >
          <Check className="w-4 h-4" />
          Aceitar
        </button>
        <button 
          onClick={() => onReject(id)}
          className="flex-1 bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white font-semibold rounded-lg py-2 px-4 flex items-center justify-center gap-2 hover:bg-gray-600/50 transition-colors"
        >
          <X className="w-4 h-4" />
          Recusar
        </button>
      </div>
    </div>
  );
}
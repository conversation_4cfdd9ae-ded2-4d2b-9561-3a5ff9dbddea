import React, { useState, useEffect } from 'react';
import { X, Key, Save, AlertTriangle } from 'lucide-react';
import { OPENAI_API_KEY } from '../config/api';

interface ApiKeyConfigModalProps {
  onClose: () => void;
  onSave: (apiKey: string) => void;
}

export default function ApiKeyConfigModal({ onClose, onSave }: ApiKeyConfigModalProps) {
  const [apiKey, setApiKey] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Inicializar com a chave atual (se não for o placeholder)
    if (OPENAI_API_KEY && OPENAI_API_KEY !== 'sua_chave_da_api_aqui' && !OPENAI_API_KEY.startsWith('sk-...')) {
      setApiKey(OPENAI_API_KEY);
    }
  }, []);

  const handleSave = () => {
    // Validar a chave da API
    if (!apiKey) {
      setError('Por favor, insira uma chave de API válida.');
      return;
    }

    if (!apiKey.startsWith('sk-')) {
      setError('A chave da API OpenAI deve começar com "sk-".');
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      // Em uma implementação real, você salvaria isso em um local seguro
      // Aqui estamos apenas atualizando o estado local
      onSave(apiKey);
      onClose();
    } catch (err) {
      setError('Ocorreu um erro ao salvar a chave da API.');
      console.error('Error saving API key:', err);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800/90 backdrop-blur-sm border border-indigo-500/20 rounded-xl w-full max-w-md shadow-xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Key className="w-5 h-5 text-white" />
            <h3 className="text-white font-bold">Configurar Chave da API OpenAI</h3>
          </div>
          <button 
            onClick={onClose}
            className="text-white/80 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-5 space-y-5">
          <div className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4">
            <p className="text-gray-300 text-sm">
              Para usar a análise automática de resultados, você precisa configurar uma chave da API OpenAI.
              Você pode obter uma chave em <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-indigo-400 hover:underline">platform.openai.com/api-keys</a>.
            </p>
          </div>
          
          <div className="space-y-2">
            <label htmlFor="api-key" className="text-white font-medium block">
              Chave da API OpenAI
            </label>
            <input
              id="api-key"
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="sk-..."
              className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
            <p className="text-gray-400 text-xs">
              Sua chave é armazenada apenas localmente no seu navegador e nunca é enviada para nossos servidores.
            </p>
          </div>
          
          <div className="bg-yellow-500/10 rounded-lg p-4 flex items-start gap-2">
            <AlertTriangle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
            <p className="text-yellow-200 text-sm">
              O uso da API OpenAI pode gerar custos. Consulte a <a href="https://openai.com/pricing" target="_blank" rel="noopener noreferrer" className="text-yellow-400 hover:underline">página de preços</a> para mais informações.
            </p>
          </div>
          
          {/* Error Message */}
          {error && (
            <div className="bg-red-500/20 text-red-400 p-3 rounded-lg flex items-start gap-2">
              <AlertTriangle className="w-5 h-5 flex-shrink-0 mt-0.5" />
              <p>{error}</p>
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t border-gray-700 flex justify-end gap-3">
          <button
            onClick={onClose}
            className="bg-gray-700 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-600"
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-2 px-4 rounded-lg hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isSaving ? 'Salvando...' : (
              <>
                <Save className="w-4 h-4" />
                Salvar Chave
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

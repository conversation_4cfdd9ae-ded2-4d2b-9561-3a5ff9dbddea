-- Migration 009: Seed Data
-- Created: 2024-01-01
-- Description: Inserts initial data for games, achievements, tiers, and system settings

-- Insert default games (skip if already exists)
INSERT INTO games (id, name, slug, description, type, image_url, icon_url, min_players, max_players, default_entry_fee, supported_platforms, game_modes, settings_schema) VALUES
-- Internal Games
('550e8400-e29b-41d4-a716-446655440001', 'FlapRocket', 'flaprocket', 'Controle seu foguete e desvie dos obstáculos', 'internal', 'https://images.unsplash.com/photo-1516849841032-87cbac4d88f7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80', 'https://images.unsplash.com/photo-1516849841032-87cbac4d88f7?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80', 2, 8, 5.00, ARRAY['web', 'mobile'], ARRAY['1v1', 'tournament'], '{"difficulty": "normal", "obstacles": "standard"}'),

('550e8400-e29b-41d4-a716-446655440002', 'Reaction Game', 'reaction', 'Teste seus reflexos e velocidade de reação', 'internal', 'https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80', 'https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80', 2, 4, 3.00, ARRAY['web', 'mobile'], ARRAY['1v1', 'tournament'], '{"rounds": 5, "max_time": 3000}'),

('550e8400-e29b-41d4-a716-446655440003', 'Color Match', 'colormatch', 'Combine as cores o mais rápido possível', 'internal', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80', 2, 6, 4.00, ARRAY['web', 'mobile'], ARRAY['1v1', 'tournament'], '{"colors": 6, "time_limit": 60}'),

('550e8400-e29b-41d4-a716-446655440004', 'Word Scramble', 'wordscramble', 'Descubra as palavras embaralhadas', 'internal', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80', 2, 4, 6.00, ARRAY['web', 'mobile'], ARRAY['1v1', 'tournament'], '{"difficulty": "medium", "language": "pt-BR"}'),

('550e8400-e29b-41d4-a716-446655440005', 'Stickman Archer', 'stickmanarcher', 'Mire e atire com precisão', 'internal', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80', 2, 2, 8.00, ARRAY['web', 'mobile'], ARRAY['1v1'], '{"rounds": 10, "wind": true}'),

-- External Games
('550e8400-e29b-41d4-a716-446655440006', 'Counter-Strike 2', 'cs2', 'O clássico FPS competitivo', 'external', 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80', 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80', 2, 10, 25.00, ARRAY['pc'], ARRAY['1v1', 'tournament'], '{"map": "dust2", "rounds": 15, "overtime": true}'),

('550e8400-e29b-41d4-a716-446655440007', 'FIFA 24', 'fifa24', 'O melhor jogo de futebol do mundo', 'external', 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80', 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80', 2, 2, 15.00, ARRAY['pc', 'console'], ARRAY['1v1', 'tournament'], '{"match_duration": 6, "difficulty": "world_class"}'),

('550e8400-e29b-41d4-a716-446655440008', 'Call of Duty Mobile', 'codmobile', 'Battle royale e multiplayer mobile', 'external', 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80', 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80', 2, 4, 12.00, ARRAY['mobile'], ARRAY['1v1', 'tournament'], '{"mode": "multiplayer", "map": "nuketown"}'),

('550e8400-e29b-41d4-a716-446655440009', 'Mobile Legends', 'mobilelegends', 'MOBA mobile mais popular', 'external', 'https://images.unsplash.com/photo-1556438064-2d7646166914?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80', 'https://images.unsplash.com/photo-1556438064-2d7646166914?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80', 2, 10, 20.00, ARRAY['mobile'], ARRAY['1v1', 'tournament'], '{"mode": "classic", "draft": false}')
ON CONFLICT (slug) DO NOTHING;

-- Insert affiliate tiers (skip if already exists)
INSERT INTO affiliate_tiers (tier_level, tier_name, min_referrals, min_earnings, commission_rate, benefits) VALUES
(1, 'Bronze', 0, 0.00, 0.15, '{"bonus_rate": 0, "priority_support": false, "exclusive_tournaments": false}'),
(2, 'Silver', 10, 100.00, 0.18, '{"bonus_rate": 0.02, "priority_support": false, "exclusive_tournaments": false}'),
(3, 'Gold', 25, 500.00, 0.20, '{"bonus_rate": 0.03, "priority_support": true, "exclusive_tournaments": false}'),
(4, 'Platinum', 50, 1500.00, 0.22, '{"bonus_rate": 0.04, "priority_support": true, "exclusive_tournaments": true}'),
(5, 'Diamond', 100, 5000.00, 0.25, '{"bonus_rate": 0.05, "priority_support": true, "exclusive_tournaments": true, "custom_rates": true}')
ON CONFLICT (tier_level) DO NOTHING;

-- Insert achievements (skip if already exists)
-- Note: achievements table has no unique constraint on name, so we'll use a different approach
DO $$
BEGIN
    -- Insert achievements only if they don't exist
    INSERT INTO achievements (name, description, icon_url, badge_url, category, rarity, points, requirements)
    SELECT name, description, icon_url, badge_url, category, rarity, points, requirements::jsonb FROM (VALUES
        -- Match achievements
        ('First Victory', 'Win your first match', '/icons/first-victory.png', '/badges/first-victory.png', 'matches', 'common', 10, '{"wins": 1}'),
        ('Winning Streak', 'Win 5 matches in a row', '/icons/winning-streak.png', '/badges/winning-streak.png', 'matches', 'rare', 50, '{"streak": 5}'),
        ('Unstoppable', 'Win 10 matches in a row', '/icons/unstoppable.png', '/badges/unstoppable.png', 'matches', 'epic', 100, '{"streak": 10}'),
        ('Legendary Streak', 'Win 20 matches in a row', '/icons/legendary-streak.png', '/badges/legendary-streak.png', 'matches', 'legendary', 250, '{"streak": 20}'),
        ('Century Club', 'Win 100 matches', '/icons/century-club.png', '/badges/century-club.png', 'matches', 'epic', 200, '{"total_wins": 100}'),

        -- Tournament achievements
        ('Tournament Debut', 'Participate in your first tournament', '/icons/tournament-debut.png', '/badges/tournament-debut.png', 'tournaments', 'common', 25, '{"tournaments_joined": 1}'),
        ('Tournament Winner', 'Win your first tournament', '/icons/tournament-winner.png', '/badges/tournament-winner.png', 'tournaments', 'rare', 100, '{"tournaments_won": 1}'),
        ('Tournament Master', 'Win 5 tournaments', '/icons/tournament-master.png', '/badges/tournament-master.png', 'tournaments', 'epic', 300, '{"tournaments_won": 5}'),

        -- Social achievements
        ('Social Butterfly', 'Add 10 friends', '/icons/social-butterfly.png', '/badges/social-butterfly.png', 'social', 'common', 20, '{"friends": 10}'),
        ('Club Founder', 'Create a club', '/icons/club-founder.png', '/badges/club-founder.png', 'social', 'rare', 75, '{"clubs_created": 1}'),
        ('Popular Player', 'Have 50 friends', '/icons/popular-player.png', '/badges/popular-player.png', 'social', 'epic', 150, '{"friends": 50}'),

        -- Milestone achievements
        ('High Roller', 'Make a deposit of R$ 100 or more', '/icons/high-roller.png', '/badges/high-roller.png', 'milestones', 'rare', 50, '{"single_deposit": 100}'),
        ('Big Spender', 'Spend R$ 1000 in total', '/icons/big-spender.png', '/badges/big-spender.png', 'milestones', 'epic', 100, '{"total_spent": 1000}'),
        ('Veteran Player', 'Play for 30 days', '/icons/veteran-player.png', '/badges/veteran-player.png', 'milestones', 'rare', 75, '{"days_active": 30}'),
        ('Loyal Player', 'Play for 365 days', '/icons/loyal-player.png', '/badges/loyal-player.png', 'milestones', 'legendary', 500, '{"days_active": 365}')
    ) AS new_achievements(name, description, icon_url, badge_url, category, rarity, points, requirements)
    WHERE NOT EXISTS (
        SELECT 1 FROM achievements WHERE achievements.name = new_achievements.name
    );
END $$;

-- Insert system settings (skip if already exists)
INSERT INTO system_settings (key, value, description, is_public) VALUES
('maintenance_mode', '"false"', 'Enable/disable maintenance mode', true),
('registration_enabled', '"true"', 'Enable/disable new user registration', true),
('min_deposit_amount', '"10.00"', 'Minimum deposit amount in BRL', true),
('max_deposit_amount', '"5000.00"', 'Maximum deposit amount in BRL', true),
('min_withdrawal_amount', '"10.00"', 'Minimum withdrawal amount in BRL', true),
('max_withdrawal_amount', '"10000.00"', 'Maximum withdrawal amount in BRL', true),
('default_commission_rate', '"0.15"', 'Default affiliate commission rate (15%)', false),
('max_friends_count', '"500"', 'Maximum number of friends per user', true),
('max_club_members', '"1000"', 'Maximum members per club', true),
('tournament_registration_fee_percentage', '"0.05"', 'Platform fee percentage for tournaments', false),
('match_auto_confirm_time', '"300"', 'Auto-confirm match results after X seconds', false),
('chat_message_max_length', '"1000"', 'Maximum length for chat messages', true),
('room_inactivity_timeout', '"1800"', 'Room timeout in seconds (30 minutes)', true),
('matchmaking_timeout', '"300"', 'Matchmaking timeout in seconds', true),
('season_duration_days', '"90"', 'Default season duration in days', true),
('ranking_update_interval', '"3600"', 'Ranking update interval in seconds', false)
ON CONFLICT (key) DO NOTHING;

-- Insert current season (skip if already exists)
INSERT INTO seasons (name, season_number, start_date, end_date, is_active, rewards) VALUES
('Season 1 - Launch', 1, '2024-01-01', '2024-03-31', true, '{
    "1": {"cash": 1000, "badge": "season_1_champion", "title": "Season 1 Champion"},
    "2": {"cash": 500, "badge": "season_1_runner_up", "title": "Season 1 Runner-up"},
    "3": {"cash": 250, "badge": "season_1_third", "title": "Season 1 Third Place"},
    "top_10": {"badge": "season_1_top_10", "title": "Season 1 Elite"},
    "top_100": {"badge": "season_1_top_100", "title": "Season 1 Competitor"}
}')
ON CONFLICT (season_number) DO NOTHING;

-- Insert sample affiliate campaign (skip if already exists)
INSERT INTO affiliate_campaigns (name, description, campaign_code, bonus_commission_rate, start_date, end_date, is_active) VALUES
('Launch Campaign', 'Special campaign for platform launch with bonus commissions', 'LAUNCH2024', 0.05, '2024-01-01 00:00:00+00', '2024-03-31 23:59:59+00', true),
('Summer Boost', 'Summer campaign with increased referral rewards', 'SUMMER2024', 0.03, '2024-06-01 00:00:00+00', '2024-08-31 23:59:59+00', false)
ON CONFLICT (campaign_code) DO NOTHING;

-- Create indexes for better performance on seed data
CREATE INDEX IF NOT EXISTS idx_games_name ON games(name);
CREATE INDEX IF NOT EXISTS idx_achievements_category_rarity ON achievements(category, rarity);
CREATE INDEX IF NOT EXISTS idx_affiliate_tiers_tier_level ON affiliate_tiers(tier_level);
CREATE INDEX IF NOT EXISTS idx_seasons_is_active ON seasons(is_active);

-- Update game statistics for today (sample data) - skip if already exists
INSERT INTO game_statistics (game_id, date, total_matches, total_players, total_prize_pool, average_match_duration, peak_concurrent_players)
SELECT
    id,
    CURRENT_DATE,
    FLOOR(RANDOM() * 100) + 10, -- 10-110 matches
    FLOOR(RANDOM() * 500) + 50, -- 50-550 players
    (FLOOR(RANDOM() * 10000) + 1000)::DECIMAL(12,2), -- R$ 1000-11000 prize pool
    FLOOR(RANDOM() * 30) + 15, -- 15-45 minutes average
    FLOOR(RANDOM() * 200) + 20 -- 20-220 peak concurrent
FROM games
ON CONFLICT (game_id, date) DO NOTHING;

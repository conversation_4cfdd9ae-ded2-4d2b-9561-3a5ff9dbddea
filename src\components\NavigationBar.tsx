import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { Radio, Gamepad, Users2, Medal, Trophy } from 'lucide-react';

export default function NavigationBar() {
  const location = useLocation();
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-800">
      <div className="flex justify-around py-3">
        <NavLink
          to="/streams"
          className={({ isActive }) => `flex flex-col items-center gap-1 ${isActive ? 'text-indigo-400' : 'text-gray-400'}`}
        >
          <Radio className="w-6 h-6" />
          <span className="text-xs">Streams</span>
        </NavLink>
        <NavLink
          to="/tournaments"
          className={({ isActive }) => `flex flex-col items-center gap-1 ${isActive ? 'text-indigo-400' : 'text-gray-400'}`}
        >
          <Trophy className="w-6 h-6" />
          <span className="text-xs">Torneios</span>
        </NavLink>
        <NavLink
          to="/"
          className={({ isActive }) => `flex flex-col items-center gap-1 ${isActive || location.pathname.includes('/external-games') ? 'text-indigo-400' : 'text-gray-400'}`}
        >
          <Gamepad className="w-6 h-6" />
          <span className="text-xs">Jogos</span>
        </NavLink>
        <NavLink
          to="/ranking"
          className={({ isActive }) => `flex flex-col items-center gap-1 ${isActive ? 'text-indigo-400' : 'text-gray-400'}`}
        >
          <Medal className="w-6 h-6" />
          <span className="text-xs">Ranking</span>
        </NavLink>
        <NavLink
          to="/social"
          className={({ isActive }) => `flex flex-col items-center gap-1 ${isActive ? 'text-indigo-400' : 'text-gray-400'}`}
        >
          <Users2 className="w-6 h-6" />
          <span className="text-xs">Social</span>
        </NavLink>
      </div>
    </div>
  );
}
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getUserData, saveUserName, saveUserAvatar, clearUserData, isUserLoggedIn } from '../services/userService';
import { authService, AuthResponse } from '../services/authService';
import { apiService } from '../services/apiService';

interface UserContextType {
  user: {
    name: string | null;
    avatar: string;
    id: string;
    email?: string;
    username?: string;
    display_name?: string;
  };
  isLoggedIn: boolean;
  isAuthenticated: boolean;
  setUserName: (name: string) => void;
  setUserAvatar: (avatar: string) => void;
  setUserFromAuth: (authResponse: AuthResponse) => void;
  logout: () => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState(() => {
    // Primeiro tenta obter dados da autenticação real
    const authUser = authService.getCurrentUser();
    if (authUser && authService.isAuthenticated()) {
      return {
        name: authUser.display_name || authUser.username,
        avatar: authUser.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        id: authUser.id,
        email: authUser.email,
        username: authUser.username,
        display_name: authUser.display_name
      };
    }
    // Não usar dados locais mockados - forçar autenticação real
    return {
      name: null,
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      id: '',
      email: undefined,
      username: undefined,
      display_name: undefined
    };
  });

  const [isLoggedIn, setIsLoggedIn] = useState(() => authService.isAuthenticated());
  const [isAuthenticated, setIsAuthenticated] = useState(() => authService.isAuthenticated());

  // Atualiza o estado quando o usuário é alterado (compatibilidade com sistema antigo - apenas para convidados)
  const setUserName = (name: string) => {
    // Só permite login como convidado se não estiver autenticado
    if (!isAuthenticated) {
      saveUserName(name);
      setUser(prev => ({ ...prev, name }));
      setIsLoggedIn(true);
    }
  };

  const setUserAvatar = (avatar: string) => {
    saveUserAvatar(avatar);
    setUser(prev => ({ ...prev, avatar }));
  };

  // Nova função para definir usuário a partir da autenticação real
  const setUserFromAuth = (authResponse: AuthResponse) => {
    const userData = {
      name: authResponse.user.display_name || authResponse.user.username,
      avatar: authResponse.user.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      id: authResponse.user.id,
      email: authResponse.user.email,
      username: authResponse.user.username,
      display_name: authResponse.user.display_name
    };

    setUser(userData);
    setIsLoggedIn(true);
    setIsAuthenticated(true);
  };

  const logout = () => {
    // Logout da autenticação real
    authService.logout();

    // Limpa dados locais (compatibilidade)
    clearUserData();

    // Reseta estado
    setUser(getUserData());
    setIsLoggedIn(false);
    setIsAuthenticated(false);
  };

  // Sincroniza o estado quando a aplicação é carregada
  useEffect(() => {
    const checkAuth = async () => {
      // Limpa dados mockados antigos se existirem
      const hasOldMockData = localStorage.getItem('playstrike_user_name') && !localStorage.getItem('auth_token');
      if (hasOldMockData) {
        clearUserData(); // Limpa dados mockados
      }

      const isAuth = authService.isAuthenticated();
      setIsAuthenticated(isAuth);

      if (isAuth) {
        try {
          // Tenta obter perfil atualizado
          const profile = await authService.getProfile();
          const userData = {
            name: profile.display_name || profile.username,
            avatar: profile.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            id: profile.id,
            email: profile.email,
            username: profile.username,
            display_name: profile.display_name
          };
          setUser(userData);
          setIsLoggedIn(true);
        } catch (error) {
          console.error('Erro ao obter perfil:', error);
          // Se falhar, faz logout
          logout();
        }
      } else {
        // Não usar dados locais mockados - usuário deve fazer login real
        setUser({
          name: null,
          avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          id: '',
          email: undefined,
          username: undefined,
          display_name: undefined
        });
        setIsLoggedIn(false);
      }
    };

    checkAuth();
  }, []);

  return (
    <UserContext.Provider value={{
      user,
      isLoggedIn,
      isAuthenticated,
      setUserName,
      setUserAvatar,
      setUserFromAuth,
      logout
    }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

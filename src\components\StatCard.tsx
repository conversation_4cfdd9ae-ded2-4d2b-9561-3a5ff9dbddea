import React from 'react';
import { LucideIcon } from 'lucide-react';

interface StatCardProps {
  icon: LucideIcon;
  label: string;
  value: string | number;
}

export default function StatCard({ icon: Icon, label, value }: StatCardProps) {
  return (
    <div className="bg-gray-800 rounded-xl p-4 flex items-center gap-3">
      <div className="bg-gray-700 p-2 rounded-lg">
        <Icon className="w-6 h-6 text-yellow-400" />
      </div>
      <div>
        <p className="text-gray-400 text-sm">{label}</p>
        <p className="text-white font-bold">{value}</p>
      </div>
    </div>
  );
}
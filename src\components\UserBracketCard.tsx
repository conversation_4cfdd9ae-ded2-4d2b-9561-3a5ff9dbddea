import React from 'react';
import { Tournament } from '../types/tournament';
import { Users, ChevronRight, Trophy } from 'lucide-react';

interface UserBracketCardProps {
  tournament: Tournament;
  onViewBracket: () => void;
}

export default function UserBracketCard({
  tournament,
  onViewBracket
}: UserBracketCardProps) {
  // Se não houver informações da subchave do usuário, não exibir o componente
  if (!tournament.userBracket) {
    return null;
  }
  
  const { userBracket } = tournament;
  
  return (
    <div className="bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-indigo-500/20 rounded-xl p-4 sm:p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-white text-lg font-bold flex items-center gap-2">
          <Users className="w-5 h-5 text-indigo-400" />
          Sua Subchave
        </h2>
        <button
          onClick={onViewBracket}
          className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center gap-1"
        >
          Ver Chaveamento
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
      
      <div className="bg-gray-800/70 backdrop-blur-sm rounded-lg p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="bg-indigo-500/20 p-2 rounded-lg">
            <Trophy className="w-5 h-5 text-indigo-400" />
          </div>
          <div>
            <p className="text-white font-medium">{userBracket.seasonName} - {userBracket.bracketName}</p>
            <p className="text-gray-400 text-sm">{userBracket.participants} participantes</p>
          </div>
        </div>
        
        <div className="flex items-center justify-between text-sm">
          <div className="text-gray-400">Sua posição:</div>
          <div className="text-white font-medium">
            {userBracket.userPosition === 1 
              ? <span className="text-yellow-400">1º Lugar 🏆</span>
              : `${userBracket.userPosition}º Lugar`}
          </div>
        </div>
        
        {userBracket.status === 'completed' && userBracket.userPosition === 1 && (
          <div className="mt-3 p-3 bg-green-500/20 text-green-400 rounded-lg text-sm">
            Parabéns! Você avançou para a próxima fase do torneio.
          </div>
        )}
      </div>
    </div>
  );
}

import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Brain, Loader2 } from 'lucide-react';

export default function MatchAnalysisPage() {
  const navigate = useNavigate();
  const { gameId } = useParams();
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  const analysisSteps = [
    'Analisando dados da partida...',
    'Processando resultados...',
    'Verificando pontuações...',
    'Confirmando vencedor...'
  ];

  useEffect(() => {
    // Progress bar animation
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + 1;
      });
    }, 50);

    // Step changes
    const stepInterval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev >= analysisSteps.length - 1) {
          clearInterval(stepInterval);
          return prev;
        }
        return prev + 1;
      });
    }, 1250);

    // Navigate to results after 5 seconds
    const navigationTimeout = setTimeout(() => {
      navigate(`/games/${gameId}/results`);
    }, 5000);

    return () => {
      clearInterval(progressInterval);
      clearInterval(stepInterval);
      clearTimeout(navigationTimeout);
    };
  }, [navigate, gameId]);

  return (
    <div className="fixed inset-0 bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8 text-center">
        {/* AI Icon with Glow Effect */}
        <div className="relative mx-auto w-24 h-24 mb-8">
          <div className="absolute inset-0 bg-rose-400 rounded-full blur-2xl opacity-20 animate-pulse" />
          <div className="relative bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-full p-6">
            <Brain className="w-12 h-12 text-rose-400" />
          </div>
        </div>

        {/* Title */}
        <h2 className="text-2xl font-bold text-white mb-2">
          Análise em Andamento
        </h2>
        
        {/* Current Step */}
        <div className="h-6">
          <p className="text-gray-300 animate-fadeIn">
            {analysisSteps[currentStep]}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="relative pt-1">
          <div className="overflow-hidden h-2 text-xs flex rounded-full bg-gray-700">
            <div
              style={{ width: `${progress}%` }}
              className="bg-gradient-to-r from-rose-400 to-pink-400 rounded-full transition-all duration-300 ease-out"
            />
          </div>
        </div>

        {/* Loading Spinner */}
        <div className="flex justify-center">
          <Loader2 className="w-6 h-6 text-rose-400 animate-spin" />
        </div>
      </div>
    </div>
  );
}
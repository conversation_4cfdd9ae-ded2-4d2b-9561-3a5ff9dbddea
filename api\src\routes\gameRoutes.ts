import { Router } from 'express';
import {
  getGames,
  getGameById,
  getGameRooms,
  createGameRoom,
  getRoomById,
  joinRoom,
  getGameRanking
} from '../controllers/gameController';
import { validate, schemas } from '../middleware/validation';
import { authenticate, optionalAuthenticate } from '../middleware/auth';
import { matchCreationRateLimit } from '../middleware/rateLimiting';

const router = Router();

/**
 * @route   GET /api/games
 * @desc    Get all games
 * @access  Public
 */
router.get('/',
  optionalAuthenticate,
  getGames
);

/**
 * @route   GET /api/games/external
 * @desc    Get external games only
 * @access  Public
 */
router.get('/external',
  optionalAuthenticate,
  async (req, res, next) => {
    req.query.type = 'external';
    next();
  },
  getGames
);

/**
 * @route   GET /api/games/internal
 * @desc    Get internal games only
 * @access  Public
 */
router.get('/internal',
  optionalAuthenticate,
  async (req, res, next) => {
    req.query.type = 'internal';
    next();
  },
  getGames
);

/**
 * @route   GET /api/games/:id
 * @desc    Get game by ID or slug
 * @access  Public
 */
router.get('/:id',
  optionalAuthenticate,
  getGameById
);

/**
 * @route   GET /api/games/:id/rooms
 * @desc    Get game rooms
 * @access  Public
 */
router.get('/:id/rooms',
  optionalAuthenticate,
  validate(schemas.pagination, 'query'),
  getGameRooms
);

/**
 * @route   POST /api/games/:id/rooms
 * @desc    Create game room
 * @access  Private
 */
router.post('/:id/rooms',
  authenticate,
  matchCreationRateLimit,
  validate(schemas.gameRoomCreation),
  createGameRoom
);

/**
 * @route   GET /api/games/rooms/:roomId
 * @desc    Get room by ID
 * @access  Public
 */
router.get('/rooms/:roomId',
  optionalAuthenticate,
  validate(schemas.roomIdParam, 'params'),
  getRoomById
);

/**
 * @route   POST /api/games/rooms/:roomId/join
 * @desc    Join a room
 * @access  Private
 */
router.post('/rooms/:roomId/join',
  authenticate,
  validate(schemas.roomIdParam, 'params'),
  joinRoom
);

/**
 * @route   GET /api/games/:id/ranking
 * @desc    Get game ranking/leaderboard
 * @access  Public
 */
router.get('/:id/ranking',
  optionalAuthenticate,
  validate(schemas.pagination, 'query'),
  getGameRanking
);

/**
 * @route   POST /api/games/rooms/:roomId/fix-count
 * @desc    Fix room participant count (temporary endpoint)
 * @access  Public
 */
router.post('/rooms/:roomId/fix-count',
  async (req, res): Promise<any> => {
    try {
      const { roomId } = req.params;
      const { supabaseAdmin } = require('../config/database');

      // Get actual participant count
      const { data: participants, error: participantError } = await supabaseAdmin
        .from('room_participants')
        .select('user_id')
        .eq('room_id', roomId);

      if (participantError) {
        return res.status(500).json({ success: false, error: participantError.message });
      }

      const actualCount = participants?.length || 0;

      // Update the room's current_players count
      const { error: updateError } = await supabaseAdmin
        .from('game_rooms')
        .update({
          current_players: actualCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', roomId);

      if (updateError) {
        return res.status(500).json({ success: false, error: updateError.message });
      }

      res.json({
        success: true,
        message: 'Room count fixed',
        actualCount,
        roomId
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      res.status(500).json({ success: false, error: errorMessage });
    }
  }
);

/**
 * @route   GET /api/games/:id/leaderboard
 * @desc    Get game leaderboard (alias for ranking)
 * @access  Public
 */
router.get('/:id/leaderboard',
  optionalAuthenticate,
  validate(schemas.pagination, 'query'),
  getGameRanking
);

/**
 * @route   GET /api/games/:id/statistics
 * @desc    Get game statistics
 * @access  Public
 */
router.get('/:id/statistics',
  optionalAuthenticate,
  async (req, res) => {
    // TODO: Implement game statistics endpoint
    res.status(501).json({
      success: false,
      error: 'Game statistics endpoint not implemented yet'
    });
  }
);

/**
 * @route   GET /api/games/:id/tournaments
 * @desc    Get tournaments for a specific game
 * @access  Public
 */
router.get('/:id/tournaments',
  optionalAuthenticate,
  validate(schemas.pagination, 'query'),
  async (req, res) => {
    // TODO: Implement game tournaments endpoint
    res.status(501).json({
      success: false,
      error: 'Game tournaments endpoint not implemented yet'
    });
  }
);

/**
 * @route   GET /api/games/:id/matches
 * @desc    Get recent matches for a specific game
 * @access  Public
 */
router.get('/:id/matches',
  optionalAuthenticate,
  validate(schemas.pagination, 'query'),
  async (req, res) => {
    // TODO: Implement game matches endpoint
    res.status(501).json({
      success: false,
      error: 'Game matches endpoint not implemented yet'
    });
  }
);

/**
 * @route   POST /api/games/:id/favorite
 * @desc    Add/remove game from favorites
 * @access  Private
 */
router.post('/:id/favorite',
  authenticate,
  async (req, res) => {
    // TODO: Implement favorite game functionality
    res.status(501).json({
      success: false,
      error: 'Favorite game functionality not implemented yet'
    });
  }
);

export default router;

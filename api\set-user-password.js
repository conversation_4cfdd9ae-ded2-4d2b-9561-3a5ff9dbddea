const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setUserPassword() {
  try {
    const email = '<EMAIL>';
    const password = 'Milhao123#';
    
    console.log(`🔧 Setting password for user: ${email}`);
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);
    console.log('🔐 Password hashed successfully');
    
    // Update user with password hash
    const { data, error } = await supabase
      .from('users')
      .update({ password_hash: hashedPassword })
      .eq('email', email)
      .select('id, email, username');
    
    if (error) {
      console.error('❌ Failed to set password:', error);
      return;
    }
    
    if (data && data.length > 0) {
      console.log('✅ Password set successfully for user:', data[0].username);
      console.log('📧 Email:', data[0].email);
      console.log('🆔 ID:', data[0].id);
      
      // Verify the password works
      console.log('\n🔍 Verifying password...');
      const { data: verifyUser, error: verifyError } = await supabase
        .from('users')
        .select('password_hash')
        .eq('email', email)
        .single();
      
      if (verifyError) {
        console.error('❌ Failed to verify:', verifyError);
        return;
      }
      
      const isValid = await bcrypt.compare(password, verifyUser.password_hash);
      console.log('✅ Password verification:', isValid ? 'SUCCESS' : 'FAILED');
      
    } else {
      console.log('❌ User not found or not updated');
    }
    
  } catch (error) {
    console.error('❌ Error setting password:', error);
  }
}

setUserPassword();

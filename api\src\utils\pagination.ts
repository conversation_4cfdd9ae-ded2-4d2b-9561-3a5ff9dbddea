import { PaginationInfo } from '../types/api';

/**
 * Create pagination information
 */
export const createPagination = (
  page: number,
  limit: number,
  total: number
): PaginationInfo => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page,
    limit,
    total,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

/**
 * Calculate offset for database queries
 */
export const calculateOffset = (page: number, limit: number): number => {
  return (page - 1) * limit;
};

/**
 * Validate pagination parameters
 */
export const validatePagination = (page?: number, limit?: number) => {
  const validatedPage = Math.max(1, page || 1);
  const validatedLimit = Math.min(100, Math.max(1, limit || 20));
  
  return {
    page: validatedPage,
    limit: validatedLimit
  };
};

/**
 * Create pagination query for Supabase
 */
export const createPaginationQuery = (page: number, limit: number) => {
  const offset = calculateOffset(page, limit);
  return {
    from: offset,
    to: offset + limit - 1
  };
};

/**
 * Extract pagination from query parameters
 */
export const extractPagination = (query: any) => {
  const page = parseInt(query.page) || 1;
  const limit = parseInt(query.limit) || 20;
  
  return validatePagination(page, limit);
};

/**
 * Create cursor-based pagination for real-time data
 */
export const createCursorPagination = (
  items: any[],
  limit: number,
  cursorField: string = 'created_at'
) => {
  const hasMore = items.length > limit;
  const data = hasMore ? items.slice(0, limit) : items;
  
  const nextCursor = hasMore && data.length > 0 
    ? data[data.length - 1][cursorField] 
    : null;
  
  return {
    data,
    hasMore,
    nextCursor
  };
};

/**
 * Create pagination metadata for API responses
 */
export const createPaginationMeta = (
  page: number,
  limit: number,
  total: number,
  additionalMeta?: any
) => {
  const pagination = createPagination(page, limit, total);
  
  return {
    pagination,
    ...additionalMeta
  };
};

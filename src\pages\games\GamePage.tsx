import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Users, Clock, Medal, Trophy, Rocket, Calendar, ArrowUp, ArrowDown, ArrowLeft } from 'lucide-react';

interface Player {
  id: string;
  name: string;
  avatar: string;
  score: number;
  position: number;
  previousPosition?: number;
  isCurrentUser?: boolean;
}

interface GameInfo {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  players: number;
  icon: React.ElementType;
  prizePool: number;
}

interface GamePageProps {
  gameInfo: GameInfo;
}

export default function GamePage({ gameInfo }: GamePageProps) {
  const navigate = useNavigate();
  const { gameId } = useParams();

  // Dados simulados para o ranking do torneio
  const players: Player[] = [
    {
      id: '1',
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: 2840,
      position: 1,
      previousPosition: 2
    },
    {
      id: '2',
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: 2750,
      position: 2,
      previousPosition: 1
    },
    {
      id: '3',
      name: 'Maria Santos',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: 2680,
      position: 3,
      previousPosition: 3
    },
    {
      id: '4',
      name: 'Pedro Costa',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: 2590,
      position: 4,
      previousPosition: 6
    },
    {
      id: '5',
      name: 'Você',
      avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: 2480,
      position: 5,
      previousPosition: 4,
      isCurrentUser: true
    },
    {
      id: '6',
      name: 'Julia Lima',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: 2420,
      position: 6,
      previousPosition: 5
    },
    {
      id: '7',
      name: 'Rafael Silva',
      avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: 2380,
      position: 7,
      previousPosition: 7
    },
    {
      id: '8',
      name: 'Carla Souza',
      avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: 2330,
      position: 8,
      previousPosition: 8
    }
  ];

  // Função para renderizar o indicador de mudança de posição
  const renderPositionChange = (player: Player) => {
    if (!player.previousPosition) return null;

    if (player.position < player.previousPosition) {
      return (
        <div className="flex items-center gap-1 text-green-500">
          <ArrowUp className="w-3 h-3" />
          <span className="text-xs">{player.previousPosition - player.position}</span>
        </div>
      );
    } else if (player.position > player.previousPosition) {
      return (
        <div className="flex items-center gap-1 text-red-500">
          <ArrowDown className="w-3 h-3" />
          <span className="text-xs">{player.position - player.previousPosition}</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-1 text-gray-500">
          <span className="text-xs">-</span>
        </div>
      );
    }
  };

  const Icon = gameInfo.icon;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <button
            onClick={() => navigate('/internal-games')}
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Voltar para Jogos Internos</span>
          </button>
        </div>

        <div>
          <h1 className="text-2xl font-bold text-white">{gameInfo.title}</h1>
          <div className="flex items-center gap-2 text-gray-400">
            <Users className="w-4 h-4" />
            <span>{gameInfo.players} jogadores online</span>
          </div>
        </div>
      </div>

      {/* Game Banner */}
      <div className="relative h-[200px] rounded-xl overflow-hidden">
        <img
          src={gameInfo.imageUrl}
          alt={gameInfo.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent">
          <div className="absolute bottom-0 left-0 right-0 p-6">
            <p className="text-white text-lg mb-2">{gameInfo.description}</p>
          </div>
        </div>
      </div>

      {/* Prize Pool Banner */}
      <div className="bg-gradient-to-r from-rose-400 to-pink-400 rounded-xl p-4">
        <div className="flex items-center gap-3">
          <div className="bg-white/20 p-2 rounded-lg">
            <Trophy className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-white font-bold text-lg">Prêmio Total</h2>
            <div className="flex items-center gap-2">
              <Calendar className="w-3.5 h-3.5 text-white/80" />
              <p className="text-white/80">Torneio termina hoje às 23:59</p>
            </div>
          </div>
          <div className="ml-auto">
            <p className="text-3xl font-bold text-white">R$ {gameInfo.prizePool.toLocaleString()}</p>
          </div>
        </div>
      </div>

      {/* Tournament Info */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-white/10">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Icon className="w-5 h-5 text-rose-400" />
            <h3 className="text-white font-semibold">Torneio Diário</h3>
          </div>
          <div className="flex items-center gap-2 text-gray-400">
            <Clock className="w-4 h-4" />
            <span>Atualizado há 2 min</span>
          </div>
        </div>
        <p className="text-gray-400 text-sm mb-4">
          Jogue quantas vezes quiser para melhorar sua pontuação. Apenas sua melhor pontuação é considerada para o ranking. Os 3 primeiros colocados ganham prêmios em dinheiro!
        </p>
        <div className="bg-gray-700/30 rounded-lg p-3 mb-4 border border-white/5">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="w-4 h-4 text-rose-400" />
            <p className="text-white text-sm font-semibold">Como funciona:</p>
          </div>
          <ul className="text-gray-400 text-xs space-y-1 list-disc pl-5">
            <li>O torneio é diário e termina à meia-noite</li>
            <li>Jogue quantas vezes quiser para tentar melhorar sua pontuação</li>
            <li>Apenas sua melhor pontuação será considerada no ranking</li>
            <li>Os prêmios são distribuídos automaticamente ao final do torneio</li>
          </ul>
        </div>
        <div className="grid grid-cols-3 gap-3">
          <div className="bg-gray-700/50 rounded-lg p-3 text-center">
            <div className="flex justify-center mb-1">
              <div className="bg-yellow-400/20 p-1.5 rounded-full">
                <Medal className="w-5 h-5 text-yellow-400" />
              </div>
            </div>
            <p className="text-white font-semibold">1º Lugar</p>
            <p className="text-yellow-400 font-bold">R$ {Math.round(gameInfo.prizePool * 0.5).toLocaleString()}</p>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-3 text-center">
            <div className="flex justify-center mb-1">
              <div className="bg-gray-400/20 p-1.5 rounded-full">
                <Medal className="w-5 h-5 text-gray-400" />
              </div>
            </div>
            <p className="text-white font-semibold">2º Lugar</p>
            <p className="text-gray-400 font-bold">R$ {Math.round(gameInfo.prizePool * 0.3).toLocaleString()}</p>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-3 text-center">
            <div className="flex justify-center mb-1">
              <div className="bg-amber-700/20 p-1.5 rounded-full">
                <Medal className="w-5 h-5 text-amber-700" />
              </div>
            </div>
            <p className="text-white font-semibold">3º Lugar</p>
            <p className="text-amber-700 font-bold">R$ {Math.round(gameInfo.prizePool * 0.2).toLocaleString()}</p>
          </div>
        </div>
      </div>

      {/* Player Score */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-white/10">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            <h3 className="text-white font-semibold">Sua Pontuação</h3>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="bg-gray-700/50 rounded-lg p-3">
            <p className="text-gray-400 text-xs mb-1">Melhor Pontuação</p>
            <p className="text-white text-xl font-bold">2.480</p>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-3">
            <p className="text-gray-400 text-xs mb-1">Última Partida</p>
            <p className="text-white text-xl font-bold">2.150</p>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-3">
            <p className="text-gray-400 text-xs mb-1">Posição Atual</p>
            <div className="flex items-center gap-2">
              <p className="text-white text-xl font-bold">5º</p>
              <div className="flex items-center gap-1 text-red-500 text-xs">
                <ArrowDown className="w-3 h-3" />
                <span>1</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <button
          onClick={() => navigate(`/games/${gameId || gameInfo.id}/rooms/dev/play`)}
          className="w-full bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold py-4 px-4 rounded-xl flex items-center justify-center gap-2 hover:opacity-90 transition-opacity shadow-lg"
        >
          <Icon className="w-5 h-5" />
          Jogar Agora
        </button>
      </div>

      {/* Ranking List */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h2 className="text-white font-bold">Ranking de pontos do torneio</h2>
        </div>

        <div className="space-y-2">
          {players.map((player) => (
            <div
              key={player.id}
              className={`w-full ${player.isCurrentUser ? 'bg-rose-400/10 border border-rose-400/30' : 'bg-gray-800'} rounded-xl overflow-hidden p-3`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <div className="w-10 h-10 rounded-full overflow-hidden">
                      <img
                        src={player.avatar}
                        alt={player.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold ${player.position <= 3 ? 'bg-yellow-400 text-black' : 'bg-gray-700 text-white'}`}>
                      {player.position}
                    </div>
                  </div>
                  <div className="text-left">
                    <h3 className={`font-semibold ${player.isCurrentUser ? 'text-rose-400' : 'text-white'}`}>
                      {player.name}
                    </h3>
                    <div className="flex items-center gap-2">
                      <p className="text-gray-400 text-sm">
                        {player.score.toLocaleString()} pts
                      </p>
                      {renderPositionChange(player)}
                    </div>
                  </div>
                </div>
                {player.position <= 3 && (
                  <div className="flex items-center gap-2">
                    <Medal className={`w-5 h-5 ${player.position === 1 ? 'text-yellow-400' : player.position === 2 ? 'text-gray-400' : 'text-amber-700'}`} />
                    <span className={`font-bold ${player.position === 1 ? 'text-yellow-400' : player.position === 2 ? 'text-gray-400' : 'text-amber-700'}`}>
                      R$ {player.position === 1
                        ? Math.round(gameInfo.prizePool * 0.5).toLocaleString()
                        : player.position === 2
                          ? Math.round(gameInfo.prizePool * 0.3).toLocaleString()
                          : Math.round(gameInfo.prizePool * 0.2).toLocaleString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

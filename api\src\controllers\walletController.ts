import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, TransactionData, PaginationQuery } from '../types/api';
import { logger, logBusinessEvent, logSecurityEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';
import config from '../config/environment';

/**
 * Get wallet balance and information
 */
export const getWallet = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    let { data: wallets, error } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(1);

    let wallet = wallets && wallets.length > 0 ? wallets[0] : null;

    // If wallet doesn't exist, create it
    if (!wallet) {
      logger.info(`Creating wallet for user ${user.id}`);

      // Try to create wallet using direct insert
      const { data: newWallet, error: insertError } = await supabaseAdmin
        .from('wallets')
        .insert({
          user_id: user.id,
          balance: 0.00,
          frozen_balance: 0.00,
          total_deposited: 0.00,
          total_withdrawn: 0.00
        })
        .select('*')
        .single();

      if (insertError) {
        logger.error('Failed to create wallet:', insertError);
        const response: ApiResponse = {
          success: false,
          error: 'Failed to create wallet'
        };
        res.status(500).json(response);
        return;
      }

      wallet = newWallet;
      logger.info(`Wallet created successfully for user ${user.id}`);
    }

    const response: ApiResponse = {
      success: true,
      data: {
        wallet: {
          id: wallet.id,
          balance: wallet.balance,
          frozen_balance: wallet.frozen_balance,
          total_deposited: wallet.total_deposited,
          total_withdrawn: wallet.total_withdrawn,
          available_balance: wallet.balance - wallet.frozen_balance,
          updated_at: wallet.updated_at
        }
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get wallet error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Create deposit transaction
 */
export const createDeposit = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { amount, payment_method } = req.body;

    // Validate amount limits
    if (amount < config.MIN_DEPOSIT_AMOUNT || amount > config.MAX_DEPOSIT_AMOUNT) {
      const response: ApiResponse = {
        success: false,
        error: `Deposit amount must be between R$ ${config.MIN_DEPOSIT_AMOUNT} and R$ ${config.MAX_DEPOSIT_AMOUNT}`
      };
      res.status(400).json(response);
      return;
    }

    // Get user wallet
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (walletError || !wallet) {
      const response: ApiResponse = {
        success: false,
        error: 'Wallet not found'
      };
      res.status(404).json(response);
      return;
    }

    // Create transaction
    const { data: transaction, error: transactionError } = await supabaseAdmin
      .from('transactions')
      .insert({
        user_id: user.id,
        wallet_id: wallet.id,
        type: 'deposit',
        amount,
        payment_method,
        description: `Deposit via ${payment_method}`,
        metadata: {
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        }
      })
      .select('*')
      .single();

    if (transactionError || !transaction) {
      logger.error('Failed to create deposit transaction:', transactionError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create deposit transaction'
      };
      res.status(500).json(response);
      return;
    }

    // Here you would integrate with payment gateway
    // For now, we'll simulate the payment process
    const paymentResult = await processPayment(transaction, payment_method);

    if (!paymentResult.success) {
      // Update transaction status to failed
      await supabaseAdmin
        .from('transactions')
        .update({
          status: 'failed',
          metadata: {
            ...transaction.metadata,
            error: paymentResult.error
          }
        })
        .eq('id', transaction.id);

      const response: ApiResponse = {
        success: false,
        error: paymentResult.error || 'Payment processing failed'
      };
      res.status(400).json(response);
      return;
    }

    // Update transaction with payment gateway response
    await supabaseAdmin
      .from('transactions')
      .update({
        external_transaction_id: paymentResult.external_id,
        metadata: {
          ...transaction.metadata,
          payment_data: paymentResult.data
        }
      })
      .eq('id', transaction.id);

    logBusinessEvent('deposit_initiated', user.id, {
      amount,
      payment_method,
      transaction_id: transaction.id
    });

    const response: ApiResponse = {
      success: true,
      message: 'Deposit transaction created successfully',
      data: {
        transaction: {
          id: transaction.id,
          amount: transaction.amount,
          status: transaction.status,
          payment_method: transaction.payment_method,
          created_at: transaction.created_at
        },
        payment_data: paymentResult.data
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Create deposit error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Create withdrawal transaction
 */
export const createWithdrawal = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { amount, payment_method, pix_key } = req.body;

    // Validate amount limits
    if (amount < config.MIN_WITHDRAWAL_AMOUNT || amount > config.MAX_WITHDRAWAL_AMOUNT) {
      const response: ApiResponse = {
        success: false,
        error: `Withdrawal amount must be between R$ ${config.MIN_WITHDRAWAL_AMOUNT} and R$ ${config.MAX_WITHDRAWAL_AMOUNT}`
      };
      res.status(400).json(response);
      return;
    }

    // Get user wallet
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (walletError || !wallet) {
      const response: ApiResponse = {
        success: false,
        error: 'Wallet not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check available balance
    const availableBalance = wallet.balance - wallet.frozen_balance;
    if (amount > availableBalance) {
      logSecurityEvent('withdrawal_insufficient_funds', 'medium', {
        user_id: user.id,
        requested_amount: amount,
        available_balance: availableBalance
      });

      const response: ApiResponse = {
        success: false,
        error: 'Insufficient balance'
      };
      res.status(400).json(response);
      return;
    }

    // Create transaction
    const { data: transaction, error: transactionError } = await supabaseAdmin
      .from('transactions')
      .insert({
        user_id: user.id,
        wallet_id: wallet.id,
        type: 'withdrawal',
        amount,
        payment_method,
        description: `Withdrawal via ${payment_method}`,
        metadata: {
          pix_key: payment_method === 'pix' ? pix_key : undefined,
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        }
      })
      .select('*')
      .single();

    if (transactionError || !transaction) {
      logger.error('Failed to create withdrawal transaction:', transactionError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create withdrawal transaction'
      };
      res.status(500).json(response);
      return;
    }

    // Freeze the withdrawal amount
    await supabaseAdmin
      .from('wallets')
      .update({
        frozen_balance: wallet.frozen_balance + amount
      })
      .eq('id', wallet.id);

    logBusinessEvent('withdrawal_initiated', user.id, {
      amount,
      payment_method,
      transaction_id: transaction.id
    });

    const response: ApiResponse = {
      success: true,
      message: 'Withdrawal request created successfully',
      data: {
        transaction: {
          id: transaction.id,
          amount: transaction.amount,
          status: transaction.status,
          payment_method: transaction.payment_method,
          created_at: transaction.created_at
        }
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Create withdrawal error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get transaction history
 */
export const getTransactions = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);
    const { type, status, date_from, date_to } = req.query;

    let query = supabaseAdmin
      .from('transactions')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id);

    // Apply filters
    if (type) {
      query = query.eq('type', type);
    }
    if (status) {
      query = query.eq('status', status);
    }
    if (date_from) {
      query = query.gte('created_at', date_from);
    }
    if (date_to) {
      query = query.lte('created_at', date_to);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: transactions, error, count } = await query;

    if (error) {
      logger.error('Failed to get transactions:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve transactions'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        transactions: transactions || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get transactions error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get transaction by ID
 */
export const getTransactionById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;

    const { data: transaction, error } = await supabaseAdmin
      .from('transactions')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error || !transaction) {
      const response: ApiResponse = {
        success: false,
        error: 'Transaction not found'
      };
      res.status(404).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        transaction
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get transaction by ID error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

// Helper function to simulate payment processing
const processPayment = async (transaction: any, paymentMethod: string) => {
  // This would integrate with actual payment gateway
  // For demo purposes, we'll simulate different scenarios

  try {
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simulate success/failure based on amount (for demo)
    if (transaction.amount > 1000) {
      // Simulate failure for large amounts in demo
      return {
        success: false,
        error: 'Payment declined by gateway'
      };
    }

    return {
      success: true,
      external_id: `PAY_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      data: {
        gateway_response: 'approved',
        authorization_code: Math.random().toString(36).substr(2, 9).toUpperCase(),
        payment_method: paymentMethod
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Payment gateway error'
    };
  }
};

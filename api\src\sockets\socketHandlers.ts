import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { supabaseAdmin } from '../config/database';
import { logger } from '../utils/logger';
import { RoomStateService } from '../services/roomStateService';
import config from '../config/environment';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  username?: string;
}

// Socket authentication middleware
const authenticateSocket = async (socket: AuthenticatedSocket, next: any) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return next(new Error('Authentication token required'));
    }

    // Verify JWT token
    const decoded = jwt.verify(token, config.JWT_SECRET) as any;

    // Get user from database
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('id, username, status')
      .eq('id', decoded.sub)
      .eq('status', 'active')
      .single();

    if (error || !user) {
      return next(new Error('User not found or inactive'));
    }

    socket.userId = user.id;
    socket.username = user.username;

    logger.info(`Socket authenticated: ${user.username} (${socket.id})`);
    next();
  } catch (error) {
    logger.error('Socket authentication error:', error);
    next(new Error('Authentication failed'));
  }
};

// Initialize socket handlers
export const initializeSocketHandlers = (io: SocketIOServer) => {
  // Apply authentication middleware
  io.use(authenticateSocket);

  io.on('connection', (socket: AuthenticatedSocket) => {
    logger.info(`User ${socket.username} connected via socket: ${socket.id}`);

    // Join user to their personal room for notifications
    socket.join(`user:${socket.userId}`);

    // Handle room joining
    socket.on('join_room', async (data: { roomId: string }) => {
      try {
        const { roomId } = data;

        // Verify user is participant of the room using service
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        const isParticipant = await RoomStateService.isUserParticipant(roomId, socket.userId);
        if (!isParticipant) {
          socket.emit('error', { message: 'Not authorized to join this room' });
          return;
        }

        socket.join(`room:${roomId}`);
        socket.emit('room_joined', { roomId });

        // Get current room state using service
        const roomState = await RoomStateService.getRoomState(roomId);
        if (roomState) {
          // Send current room state to the joining user
          socket.emit('room_state_update', {
            status: roomState.status,
            started_at: roomState.started_at,
            participants: roomState.participants,
            currentMatch: roomState.currentMatch || null
          });

          // If game is in progress, send match state
          if (roomState.status === 'playing' && roomState.currentMatch) {
            socket.emit('match_in_progress', {
              matchId: roomState.currentMatch.id,
              startedAt: roomState.currentMatch.started_at
            });
          } else if (roomState.status === 'starting') {
            socket.emit('game_starting', {
              countdown: 5, // Default countdown if user joins during starting
              message: 'Partida iniciando...'
            });
          }
        }

        // Notify other room members and send updated room state
        socket.to(`room:${roomId}`).emit('user_joined_room', {
          userId: socket.userId,
          username: socket.username
        });

        // Send updated room state to all users in the room
        const updatedRoomState = await RoomStateService.getRoomState(roomId);
        if (updatedRoomState) {
          io.to(`room:${roomId}`).emit('room_state_update', {
            status: updatedRoomState.status,
            started_at: updatedRoomState.started_at,
            participants: updatedRoomState.participants,
            currentMatch: updatedRoomState.currentMatch || null
          });
        }

        logger.info(`User ${socket.username} joined room: ${roomId}`);
      } catch (error) {
        logger.error('Error joining room:', error);
        socket.emit('error', { message: 'Failed to join room' });
      }
    });

    // Handle room leaving
    socket.on('leave_room', (data: { roomId: string }) => {
      const { roomId } = data;
      socket.leave(`room:${roomId}`);
      socket.emit('room_left', { roomId });

      // Notify other room members
      socket.to(`room:${roomId}`).emit('user_left_room', {
        userId: socket.userId,
        username: socket.username
      });

      logger.info(`User ${socket.username} left room: ${roomId}`);
    });

    // Handle chat messages
    socket.on('send_message', async (data: { roomId: string; message: string; type?: string }) => {
      try {
        const { roomId, message, type = 'text' } = data;

        // Verify user is participant of the room
        const { data: participant, error } = await supabaseAdmin
          .from('room_participants')
          .select('id')
          .eq('room_id', roomId)
          .eq('user_id', socket.userId)
          .single();

        if (error || !participant) {
          socket.emit('error', { message: 'Not authorized to send messages in this room' });
          return;
        }

        // Save message to database
        const { data: savedMessage, error: messageError } = await supabaseAdmin
          .from('room_chat_messages')
          .insert({
            room_id: roomId,
            user_id: socket.userId,
            message,
            message_type: type
          })
          .select(`
            id, message, message_type, created_at,
            users (username, display_name, avatar_url)
          `)
          .single();

        if (messageError || !savedMessage) {
          socket.emit('error', { message: 'Failed to send message' });
          return;
        }

        // Broadcast message to room
        io.to(`room:${roomId}`).emit('new_message', savedMessage);

        logger.info(`Message sent by ${socket.username} in room ${roomId}`);
      } catch (error) {
        logger.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle match updates
    socket.on('join_match', async (data: { matchId: string }) => {
      try {
        const { matchId } = data;

        // Verify user is participant of the match
        const { data: participant, error } = await supabaseAdmin
          .from('match_participants')
          .select('id')
          .eq('match_id', matchId)
          .eq('user_id', socket.userId)
          .single();

        if (error || !participant) {
          socket.emit('error', { message: 'Not authorized to join this match' });
          return;
        }

        socket.join(`match:${matchId}`);
        socket.emit('match_joined', { matchId });

        // Get current match state from database
        const { data: matchState, error: matchError } = await supabaseAdmin
          .from('matches')
          .select(`
            *,
            match_participants (user_id, score, placement),
            game_rooms (status, started_at)
          `)
          .eq('id', matchId)
          .single();

        if (!matchError && matchState) {
          // Send current match state to the joining user
          socket.emit('match_state_update', {
            status: matchState.status,
            started_at: matchState.started_at,
            finished_at: matchState.finished_at,
            participants: matchState.match_participants,
            room_status: matchState.game_rooms?.status
          });
        }

        logger.info(`User ${socket.username} joined match: ${matchId}`);
      } catch (error) {
        logger.error('Error joining match:', error);
        socket.emit('error', { message: 'Failed to join match' });
      }
    });

    // Handle match result submission
    socket.on('submit_match_result', async (data: {
      matchId: string;
      result: 'win' | 'loss' | 'draw';
      score?: number;
      evidence?: string;
    }) => {
      try {
        const { matchId, result, score, evidence } = data;

        // Force console log for debugging
        console.log(`🎯 SUBMIT MATCH RESULT - User: ${socket.username} (${socket.userId}), Match: ${matchId}, Result: ${result}`);

        // Verify user is participant of the match
        const { data: participant, error: participantError } = await supabaseAdmin
          .from('match_participants')
          .select('id, match_id')
          .eq('match_id', matchId)
          .eq('user_id', socket.userId)
          .single();

        if (participantError || !participant) {
          socket.emit('error', { message: 'Not authorized to submit result for this match' });
          return;
        }

        // Check if match is still in progress
        logger.info(`🔍 Checking match ${matchId} status...`);
        const { data: match, error: matchError } = await supabaseAdmin
          .from('matches')
          .select('status, room_id, entry_fee')
          .eq('id', matchId)
          .single();

        logger.info(`Match query result:`, { match, matchError });

        if (matchError || !match) {
          logger.error(`❌ Match ${matchId} not found:`, matchError);
          socket.emit('error', { message: 'Match is not in progress or not found' });
          return;
        }

        if (match.status !== 'in_progress') {
          logger.error(`❌ Match ${matchId} has invalid status: ${match.status} (expected: in_progress)`);
          socket.emit('error', { message: 'Match is not in progress or not found' });
          return;
        }

        logger.info(`✅ Match ${matchId} is valid and in progress`);


        // Update participant result in database
        const placementValue = result === 'win' ? 1 : result === 'loss' ? 2 : null;
        logger.info(`🎯 Submitting result for user ${socket.userId} (${socket.username}):`);
        logger.info(`  Match ID: ${matchId}`);
        logger.info(`  Result: ${result}`);
        logger.info(`  Score: ${score || 0}`);
        logger.info(`  Placement: ${placementValue}`);

        const { error: updateError } = await supabaseAdmin
          .from('match_participants')
          .update({
            score: score || 0,
            placement: placementValue,
            stats: { result, evidence, submitted_at: new Date().toISOString() }
          })
          .eq('match_id', matchId)
          .eq('user_id', socket.userId);

        if (updateError) {
          socket.emit('error', { message: 'Failed to submit match result' });
          return;
        }

        // Check how many participants have submitted results AFTER this submission
        const { data: allParticipants, error: allParticipantsError } = await supabaseAdmin
          .from('match_participants')
          .select('user_id, stats, placement')
          .eq('match_id', matchId);

        if (allParticipantsError) {
          logger.error('Error checking participants:', allParticipantsError);
          socket.emit('error', { message: 'Failed to check match participants' });
          return;
        }

        const totalParticipants = allParticipants?.length || 0;

        // Verificar quantos participantes têm placement definido (indicando que submeteram resultado)
        const participantsWithResults = allParticipants?.filter(p => p.placement !== null && p.placement !== undefined).length || 0;

        // Since we just updated this user's result, participantsWithResults includes current submission
        const isFirstResult = participantsWithResults === 1; // This is the first result
        const allResultsSubmitted = participantsWithResults === totalParticipants;

        console.log(`🔍 Match ${matchId} result submission analysis:`);
        console.log(`  Total participants: ${totalParticipants}`);
        console.log(`  Participants with results (placement defined): ${participantsWithResults}`);
        console.log(`  Participants data:`, allParticipants?.map(p => ({
          user_id: p.user_id,
          placement: p.placement,
          stats: p.stats ? 'defined' : 'null'
        })));
        console.log(`  Is first result: ${isFirstResult}`);
        console.log(`  All results submitted: ${allResultsSubmitted}`);

        // Broadcast result submission to room
        if (match.room_id) {
          io.to(`room:${match.room_id}`).emit('match_result_submitted', {
            userId: socket.userId,
            username: socket.username,
            result,
            score,
            matchId,
            isFirstResult,
            allResultsSubmitted,
            participantsWithResults,
            totalParticipants
          });

          // If this is the first result, notify other players that match is ending
          if (isFirstResult) {
            socket.to(`room:${match.room_id}`).emit('match_ending', {
              message: `${socket.username} submeteu o resultado. A partida está sendo finalizada.`,
              submittedBy: socket.username,
              result
            });
          }

          // If all results are submitted, finalize the match and room
          if (allResultsSubmitted && totalParticipants >= 2) {
            logger.info(`All ${totalParticipants} results submitted for match ${matchId}. Finalizing match and room.`);

            // Determine the winner based on submitted results
            const { data: allParticipants, error: participantsError } = await supabaseAdmin
              .from('match_participants')
              .select('user_id, placement, stats')
              .eq('match_id', matchId);

            logger.info(`🔍 Determining winner for match ${matchId}`);
            logger.info(`Participants data:`, JSON.stringify(allParticipants, null, 2));
            logger.info(`Participants error:`, participantsError);

            // Force console log for debugging
            console.log(`🔍 CONSOLE LOG - Determining winner for match ${matchId}`);
            console.log(`CONSOLE LOG - Participants:`, allParticipants);

            let winnerId = null;
            if (!participantsError && allParticipants) {
              logger.info(`🔍 Analyzing participants for match ${matchId}:`);
              allParticipants.forEach((p, index) => {
                logger.info(`  Participant ${index + 1}: user_id=${p.user_id}, placement=${p.placement}, stats=${JSON.stringify(p.stats)}`);
              });

              // Find the participant who claimed victory (placement = 1)
              const winner = allParticipants.find(p => p.placement === 1);
              logger.info(`Looking for winner with placement = 1:`, winner);

              if (winner && winner.user_id) {
                winnerId = winner.user_id;
                // Ensure winnerId is not undefined or null
                if (!winnerId || winnerId === 'undefined' || winnerId === undefined) {
                  logger.error(`❌ Winner user_id is invalid: ${winnerId} for match ${matchId}`);
                  winnerId = null;
                } else {
                  logger.info(`✅ Winner determined: ${winnerId} (type: ${typeof winnerId}) for match ${matchId}`);
                  logger.info(`✅ Winner object:`, JSON.stringify(winner, null, 2));

                  // Force console log for debugging
                  console.log(`✅ CONSOLE LOG - Winner determined: ${winnerId} (type: ${typeof winnerId})`);
                  console.log(`CONSOLE LOG - Winner object:`, winner);
                }
              } else {
                // If no one claimed victory, determine winner based on who has the better placement
                logger.warn(`⚠️ No winner found for match ${matchId} - no participant has placement = 1`);
                logger.warn(`Participants placements:`, allParticipants.map(p => ({ user_id: p.user_id, placement: p.placement })));

                // Find participant with best placement (lowest number, excluding null)
                const participantsWithPlacement = allParticipants.filter(p => p.placement !== null);
                if (participantsWithPlacement.length > 0) {
                  const bestParticipant = participantsWithPlacement.sort((a, b) => a.placement - b.placement)[0];
                  winnerId = bestParticipant.user_id;
                  logger.info(`🔄 Winner determined by best placement: ${winnerId} (placement: ${bestParticipant.placement})`);
                } else {
                  // If no one has placement, default to first participant
                  winnerId = allParticipants[0]?.user_id || null;
                  logger.warn(`⚠️ No placements found, defaulting to first participant: ${winnerId}`);
                }
              }
            } else {
              logger.error(`❌ Error getting participants for match ${matchId}:`, participantsError);
            }

            // Ensure winnerId is valid and not the string "undefined"
            if (winnerId === "undefined" || winnerId === undefined || winnerId === null || winnerId === "") {
              logger.warn(`⚠️ Winner ID was invalid (${winnerId}), setting to null for match ${matchId}`);
              winnerId = null;
            } else {
              // Ensure winnerId is a valid UUID string
              if (typeof winnerId !== 'string' || winnerId.length !== 36) {
                logger.warn(`⚠️ Winner ID format invalid (${winnerId}), setting to null for match ${matchId}`);
                winnerId = null;
              }
            }

            // Update match status to completed with winner
            logger.info(`🔄 Updating match ${matchId} with winner: ${winnerId} (type: ${typeof winnerId})`);

            // Force console log for debugging
            console.log(`🔄 CONSOLE LOG - Updating match ${matchId} with winner: ${winnerId} (type: ${typeof winnerId})`);

            const { error: matchUpdateError } = await supabaseAdmin
              .from('matches')
              .update({
                status: 'completed',
                winner_user_id: winnerId,
                finished_at: new Date().toISOString()
              })
              .eq('id', matchId);

            if (matchUpdateError) {
              logger.error(`❌ Error updating match ${matchId}:`, matchUpdateError);
            } else {
              logger.info(`✅ Match ${matchId} updated successfully with winner: ${winnerId}`);
            }

            // Distribute prizes if there's a winner
            if (winnerId && match.entry_fee && allParticipants) {
              try {
                const totalPrize = match.entry_fee * allParticipants.length;

                // Update winner's earnings
                await supabaseAdmin
                  .from('match_participants')
                  .update({ earnings: totalPrize })
                  .eq('match_id', matchId)
                  .eq('user_id', winnerId);

                // Get winner's wallet
                const { data: wallet } = await supabaseAdmin
                  .from('wallets')
                  .select('id')
                  .eq('user_id', winnerId)
                  .single();

                if (wallet) {
                  // Create win transaction
                  await supabaseAdmin
                    .from('transactions')
                    .insert({
                      user_id: winnerId,
                      wallet_id: wallet.id,
                      type: 'win',
                      amount: totalPrize,
                      description: `Match winnings - Victory`,
                      status: 'completed'
                    });

                  logger.info(`Prize of ${totalPrize} distributed to winner ${winnerId} for match ${matchId}`);
                }
              } catch (prizeError) {
                logger.error(`Error distributing prizes for match ${matchId}:`, prizeError);
              }
            }

            // Update room status to finished
            await RoomStateService.updateRoomStatus(match.room_id, 'finished', {
              finished_at: new Date().toISOString()
            });

            // Notify all players that match is completed and redirect them
            io.to(`room:${match.room_id}`).emit('match_completed', {
              message: 'Todos os resultados foram submetidos. A partida foi finalizada.',
              matchId,
              redirectToResults: true
            });

            // Clean up the room after a delay to allow players to see the message
            setTimeout(async () => {
              const cleanupSuccess = await RoomStateService.cleanupFinishedRoom(match.room_id);
              if (cleanupSuccess) {
                logger.info(`Room ${match.room_id} cleaned up successfully after match completion`);
              } else {
                logger.error(`Failed to clean up room ${match.room_id} after match completion`);
              }
            }, 5000); // 5 second delay

            logger.info(`Match ${matchId} and room ${match.room_id} finalized successfully`);
          } else {
            // Não finalizar ainda - aguardar mais resultados
            logger.info(`Match ${matchId}: ${participantsWithResults}/${totalParticipants} results submitted. Waiting for remaining players.`);

            // Notificar outros jogadores que ainda precisam submeter resultado
            if (participantsWithResults < totalParticipants) {
              socket.to(`room:${match.room_id}`).emit('waiting_for_results', {
                message: `${socket.username} submeteu o resultado. Aguardando outros jogadores...`,
                submittedCount: participantsWithResults,
                totalCount: totalParticipants
              });
            }
          }
        }

        socket.emit('match_result_submitted_success', {
          matchId,
          result,
          shouldRedirect: allResultsSubmitted // Only redirect when all results are submitted
        });
        logger.info(`Match result submitted by ${socket.username} for match ${matchId}: ${result}`);

      } catch (error) {
        logger.error('Error submitting match result:', error);
        socket.emit('error', { message: 'Failed to submit match result' });
      }
    });

    // Handle player ready status
    socket.on('player_ready', async (data: { roomId: string; isReady: boolean }) => {
      try {
        const { roomId, isReady } = data;

        // Update player ready status using service
        if (!socket.userId) {
          socket.emit('error', { message: 'User not authenticated' });
          return;
        }

        const success = await RoomStateService.updatePlayerReadyStatus(roomId, socket.userId, isReady);
        if (!success) {
          socket.emit('error', { message: 'Failed to update ready status' });
          return;
        }

        // Check if all players are ready and room is full using service
        const { allReady, roomFull } = await RoomStateService.areAllPlayersReady(roomId);

        // Get current room state
        const roomState = await RoomStateService.getRoomState(roomId);
        if (!roomState) {
          socket.emit('error', { message: 'Room not found' });
          return;
        }

        if (allReady && roomFull && roomState.status === 'waiting') {

          // Update room status to starting using service
          await RoomStateService.updateRoomStatus(roomId, 'starting', {
            started_at: new Date().toISOString()
          });

          // Broadcast game starting to all players in room
          io.to(`room:${roomId}`).emit('game_starting', {
            countdown: 10,
            message: `Todos os ${roomState.participants.length} jogadores estão prontos! Iniciando partida...`
          });

          // Start countdown with database persistence
          let countdown = 10;
          const countdownInterval = setInterval(async () => {
            countdown--;

            if (countdown <= 0) {
              clearInterval(countdownInterval);

              // Create match using service
              const matchId = await RoomStateService.createMatchForRoom(roomId);
              if (!matchId) {
                logger.error(`Failed to create match for room ${roomId}`);
                io.to(`room:${roomId}`).emit('error', { message: 'Failed to start match' });
                return;
              }

              // Update room status to playing using service
              await RoomStateService.updateRoomStatus(roomId, 'playing');

              // Broadcast match started
              io.to(`room:${roomId}`).emit('match_started', {
                matchId,
                message: 'Partida iniciada! Boa sorte!'
              });

              logger.info(`Match ${matchId} started for room ${roomId}`);
            } else {
              // Broadcast countdown update
              io.to(`room:${roomId}`).emit('countdown_update', { countdown });
            }
          }, 1000);

          logger.info(`Game starting in room ${roomId} with ${roomState.participants.length} players`);
        }

        // Broadcast ready status update
        io.to(`room:${roomId}`).emit('player_ready_update', {
          userId: socket.userId,
          username: socket.username,
          isReady
        });

        logger.info(`User ${socket.username} ready status: ${isReady} in room ${roomId}`);
      } catch (error) {
        logger.error('Error updating ready status:', error);
        socket.emit('error', { message: 'Failed to update ready status' });
      }
    });

    // Handle matchmaking
    socket.on('start_matchmaking', async (data: { gameId: string; entryFee: number }) => {
      try {
        const { gameId, entryFee } = data;

        // Add user to matchmaking queue
        const { data: queueEntry, error } = await supabaseAdmin
          .from('matchmaking_queue')
          .insert({
            user_id: socket.userId,
            game_id: gameId,
            entry_fee: entryFee
          })
          .select('*')
          .single();

        if (error || !queueEntry) {
          socket.emit('error', { message: 'Failed to join matchmaking queue' });
          return;
        }

        socket.join('matchmaking');
        socket.emit('matchmaking_started', { queueId: queueEntry.id });

        logger.info(`User ${socket.username} started matchmaking for game ${gameId}`);
      } catch (error) {
        logger.error('Error starting matchmaking:', error);
        socket.emit('error', { message: 'Failed to start matchmaking' });
      }
    });

    // Handle matchmaking cancellation
    socket.on('cancel_matchmaking', async () => {
      try {
        // Remove user from matchmaking queue
        const { error } = await supabaseAdmin
          .from('matchmaking_queue')
          .delete()
          .eq('user_id', socket.userId);

        if (error) {
          socket.emit('error', { message: 'Failed to cancel matchmaking' });
          return;
        }

        socket.leave('matchmaking');
        socket.emit('matchmaking_cancelled');

        logger.info(`User ${socket.username} cancelled matchmaking`);
      } catch (error) {
        logger.error('Error cancelling matchmaking:', error);
        socket.emit('error', { message: 'Failed to cancel matchmaking' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data: { roomId: string }) => {
      socket.to(`room:${data.roomId}`).emit('user_typing', {
        userId: socket.userId,
        username: socket.username
      });
    });

    socket.on('typing_stop', (data: { roomId: string }) => {
      socket.to(`room:${data.roomId}`).emit('user_stopped_typing', {
        userId: socket.userId,
        username: socket.username
      });
    });

    // Handle room state synchronization request
    socket.on('sync_room_state', async (data: { roomId: string }) => {
      try {
        const { roomId } = data;

        // Verify user is participant of the room
        const { data: participant, error } = await supabaseAdmin
          .from('room_participants')
          .select('id')
          .eq('room_id', roomId)
          .eq('user_id', socket.userId)
          .single();

        if (error || !participant) {
          socket.emit('error', { message: 'Not authorized to sync this room' });
          return;
        }

        // Get complete room state from database
        const { data: roomState, error: stateError } = await supabaseAdmin
          .from('game_rooms')
          .select(`
            *,
            room_participants (
              user_id,
              is_ready,
              joined_at,
              users (username, display_name, avatar_url)
            ),
            matches (id, status, started_at, finished_at),
            room_chat_messages (
              id, message, message_type, created_at,
              users (username, display_name, avatar_url)
            )
          `)
          .eq('id', roomId)
          .order('created_at', { foreignTable: 'room_chat_messages', ascending: true })
          .single();

        if (!stateError && roomState) {
          // Send complete room state to the requesting user
          socket.emit('room_state_synced', {
            room: {
              id: roomState.id,
              name: roomState.name,
              status: roomState.status,
              entry_fee: roomState.entry_fee,
              max_players: roomState.max_players,
              current_players: roomState.current_players,
              started_at: roomState.started_at,
              finished_at: roomState.finished_at
            },
            participants: roomState.room_participants,
            currentMatch: roomState.matches?.[0] || null,
            messages: roomState.room_chat_messages || []
          });

          logger.info(`Room state synced for user ${socket.username} in room ${roomId}`);
        } else {
          socket.emit('error', { message: 'Failed to sync room state' });
        }

      } catch (error) {
        logger.error('Error syncing room state:', error);
        socket.emit('error', { message: 'Failed to sync room state' });
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`User ${socket.username} disconnected: ${reason}`);

      // Clean up matchmaking queue if user was in queue
      (async () => {
        try {
          await supabaseAdmin
            .from('matchmaking_queue')
            .delete()
            .eq('user_id', socket.userId);
          logger.info(`Cleaned up matchmaking queue for user ${socket.username}`);
        } catch (error: any) {
          logger.error('Error cleaning up matchmaking queue:', error);
        }
      })();
    });

    // Handle ping/pong for connection health
    socket.on('ping', () => {
      socket.emit('pong');
    });
  });

  // Helper function to send notification to user
  const sendNotificationToUser = (userId: string, notification: any) => {
    io.to(`user:${userId}`).emit('notification', notification);
  };

  // Helper function to broadcast to room
  const broadcastToRoom = (roomId: string, event: string, data: any) => {
    io.to(`room:${roomId}`).emit(event, data);
  };

  // Helper function to broadcast to match
  const broadcastToMatch = (matchId: string, event: string, data: any) => {
    io.to(`match:${matchId}`).emit(event, data);
  };

  // Export helper functions for use in other parts of the application
  return {
    sendNotificationToUser,
    broadcastToRoom,
    broadcastToMatch
  };
};

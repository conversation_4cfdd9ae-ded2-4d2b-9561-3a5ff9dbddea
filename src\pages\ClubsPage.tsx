import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Filter, Users, Trophy, Crown, Star, Plus, ChevronRight, Loader2 } from 'lucide-react';
import { realSocialService, Club as ApiClub } from '../services/realSocialService';

interface Club {
  id: string;
  name: string;
  logo: string;
  game: string;
  members: number;
  level: number;
  xp: number;
  maxXp: number;
  isPublic: boolean;
  isMember?: boolean;
  role?: 'owner' | 'admin' | 'member';
}

export default function ClubsPage() {
  const navigate = useNavigate();
  const [selectedGame, setSelectedGame] = useState<string | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'my-clubs' | 'recommended'>('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Estados para dados da API
  const [apiClubs, setApiClubs] = useState<ApiClub[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carrega dados dos clubes
  useEffect(() => {
    const loadClubsData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando clubes...');

        // Carrega clubes baseado no filtro selecionado
        let clubsData: ApiClub[] = [];

        if (selectedFilter === 'my-clubs') {
          clubsData = await realSocialService.getUserClubs();
        } else {
          clubsData = await realSocialService.getClubs(1, 50, {
            is_public: true
          });
        }

        setApiClubs(clubsData);
        console.log(`✅ ${clubsData.length} clubes carregados`);

      } catch (err) {
        console.error('❌ Erro ao carregar clubes:', err);
        setError('Erro ao carregar clubes. Usando dados offline.');
      } finally {
        setIsLoading(false);
      }
    };

    loadClubsData();
  }, [selectedFilter]);

  const games = [
    { id: 'cs2', name: 'Counter-Strike 2' },
    { id: 'apex', name: 'Apex Legends' },
    { id: 'valorant', name: 'VALORANT' }
  ];

  const clubs: Club[] = [
    {
      id: '1',
      name: 'CS Masters Brasil',
      logo: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      game: 'Counter-Strike 2',
      members: 250,
      level: 42,
      xp: 8500,
      maxXp: 10000,
      isPublic: true,
      isMember: true,
      role: 'admin'
    },
    {
      id: '2',
      name: 'Legends Academy',
      logo: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      game: 'Apex Legends',
      members: 180,
      level: 35,
      xp: 6200,
      maxXp: 8000,
      isPublic: true
    }
  ];

  // Converte dados da API para o formato da interface
  const convertApiClubToClub = (apiClub: ApiClub): Club => ({
    id: apiClub.id,
    name: apiClub.name,
    logo: apiClub.logo_url || 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    game: apiClub.game || 'Geral',
    members: apiClub.members_count,
    level: apiClub.level,
    xp: apiClub.xp,
    maxXp: apiClub.max_members * 100, // Estimativa baseada no número máximo de membros
    isPublic: apiClub.is_public,
    isMember: apiClub.user_role !== undefined,
    role: apiClub.user_role
  });

  // Usa dados da API se disponíveis, senão usa dados mock
  const displayClubs = apiClubs.length > 0
    ? apiClubs.map(convertApiClubToClub)
    : clubs;

  const filteredClubs = displayClubs.filter(club => {
    if (selectedGame && club.game !== games.find(g => g.id === selectedGame)?.name) {
      return false;
    }
    if (selectedFilter === 'my-clubs' && !club.isMember) {
      return false;
    }
    if (searchQuery && !club.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    return true;
  });

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Clubes</h1>
          <p className="text-gray-400">Encontre ou crie um clube para jogar com outros membros</p>
        </div>
        <button
          onClick={() => navigate('/clubs/create')}
          className="bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold px-6 py-3 rounded-xl flex items-center gap-2 hover:opacity-90 transition-opacity shadow-lg"
        >
          <Plus className="w-5 h-5" />
          Criar Clube
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Buscar clubes..."
            className="w-full bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white rounded-xl py-3 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-rose-400"
          />
        </div>

        <div className="flex gap-2">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 p-2 rounded-lg">
            <Filter className="w-5 h-5 text-rose-400" />
          </div>

          {/* Game Filter */}
          {games.map(game => (
            <button
              key={game.id}
              onClick={() => setSelectedGame(selectedGame === game.id ? null : game.id)}
              className={`px-4 py-2 rounded-lg ${
                selectedGame === game.id
                  ? 'bg-gradient-to-r from-rose-400 to-pink-400 text-white shadow-lg shadow-rose-500/20'
                  : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
              }`}
            >
              {game.name}
            </button>
          ))}
        </div>

        {/* View Filter */}
        <div className="flex gap-2">
          <button
            onClick={() => setSelectedFilter('all')}
            className={`px-4 py-2 rounded-lg ${
              selectedFilter === 'all'
                ? 'bg-gradient-to-r from-rose-400 to-pink-400 text-white shadow-lg shadow-rose-500/20'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Todos
          </button>
          <button
            onClick={() => setSelectedFilter('my-clubs')}
            className={`px-4 py-2 rounded-lg ${
              selectedFilter === 'my-clubs'
                ? 'bg-gradient-to-r from-rose-400 to-pink-400 text-white shadow-lg shadow-rose-500/20'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Meus Clubes
          </button>
          <button
            onClick={() => setSelectedFilter('recommended')}
            className={`px-4 py-2 rounded-lg ${
              selectedFilter === 'recommended'
                ? 'bg-gradient-to-r from-rose-400 to-pink-400 text-white shadow-lg shadow-rose-500/20'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Recomendados
          </button>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-xl p-4 text-center">
          <p className="text-yellow-400 text-sm">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Loader2 className="w-8 h-8 text-rose-400 mx-auto mb-4 animate-spin" />
          <h3 className="text-white font-bold text-lg mb-2">Carregando clubes...</h3>
          <p className="text-gray-400">Aguarde enquanto buscamos os clubes disponíveis.</p>
        </div>
      ) : (
        <>
      {/* Clubs Grid */}
      <div className="grid gap-4">
        {filteredClubs.map(club => (
          <div
            key={club.id}
            className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 hover:bg-gray-700/50 cursor-pointer hover:scale-[1.02] hover:shadow-xl hover:shadow-rose-500/10 transition-all duration-300"
            onClick={() => navigate(`/clubs/${club.id}`)}
          >
            <div className="flex items-center gap-4">
              <img
                src={club.logo}
                alt={club.name}
                className="w-16 h-16 rounded-xl object-cover ring-2 ring-white/10"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h3 className="text-white font-semibold">{club.name}</h3>
                  {club.role === 'owner' && (
                    <Crown className="w-4 h-4 text-rose-400" />
                  )}
                  {club.role === 'admin' && (
                    <Star className="w-4 h-4 text-rose-400" />
                  )}
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <span>{club.game}</span>
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {club.members}
                  </span>
                  <span className="flex items-center gap-1">
                    <Trophy className="w-4 h-4 text-yellow-400" />
                    Nível {club.level}
                  </span>
                </div>

                {/* XP Progress Bar */}
                <div className="mt-2 w-48 bg-gray-700/50 backdrop-blur-sm rounded-full h-1.5">
                  <div
                    className="bg-gradient-to-r from-rose-400 to-pink-400 h-1.5 rounded-full"
                    style={{ width: `${(club.xp / club.maxXp) * 100}%` }}
                  />
                </div>
              </div>

              <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
            </div>
          </div>
        ))}

        {filteredClubs.length === 0 && (
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
            <Users className="w-12 h-12 text-gray-600 mx-auto mb-3" />
            <p className="text-gray-400">
              Nenhum clube encontrado.
              <br />
              Que tal criar um novo clube?
            </p>
            <button
              onClick={() => navigate('/clubs/create')}
              className="mt-4 bg-gradient-to-r from-rose-400 to-pink-400 text-white font-semibold px-6 py-2 rounded-lg hover:opacity-90 transition-opacity shadow-lg"
            >
              Criar Clube
            </button>
          </div>
        )}
      </div>
        </>
      )}
    </div>
  );
}
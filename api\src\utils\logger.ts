import winston from 'winston';
import path from 'path';
import config from '../config/environment';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which logs to print based on environment
const level = () => {
  const env = config.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Define format for file logs (without colors)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    level: level(),
    format: format
  }),
  
  // File transport for errors
  new winston.transports.File({
    filename: path.join('logs', 'error.log'),
    level: 'error',
    format: fileFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: path.join('logs', 'combined.log'),
    format: fileFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  })
];

// Create the logger
export const logger = winston.createLogger({
  level: level(),
  levels,
  format: fileFormat,
  transports,
  exitOnError: false
});

// Create a stream object for Morgan HTTP logging
export const morganStream = {
  write: (message: string) => {
    logger.http(message.trim());
  }
};

// Helper functions for structured logging
export const logError = (message: string, error?: Error, meta?: any) => {
  logger.error(message, {
    error: error ? {
      message: error.message,
      stack: error.stack,
      name: error.name
    } : undefined,
    meta
  });
};

export const logWarn = (message: string, meta?: any) => {
  logger.warn(message, { meta });
};

export const logInfo = (message: string, meta?: any) => {
  logger.info(message, { meta });
};

export const logDebug = (message: string, meta?: any) => {
  logger.debug(message, { meta });
};

// Log API requests
export const logApiRequest = (req: any, res: any, responseTime?: number) => {
  const user = req.user;
  const logData = {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: user?.id,
    username: user?.username,
    statusCode: res.statusCode,
    responseTime: responseTime ? `${responseTime}ms` : undefined,
    contentLength: res.get('Content-Length')
  };
  
  if (res.statusCode >= 400) {
    logger.warn('API Request Failed', logData);
  } else {
    logger.info('API Request', logData);
  }
};

// Log database operations
export const logDatabaseOperation = (operation: string, table: string, duration?: number, error?: Error) => {
  const logData = {
    operation,
    table,
    duration: duration ? `${duration}ms` : undefined,
    error: error ? {
      message: error.message,
      stack: error.stack
    } : undefined
  };
  
  if (error) {
    logger.error('Database Operation Failed', logData);
  } else {
    logger.debug('Database Operation', logData);
  }
};

// Log authentication events
export const logAuthEvent = (event: string, userId?: string, username?: string, ip?: string, success: boolean = true) => {
  const logData = {
    event,
    userId,
    username,
    ip,
    success,
    timestamp: new Date().toISOString()
  };
  
  if (success) {
    logger.info('Auth Event', logData);
  } else {
    logger.warn('Auth Event Failed', logData);
  }
};

// Log security events
export const logSecurityEvent = (event: string, severity: 'low' | 'medium' | 'high' | 'critical', details: any) => {
  const logData = {
    event,
    severity,
    details,
    timestamp: new Date().toISOString()
  };
  
  switch (severity) {
    case 'critical':
    case 'high':
      logger.error('Security Event', logData);
      break;
    case 'medium':
      logger.warn('Security Event', logData);
      break;
    case 'low':
    default:
      logger.info('Security Event', logData);
      break;
  }
};

// Log business events
export const logBusinessEvent = (event: string, userId?: string, data?: any) => {
  const logData = {
    event,
    userId,
    data,
    timestamp: new Date().toISOString()
  };
  
  logger.info('Business Event', logData);
};

// Log performance metrics
export const logPerformance = (operation: string, duration: number, metadata?: any) => {
  const logData = {
    operation,
    duration: `${duration}ms`,
    metadata,
    timestamp: new Date().toISOString()
  };
  
  if (duration > 5000) { // Log as warning if operation takes more than 5 seconds
    logger.warn('Slow Operation', logData);
  } else {
    logger.debug('Performance Metric', logData);
  }
};

// Log external API calls
export const logExternalApiCall = (
  service: string,
  endpoint: string,
  method: string,
  statusCode?: number,
  duration?: number,
  error?: Error
) => {
  const logData = {
    service,
    endpoint,
    method,
    statusCode,
    duration: duration ? `${duration}ms` : undefined,
    error: error ? {
      message: error.message,
      stack: error.stack
    } : undefined,
    timestamp: new Date().toISOString()
  };
  
  if (error || (statusCode && statusCode >= 400)) {
    logger.error('External API Call Failed', logData);
  } else {
    logger.info('External API Call', logData);
  }
};

// Create child logger with additional context
export const createChildLogger = (context: any) => {
  return logger.child(context);
};

// Ensure logs directory exists
import fs from 'fs';
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

export default logger;

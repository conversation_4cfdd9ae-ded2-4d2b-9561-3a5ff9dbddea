import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Trophy, RotateCcw, ArrowLeft, Microscope, Film, Trophy as TrophyIcon, Leaf } from 'lucide-react';

// ... (keep all interfaces and constants the same)

export default function WordScramble() {
  // ... (keep all state declarations the same)

  const startSession = () => {
    // Get available words for current session
    const availableWords = SESSIONS[currentSession].words.filter(word => !usedWords.has(word));
    
    if (availableWords.length === 0) {
      // No more words in current session
      if (currentSession < SESSIONS.length - 1) {
        // Move to next session
        setCurrentSession(prev => prev + 1);
        setUsedWords(new Set());
        // Get a word from the new session
        const nextSessionWord = SESSIONS[currentSession + 1].words[
          Math.floor(Math.random() * SESSIONS[currentSession + 1].words.length)
        ];
        setCurrentWord(nextSessionWord);
        setScrambledWord(scrambleWord(nextSessionWord));
        setUsedWords(new Set([nextSessionWord]));
      } else {
        // No more sessions, end game
        endGame();
        return;
      }
    } else {
      // Get a random word from available words
      const randomIndex = Math.floor(Math.random() * availableWords.length);
      const word = availableWords[randomIndex];
      setCurrentWord(word);
      setScrambledWord(scrambleWord(word));
      setUsedWords(prev => new Set([...prev, word]));
    }

    // Reset for new word
    setUserInput('');
    setTimeLeft(ROUND_TIME);

    // Clear existing timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Start new timer
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          startSession();
          return ROUND_TIME;
        }
        return prev - 1;
      });
    }, 1000);

    // Focus input
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // ... (keep all other functions and JSX the same)
}
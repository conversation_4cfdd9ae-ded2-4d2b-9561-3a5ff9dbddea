import { type Config } from 'tailwindcss';

// Color palette
export const colors = {
  primary: {
    gradient: 'bg-gradient-to-r from-rose-400 to-pink-400',
    hover: 'hover:opacity-90',
    text: 'text-rose-400',
    border: 'border-rose-400',
    ring: 'ring-rose-400',
  },
  secondary: {
    gradient: 'bg-gradient-to-r from-fuchsia-500 to-magenta-500',
    hover: 'hover:opacity-90',
    text: 'text-magenta-500',
    border: 'border-magenta-500',
    ring: 'ring-magenta-500',
  },
  surface: {
    primary: 'bg-gray-800/50',
    secondary: 'bg-gray-700/50',
    border: 'border border-white/10',
    hover: 'hover:bg-gray-700/50',
  },
  text: {
    primary: 'text-white',
    secondary: 'text-gray-400',
    contrast: 'text-black',
  },
};

// Component styles
export const components = {
  button: {
    base: 'rounded-lg font-semibold transition-all duration-300 shadow-lg',
    primary: `${colors.primary.gradient} text-white ${colors.primary.hover}`,
    secondary: `${colors.secondary.gradient} text-white ${colors.secondary.hover}`,
    sizes: {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2',
      lg: 'px-6 py-3',
    },
  },
  card: {
    base: `${colors.surface.primary} backdrop-blur-sm rounded-2xl ${colors.surface.border} overflow-hidden`,
    hover: 'hover:scale-[1.02] hover:shadow-xl transition-all duration-300',
    header: 'p-4 border-b border-white/10',
    body: 'p-4',
  },
  input: {
    base: `${colors.surface.primary} text-white rounded-xl py-3 px-4 focus:outline-none focus:ring-2 ${colors.primary.ring} backdrop-blur-sm ${colors.surface.border}`,
    withIcon: 'pl-10 pr-4',
  },
  badge: {
    base: 'rounded-full px-3 py-1.5 text-xs',
    primary: `${colors.surface.secondary} ${colors.text.primary} ${colors.surface.border}`,
  },
  glassmorphism: {
    light: 'backdrop-blur-sm bg-white/10',
    dark: 'backdrop-blur-sm bg-black/40',
  },
};

// Animation variants
export const animations = {
  scale: {
    hover: 'hover:scale-105 transition-transform duration-300',
  },
  fade: {
    enter: 'transition-opacity duration-300 ease-in-out',
    enterFrom: 'opacity-0',
    enterTo: 'opacity-100',
  },
};

// Layout constants
export const layout = {
  maxWidth: 'max-w-7xl',
  containerPadding: 'px-4 sm:px-6 lg:px-8',
  spacing: {
    section: 'space-y-6',
    stack: 'space-y-4',
    inline: 'space-x-4',
  },
};

// Tailwind config extension
export const tailwindTheme: Partial<Config> = {
  theme: {
    extend: {
      colors: {
        rose: {
          400: '#FB7185',
        },
        pink: {
          400: '#F472B6',
        },
        magenta: {
          500: '#FF00FF',
        },
        fuchsia: {
          500: '#D946EF',
        },
      },
    },
  },
};
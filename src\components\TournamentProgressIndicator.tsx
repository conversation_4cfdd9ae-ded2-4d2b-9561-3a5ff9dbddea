import React from 'react';
import { TournamentSeason } from '../types/tournament';
import { CheckCircle } from 'lucide-react';

interface TournamentProgressIndicatorProps {
  seasons: TournamentSeason[];
  currentSeasonId: string;
}

export default function TournamentProgressIndicator({
  seasons,
  currentSeasonId
}: TournamentProgressIndicatorProps) {
  // Ordenar temporadas por ordem
  const sortedSeasons = [...seasons].sort((a, b) => a.order - b.order);
  
  // Calcular porcentagem de conclusão
  const getCompletionPercentage = (): number => {
    const totalSeasons = sortedSeasons.length;
    const completedSeasons = sortedSeasons.filter(s => s.status === 'completed').length;
    const currentSeasonIndex = sortedSeasons.findIndex(s => s.id === currentSeasonId);
    
    // Se a temporada atual estiver em andamento, adicionar progresso parcial
    let currentSeasonProgress = 0;
    if (currentSeasonIndex >= 0 && sortedSeasons[currentSeasonIndex].status === 'in_progress') {
      // Estimar progresso da temporada atual (simplificado para 50%)
      currentSeasonProgress = 0.5;
    }
    
    return Math.round(((completedSeasons + currentSeasonProgress) / totalSeasons) * 100);
  };
  
  // Obter nome da temporada atual
  const getCurrentSeasonName = (): string => {
    const currentSeason = sortedSeasons.find(s => s.id === currentSeasonId);
    return currentSeason ? currentSeason.name : '';
  };
  
  return (
    <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 mb-6">
      <h2 className="text-white text-lg font-bold mb-4">Progresso do Torneio</h2>
      
      <div className="relative pt-1">
        <div className="flex mb-2 items-center justify-between">
          <div>
            <span className="text-xs font-semibold inline-block text-indigo-400">
              {getCompletionPercentage()}% Concluído
            </span>
          </div>
          <div className="text-right">
            <span className="text-xs font-semibold inline-block text-gray-400">
              {getCurrentSeasonName()}
            </span>
          </div>
        </div>
        <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-700">
          <div
            style={{ width: `${getCompletionPercentage()}%` }}
            className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"
          ></div>
        </div>
      </div>
      
      <div className="flex justify-between">
        {sortedSeasons.map((season, index) => (
          <div 
            key={season.id} 
            className={`flex flex-col items-center ${
              season.id === currentSeasonId 
                ? 'scale-110' 
                : ''
            }`}
          >
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              season.status === 'completed' 
                ? 'bg-green-500/20 text-green-400' 
                : season.status === 'in_progress' 
                  ? 'bg-indigo-500/20 text-indigo-400' 
                  : 'bg-gray-700 text-gray-400'
            }`}>
              {season.status === 'completed' 
                ? <CheckCircle className="w-4 h-4" /> 
                : index + 1}
            </div>
            <div className="w-16 text-center">
              <span className="text-xs text-gray-400 mt-1 block truncate">{season.name}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

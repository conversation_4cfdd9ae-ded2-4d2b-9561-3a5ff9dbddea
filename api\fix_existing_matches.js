const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixExistingMatches() {
  console.log('🔧 Corrigindo partidas existentes com problema de colocação...\n');

  try {
    // Buscar todas as partidas completadas com vencedor definido
    console.log('1. Buscando partidas com problemas de colocação...');
    
    const { data: matches, error: matchError } = await supabase
      .from('matches')
      .select(`
        id,
        winner_user_id,
        status,
        finished_at,
        match_participants!inner(
          user_id,
          placement,
          users!inner(username)
        )
      `)
      .eq('status', 'completed')
      .not('winner_user_id', 'is', null)
      .order('finished_at', { ascending: false })
      .limit(20); // Últimas 20 partidas

    if (matchError) {
      console.error('❌ Erro ao buscar partidas:', matchError);
      return;
    }

    console.log(`📊 Analisando ${matches.length} partidas completadas...\n`);

    const problematicMatches = [];

    matches.forEach((match, index) => {
      const winnerParticipant = match.match_participants.find(p => p.user_id === match.winner_user_id);
      const winnerPlacement = winnerParticipant?.placement;
      
      if (winnerPlacement !== 1) {
        problematicMatches.push({
          matchId: match.id,
          winnerId: match.winner_user_id,
          winnerUsername: winnerParticipant?.users?.username || 'Unknown',
          currentPlacement: winnerPlacement,
          finishedAt: match.finished_at
        });
      }
    });

    console.log(`🔍 Encontradas ${problematicMatches.length} partidas com problemas de colocação:\n`);

    if (problematicMatches.length === 0) {
      console.log('✅ Nenhuma partida com problema encontrada!');
      return;
    }

    // Mostrar partidas problemáticas
    problematicMatches.forEach((match, index) => {
      console.log(`${index + 1}. Match: ${match.matchId}`);
      console.log(`   Vencedor: ${match.winnerUsername} (${match.winnerId})`);
      console.log(`   Colocação atual: ${match.currentPlacement} (deveria ser 1)`);
      console.log(`   Finalizada: ${match.finishedAt}`);
      console.log('');
    });

    // Confirmar correção
    console.log(`🔧 Aplicando correção em ${problematicMatches.length} partidas...\n`);

    let successCount = 0;
    let errorCount = 0;

    for (const match of problematicMatches) {
      try {
        console.log(`Corrigindo match ${match.matchId} (${match.winnerUsername})...`);
        
        const { error: updateError } = await supabase
          .from('match_participants')
          .update({ placement: 1 })
          .eq('match_id', match.matchId)
          .eq('user_id', match.winnerId);

        if (updateError) {
          console.error(`❌ Erro ao corrigir match ${match.matchId}:`, updateError);
          errorCount++;
        } else {
          console.log(`✅ Match ${match.matchId} corrigido com sucesso`);
          successCount++;
        }
        
        // Pequena pausa entre correções
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`❌ Erro inesperado ao corrigir match ${match.matchId}:`, error);
        errorCount++;
      }
    }

    console.log(`\n📊 Resultado da correção:`);
    console.log(`✅ Sucessos: ${successCount}`);
    console.log(`❌ Erros: ${errorCount}`);
    console.log(`📈 Total processado: ${successCount + errorCount}`);

    if (successCount > 0) {
      console.log('\n🎉 Correções aplicadas com sucesso!');
      console.log('💡 Agora os vencedores têm colocação 1 e não aparecerão mais como empate');
    }

    // Verificar se as correções funcionaram
    if (successCount > 0) {
      console.log('\n🔍 Verificando correções...');
      
      const { data: verifyMatches, error: verifyError } = await supabase
        .from('matches')
        .select(`
          id,
          winner_user_id,
          match_participants!inner(
            user_id,
            placement,
            users!inner(username)
          )
        `)
        .in('id', problematicMatches.map(m => m.matchId));

      if (verifyError) {
        console.error('❌ Erro ao verificar correções:', verifyError);
      } else {
        console.log('📊 Estado após correções:');
        
        verifyMatches.forEach(match => {
          const winnerParticipant = match.match_participants.find(p => p.user_id === match.winner_user_id);
          const winnerPlacement = winnerParticipant?.placement;
          const winnerUsername = winnerParticipant?.users?.username || 'Unknown';
          
          if (winnerPlacement === 1) {
            console.log(`✅ ${match.id}: ${winnerUsername} tem colocação 1`);
          } else {
            console.log(`❌ ${match.id}: ${winnerUsername} ainda tem colocação ${winnerPlacement}`);
          }
        });
      }
    }

  } catch (error) {
    console.error('❌ Erro durante correção:', error);
  }
}

// Executar correção
fixExistingMatches()
  .then(() => {
    console.log('\n✅ Processo de correção concluído!');
    console.log('🔄 Reinicie o servidor da API para que as mudanças no código entrem em vigor');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

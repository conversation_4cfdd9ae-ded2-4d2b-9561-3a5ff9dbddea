import React from 'react';
import { Trophy, TrendingUp, TrendingDown } from 'lucide-react';

interface RankingCardProps {
  position: number;
  name: string;
  avatar: string;
  points: number;
  trend: 'up' | 'down' | 'stable';
  isUser?: boolean;
}

export default function RankingCard({ position, name, avatar, points, trend, isUser }: RankingCardProps) {
  return (
    <div className={`bg-gray-800 rounded-xl p-4 ${isUser ? 'border-2 border-yellow-400' : ''}`}>
      <div className="flex items-center gap-4">
        <span className={`text-2xl font-bold ${
          position === 1 ? 'text-yellow-400' :
          position === 2 ? 'text-gray-300' :
          position === 3 ? 'text-yellow-700' :
          'text-gray-400'
        }`}>
          {position === 1 ? <Trophy className="w-6 h-6" /> : position}
        </span>
        <div className="flex items-center gap-3 flex-1">
          <img
            src={avatar}
            alt={name}
            className="w-12 h-12 rounded-full object-cover"
          />
          <div>
            <h3 className="text-white font-semibold">{name}</h3>
            <p className="text-sm text-gray-400">{points.toLocaleString()} pts</p>
          </div>
        </div>
        {trend === 'up' && <TrendingUp className="w-5 h-5 text-green-500" />}
        {trend === 'down' && <TrendingDown className="w-5 h-5 text-red-500" />}
      </div>
    </div>
  );
}
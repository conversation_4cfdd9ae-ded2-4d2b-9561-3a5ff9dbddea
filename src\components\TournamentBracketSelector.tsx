import React from 'react';
import { TournamentBracket } from '../types/tournament';
import { Trophy, Users } from 'lucide-react';

interface TournamentBracketSelectorProps {
  brackets: TournamentBracket[];
  activeBracket: string;
  onBracketChange: (bracketId: string) => void;
  userBracketId?: string;
}

export default function TournamentBracketSelector({
  brackets,
  activeBracket,
  onBracketChange,
  userBracketId
}: TournamentBracketSelectorProps) {
  // Se houver apenas uma subchave, não exibir o seletor
  if (brackets.length <= 1) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-white text-base font-semibold flex items-center gap-2">
          <Users className="w-4 h-4 text-indigo-400" />
          Subchaves
          <span className="text-gray-400 text-sm font-normal">
            ({brackets.length})
          </span>
        </h3>

        {/* Exibir o nome do grupo selecionado */}
        {brackets.find(b => b.id === activeBracket) && (
          <div className="bg-indigo-500/20 text-indigo-400 px-3 py-1 rounded-lg text-sm">
            Grupo selecionado: <span className="font-medium">{brackets.find(b => b.id === activeBracket)?.name}</span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
        {brackets.map(bracket => (
          <button
            key={bracket.id}
            onClick={() => onBracketChange(bracket.id)}
            className={`p-2 rounded-lg text-center text-sm transition-all ${
              activeBracket === bracket.id
                ? 'bg-indigo-500/20 text-indigo-400 border border-indigo-500/30'
                : userBracketId === bracket.id
                  ? 'bg-purple-500/10 text-purple-400 border border-purple-500/20 hover:bg-purple-500/20'
                  : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'
            }`}
          >
            <div className="flex items-center justify-center gap-1">
              {bracket.name}
              {bracket.winnerId && (
                <Trophy className="w-3 h-3 text-yellow-400" />
              )}
              {userBracketId === bracket.id && (
                <span className="w-2 h-2 rounded-full bg-purple-400"></span>
              )}
            </div>

            <div className="text-xs text-gray-500 mt-1">
              {bracket.participants.length} jogadores
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}

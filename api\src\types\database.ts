// Database types generated from Supabase schema
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          username: string;
          display_name: string;
          avatar_url: string | null;
          status: 'active' | 'inactive' | 'banned' | 'suspended';
          email_verified: boolean;
          phone: string | null;
          birth_date: string | null;
          country: string | null;
          timezone: string;
          language: string;
          password_hash: string | null;
          created_at: string;
          updated_at: string;
          last_login_at: string | null;
        };
        Insert: {
          id?: string;
          email: string;
          username: string;
          display_name: string;
          avatar_url?: string | null;
          status?: 'active' | 'inactive' | 'banned' | 'suspended';
          email_verified?: boolean;
          phone?: string | null;
          birth_date?: string | null;
          country?: string | null;
          timezone?: string;
          language?: string;
          password_hash?: string | null;
          created_at?: string;
          updated_at?: string;
          last_login_at?: string | null;
        };
        Update: {
          id?: string;
          email?: string;
          username?: string;
          display_name?: string;
          avatar_url?: string | null;
          status?: 'active' | 'inactive' | 'banned' | 'suspended';
          email_verified?: boolean;
          phone?: string | null;
          birth_date?: string | null;
          country?: string | null;
          timezone?: string;
          language?: string;
          password_hash?: string | null;
          created_at?: string;
          updated_at?: string;
          last_login_at?: string | null;
        };
      };
      user_profiles: {
        Row: {
          user_id: string;
          bio: string | null;
          favorite_games: string[] | null;
          gaming_experience: string | null;
          preferred_game_modes: string[] | null;
          social_links: any;
          privacy_settings: any;
          notification_preferences: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          bio?: string | null;
          favorite_games?: string[] | null;
          gaming_experience?: string | null;
          preferred_game_modes?: string[] | null;
          social_links?: any;
          privacy_settings?: any;
          notification_preferences?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          user_id?: string;
          bio?: string | null;
          favorite_games?: string[] | null;
          gaming_experience?: string | null;
          preferred_game_modes?: string[] | null;
          social_links?: any;
          privacy_settings?: any;
          notification_preferences?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_stats: {
        Row: {
          user_id: string;
          total_matches: number;
          total_wins: number;
          total_losses: number;
          total_draws: number;
          win_rate: number;
          total_earnings: number;
          total_spent: number;
          current_streak: number;
          best_streak: number;
          ranking_points: number;
          level: number;
          experience_points: number;
          achievements_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          total_matches?: number;
          total_wins?: number;
          total_losses?: number;
          total_draws?: number;
          win_rate?: number;
          total_earnings?: number;
          total_spent?: number;
          current_streak?: number;
          best_streak?: number;
          ranking_points?: number;
          level?: number;
          experience_points?: number;
          achievements_count?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          user_id?: string;
          total_matches?: number;
          total_wins?: number;
          total_losses?: number;
          total_draws?: number;
          win_rate?: number;
          total_earnings?: number;
          total_spent?: number;
          current_streak?: number;
          best_streak?: number;
          ranking_points?: number;
          level?: number;
          experience_points?: number;
          achievements_count?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      wallets: {
        Row: {
          id: string;
          user_id: string;
          balance: number;
          frozen_balance: number;
          total_deposited: number;
          total_withdrawn: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          balance?: number;
          frozen_balance?: number;
          total_deposited?: number;
          total_withdrawn?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          balance?: number;
          frozen_balance?: number;
          total_deposited?: number;
          total_withdrawn?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      transactions: {
        Row: {
          id: string;
          user_id: string;
          wallet_id: string;
          type: 'deposit' | 'withdrawal' | 'bet' | 'win' | 'commission' | 'refund';
          amount: number;
          status: 'pending' | 'completed' | 'failed' | 'cancelled';
          payment_method: 'pix' | 'credit_card' | 'bank_transfer' | null;
          external_transaction_id: string | null;
          description: string | null;
          metadata: any;
          processed_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          wallet_id: string;
          type: 'deposit' | 'withdrawal' | 'bet' | 'win' | 'commission' | 'refund';
          amount: number;
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
          payment_method?: 'pix' | 'credit_card' | 'bank_transfer' | null;
          external_transaction_id?: string | null;
          description?: string | null;
          metadata?: any;
          processed_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          wallet_id?: string;
          type?: 'deposit' | 'withdrawal' | 'bet' | 'win' | 'commission' | 'refund';
          amount?: number;
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
          payment_method?: 'pix' | 'credit_card' | 'bank_transfer' | null;
          external_transaction_id?: string | null;
          description?: string | null;
          metadata?: any;
          processed_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      games: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          type: 'internal' | 'external';
          image_url: string | null;
          icon_url: string | null;
          is_active: boolean;
          min_players: number;
          max_players: number;
          default_entry_fee: number;
          supported_platforms: string[] | null;
          game_modes: string[] | null;
          settings_schema: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          type: 'internal' | 'external';
          image_url?: string | null;
          icon_url?: string | null;
          is_active?: boolean;
          min_players?: number;
          max_players?: number;
          default_entry_fee?: number;
          supported_platforms?: string[] | null;
          game_modes?: string[] | null;
          settings_schema?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          type?: 'internal' | 'external';
          image_url?: string | null;
          icon_url?: string | null;
          is_active?: boolean;
          min_players?: number;
          max_players?: number;
          default_entry_fee?: number;
          supported_platforms?: string[] | null;
          game_modes?: string[] | null;
          settings_schema?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      // Add more table types as needed...
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      user_status: 'active' | 'inactive' | 'banned' | 'suspended';
      transaction_type: 'deposit' | 'withdrawal' | 'bet' | 'win' | 'commission' | 'refund';
      transaction_status: 'pending' | 'completed' | 'failed' | 'cancelled';
      payment_method: 'pix' | 'credit_card' | 'bank_transfer';
      match_status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'disputed';
      tournament_status: 'upcoming' | 'registration_open' | 'in_progress' | 'completed' | 'cancelled';
      game_type: 'internal' | 'external';
      room_status: 'waiting' | 'starting' | 'playing' | 'finished';
      friend_request_status: 'pending' | 'accepted' | 'declined';
      notification_type: 'friend_request' | 'match_found' | 'tournament_update' | 'system' | 'achievement';
    };
  };
}

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Timer, Users, X, Loader2 } from 'lucide-react';
import { useUser } from '../contexts/UserContext';
import { realMatchService } from '../services/realMatchService';

interface Player {
  id: string;
  name: string;
  avatar: string;
  rating: number;
}

export default function MatchmakingPage() {
  const { gameId } = useParams();
  const navigate = useNavigate();
  const { user } = useUser();
  const [searchTime, setSearchTime] = useState(0);
  const [players, setPlayers] = useState<Player[]>([
    {
      id: '1',
      name: user.name || '<PERSON>',
      avatar: user.avatar || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      rating: 1200
    }
  ]);

  // Estados para API real
  const [matchmakingId, setMatchmakingId] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Inicia matchmaking real
  useEffect(() => {
    const startMatchmaking = async () => {
      if (!gameId) {
        setError('ID do jogo não fornecido');
        return;
      }

      try {
        setIsSearching(true);
        setError(null);

        console.log(`🔍 Iniciando matchmaking para ${gameId}...`);

        const result = await realMatchService.startMatchmaking({
          game_id: gameId,
          bet_amount: 50, // Valor padrão, pode ser configurável
          preferred_rating_range: {
            min: 1000,
            max: 1500
          }
        });

        if (result.success && result.matchmaking_id) {
          setMatchmakingId(result.matchmaking_id);
          console.log(`✅ Matchmaking iniciado: ${result.matchmaking_id}`);

          // Simula encontrar jogadores (enquanto não há WebSocket real)
          simulateFindingPlayers();
        } else {
          console.error('❌ Erro no matchmaking:', result.error);
          setError(result.error || 'Erro ao iniciar matchmaking');
          // Fallback para simulação
          simulateFindingPlayers();
        }
      } catch (err) {
        console.error('❌ Erro ao iniciar matchmaking:', err);
        setError('Erro de conexão. Usando modo offline.');
        // Fallback para simulação
        simulateFindingPlayers();
      }
    };

    const simulateFindingPlayers = () => {
      const potentialPlayers = [
        {
          id: '2',
          name: 'Ana Silva',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          rating: 1250
        },
        {
          id: '3',
          name: 'Lucas Oliveira',
          avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          rating: 1180
        },
        {
          id: '4',
          name: 'Maria Santos',
          avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          rating: 1220
        }
      ];

      const intervals = [3000, 7000, 12000];
      const timeouts: NodeJS.Timeout[] = [];

      potentialPlayers.forEach((player, index) => {
        const timeout = setTimeout(() => {
          setPlayers(prev => [...prev, player]);

          if (index === potentialPlayers.length - 1) {
            setTimeout(() => {
              navigate(`/games/${gameId}/rooms/matchmaking`);
            }, 1500);
          }
        }, intervals[index]);

        timeouts.push(timeout);
      });

      return timeouts;
    };

    const timeouts = startMatchmaking();

    // Timer para o tempo de busca
    const searchTimer = setInterval(() => {
      setSearchTime(prev => prev + 1);
    }, 1000);

    // Cleanup
    return () => {
      if (Array.isArray(timeouts)) {
        timeouts.forEach(timeout => clearTimeout(timeout));
      }
      clearInterval(searchTimer);
    };
  }, [gameId, navigate]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCancelMatchmaking = async () => {
    try {
      if (matchmakingId) {
        console.log(`❌ Cancelando matchmaking ${matchmakingId}...`);

        const result = await realMatchService.cancelMatchmaking(matchmakingId);

        if (result.success) {
          console.log('✅ Matchmaking cancelado com sucesso');
        } else {
          console.warn('⚠️ Erro ao cancelar matchmaking:', result.error);
        }
      }
    } catch (error) {
      console.error('❌ Erro ao cancelar matchmaking:', error);
    } finally {
      navigate(`/external-games/${gameId}`);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-900 z-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        <div className="text-center space-y-6">
          {/* Error State */}
          {error && (
            <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-xl p-4 mb-4">
              <p className="text-yellow-400 text-sm">{error}</p>
            </div>
          )}

          <div className="animate-spin rounded-full h-16 w-16 border-4 border-yellow-400 border-t-transparent mx-auto" />

          <div>
            <h2 className="text-2xl font-bold text-white mb-2">Procurando Partida</h2>
            <p className="text-gray-400">
              {isSearching ? 'Conectando com a API...' : 'Aguarde enquanto encontramos os melhores jogadores'}
            </p>
            {matchmakingId && (
              <p className="text-gray-500 text-xs mt-1">ID: {matchmakingId}</p>
            )}
          </div>

          <div className="flex items-center justify-center gap-6 text-gray-400">
            <div className="flex items-center gap-2">
              <Timer className="w-5 h-5" />
              <span>{formatTime(searchTime)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              <span>{players.length}/4</span>
            </div>
          </div>

          {/* Players Found */}
          <div className="bg-gray-800 rounded-xl p-4">
            <h3 className="text-white font-semibold mb-3">Jogadores Encontrados</h3>
            <div className="space-y-3">
              {players.map(player => (
                <div key={player.id} className="flex items-center gap-3 animate-fadeIn">
                  <img
                    src={player.avatar}
                    alt={player.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <p className="text-white">{player.name}</p>
                    <p className="text-gray-400 text-sm">Rating: {player.rating}</p>
                  </div>
                </div>
              ))}
              {Array(4 - players.length).fill(0).map((_, i) => (
                <div key={`empty-${i}`} className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-gray-700 animate-pulse" />
                  <div className="flex-1">
                    <div className="h-4 bg-gray-700 rounded w-24 animate-pulse mb-1" />
                    <div className="h-3 bg-gray-700 rounded w-16 animate-pulse" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          <button
            onClick={handleCancelMatchmaking}
            className="bg-gray-800 text-white px-6 py-3 rounded-xl font-semibold flex items-center gap-2 mx-auto hover:bg-gray-700"
          >
            <X className="w-5 h-5" />
            Cancelar Busca
          </button>
        </div>
      </div>
    </div>
  );
}
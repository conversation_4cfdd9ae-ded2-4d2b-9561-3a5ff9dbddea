import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Bell, 
  Moon, 
  Sun, 
  Lock, 
  CreditCard, 
  User, 
  HelpCircle, 
  LogOut,
  ChevronRight,
  ArrowLeft
} from 'lucide-react';

export default function SettingsPage() {
  const navigate = useNavigate();
  const [darkMode, setDarkMode] = useState(true);
  const [notifications, setNotifications] = useState(true);

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button 
          onClick={() => navigate('/profile')}
          className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Voltar</span>
        </button>
        <h1 className="text-2xl font-bold text-white">Configurações</h1>
      </div>

      {/* Settings Sections */}
      <div className="space-y-6">
        {/* Account Settings */}
        <section>
          <h2 className="text-white text-lg font-bold mb-3">Conta</h2>
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl divide-y divide-gray-700">
            <div className="p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <User className="w-5 h-5 text-yellow-400" />
                <span className="text-white">Informações Pessoais</span>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
            <div className="p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Lock className="w-5 h-5 text-yellow-400" />
                <span className="text-white">Segurança</span>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
            <div className="p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <CreditCard className="w-5 h-5 text-yellow-400" />
                <span className="text-white">Métodos de Pagamento</span>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        </section>

        {/* App Settings */}
        <section>
          <h2 className="text-white text-lg font-bold mb-3">Aplicativo</h2>
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl divide-y divide-gray-700">
            <div className="p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Bell className="w-5 h-5 text-yellow-400" />
                <span className="text-white">Notificações</span>
              </div>
              <div className="relative inline-block w-12 h-6">
                <input
                  type="checkbox"
                  className="opacity-0 w-0 h-0"
                  checked={notifications}
                  onChange={() => setNotifications(!notifications)}
                  id="notifications-toggle"
                />
                <label
                  htmlFor="notifications-toggle"
                  className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full transition-colors ${
                    notifications ? 'bg-rose-400' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`absolute h-5 w-5 rounded-full bg-white transition-transform ${
                      notifications ? 'translate-x-6' : 'translate-x-1'
                    } top-0.5`}
                  ></span>
                </label>
              </div>
            </div>
            <div className="p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                {darkMode ? (
                  <Moon className="w-5 h-5 text-yellow-400" />
                ) : (
                  <Sun className="w-5 h-5 text-yellow-400" />
                )}
                <span className="text-white">Tema Escuro</span>
              </div>
              <div className="relative inline-block w-12 h-6">
                <input
                  type="checkbox"
                  className="opacity-0 w-0 h-0"
                  checked={darkMode}
                  onChange={() => setDarkMode(!darkMode)}
                  id="darkmode-toggle"
                />
                <label
                  htmlFor="darkmode-toggle"
                  className={`absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full transition-colors ${
                    darkMode ? 'bg-rose-400' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`absolute h-5 w-5 rounded-full bg-white transition-transform ${
                      darkMode ? 'translate-x-6' : 'translate-x-1'
                    } top-0.5`}
                  ></span>
                </label>
              </div>
            </div>
            <div className="p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <HelpCircle className="w-5 h-5 text-yellow-400" />
                <span className="text-white">Ajuda e Suporte</span>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        </section>

        {/* Logout Button */}
        <button className="w-full bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 flex items-center justify-center gap-2 text-rose-400 hover:bg-gray-700/50">
          <LogOut className="w-5 h-5" />
          <span>Sair da Conta</span>
        </button>
      </div>
    </div>
  );
}

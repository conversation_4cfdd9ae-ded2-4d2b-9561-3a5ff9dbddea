import { Router } from 'express';
import {
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  updateNotificationSettings,
  getNotificationSettings
} from '../controllers/notificationController';
import { validate, schemas } from '../middleware/validation';
import { authenticate } from '../middleware/auth';

const router = Router();

/**
 * @route   GET /api/notifications/settings
 * @desc    Get notification settings
 * @access  Private
 */
router.get('/settings',
  authenticate,
  getNotificationSettings
);

/**
 * @route   PUT /api/notifications/read-all
 * @desc    Mark all notifications as read
 * @access  Private
 */
router.put('/read-all',
  authenticate,
  markAllNotificationsAsRead
);

/**
 * @route   GET /api/notifications
 * @desc    Get user notifications
 * @access  Private
 */
router.get('/',
  authenticate,
  validate(schemas.pagination, 'query'),
  getNotifications
);

/**
 * @route   PUT /api/notifications/:id/read
 * @desc    Mark notification as read
 * @access  Private
 */
router.put('/:id/read',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  markNotificationAsRead
);

/**
 * @route   DELETE /api/notifications/:id
 * @desc    Delete notification
 * @access  Private
 */
router.delete('/:id',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  deleteNotification
);

/**
 * @route   POST /api/notifications/settings
 * @desc    Update notification settings
 * @access  Private
 */
router.post('/settings',
  authenticate,
  validate(schemas.notificationSettings),
  updateNotificationSettings
);

export default router;

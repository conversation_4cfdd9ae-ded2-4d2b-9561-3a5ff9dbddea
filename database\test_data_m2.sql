-- <PERSON>ript para adicionar dados de teste para o usuário <EMAIL>
-- Execute este script no SQL Editor do Supabase

-- Primeiro, vamos buscar o ID do usuário <EMAIL>
DO $$
DECLARE
    user_m2_id UUID;
    game1_id UUID;
    game2_id UUID;
    game3_id UUID;
    match1_id UUID;
    match2_id UUID;
    match3_id UUID;
    match4_id UUID;
    match5_id UUID;
BEGIN
    -- Buscar o ID do usuário <EMAIL>
    SELECT id INTO user_m2_id FROM users WHERE email = '<EMAIL>';

    IF user_m2_id IS NULL THEN
        RAISE EXCEPTION 'Usuário <EMAIL> não encontrado';
    END IF;

    -- <PERSON><PERSON>r alguns jogos se não existirem
    INSERT INTO games (id, name, slug, description, type, is_active, min_players, max_players, default_entry_fee)
    VALUES
        (uuid_generate_v4(), 'Foguete Espacial', 'foguete-espacial', 'Jogo de estratégia espacial', 'internal', true, 2, 8, 10.00),
        (uuid_generate_v4(), 'Tetris Battle', 'tetris-battle', 'Batalha de Tetris em tempo real', 'internal', true, 2, 6, 15.00),
        (uuid_generate_v4(), 'Quiz Master', 'quiz-master', 'Quiz de conhecimentos gerais', 'internal', true, 2, 10, 5.00)
    ON CONFLICT (slug) DO NOTHING;

    -- Buscar IDs dos jogos
    SELECT id INTO game1_id FROM games WHERE slug = 'foguete-espacial';
    SELECT id INTO game2_id FROM games WHERE slug = 'tetris-battle';
    SELECT id INTO game3_id FROM games WHERE slug = 'quiz-master';

    -- Criar partidas de teste
    -- Partida 1: Vitória no Foguete Espacial
    INSERT INTO matches (id, game_id, match_type, status, entry_fee, total_prize, started_at, finished_at, winner_user_id, result_data, created_at)
    VALUES (
        uuid_generate_v4(),
        game1_id,
        '1v1',
        'completed',
        10.00,
        20.00,
        NOW() - INTERVAL '2 hours',
        NOW() - INTERVAL '1 hour 30 minutes',
        user_m2_id,
        '{"total_participants": 8, "duration_minutes": 25}',
        NOW() - INTERVAL '3 hours'
    ) RETURNING id INTO match1_id;

    -- Adicionar participante na partida 1
    INSERT INTO match_participants (match_id, user_id, score, placement, earnings)
    VALUES (match1_id, user_m2_id, 1250, 1, 14.00);

    -- Partida 2: Derrota no Tetris Battle
    INSERT INTO matches (id, game_id, match_type, status, entry_fee, total_prize, started_at, finished_at, result_data, created_at)
    VALUES (
        uuid_generate_v4(),
        game2_id,
        '1v1',
        'completed',
        15.00,
        90.00,
        NOW() - INTERVAL '1 day 3 hours',
        NOW() - INTERVAL '1 day 2 hours 45 minutes',
        '{"total_participants": 6, "duration_minutes": 18}',
        NOW() - INTERVAL '1 day 4 hours'
    ) RETURNING id INTO match2_id;

    -- Adicionar participante na partida 2
    INSERT INTO match_participants (match_id, user_id, score, placement, earnings)
    VALUES (match2_id, user_m2_id, 850, 4, 3.00);

    -- Partida 3: Vitória no Quiz Master
    INSERT INTO matches (id, game_id, match_type, status, entry_fee, total_prize, started_at, finished_at, winner_user_id, result_data, created_at)
    VALUES (
        uuid_generate_v4(),
        game3_id,
        'tournament',
        'completed',
        5.00,
        20.00,
        NOW() - INTERVAL '2 days 1 hour',
        NOW() - INTERVAL '2 days 30 minutes',
        user_m2_id,
        '{"total_participants": 4, "duration_minutes": 12}',
        NOW() - INTERVAL '2 days 2 hours'
    ) RETURNING id INTO match3_id;

    -- Adicionar participante na partida 3
    INSERT INTO match_participants (match_id, user_id, score, placement, earnings)
    VALUES (match3_id, user_m2_id, 2100, 1, 14.00);

    -- Partida 4: Vitória no Foguete Espacial (mais antiga)
    INSERT INTO matches (id, game_id, match_type, status, entry_fee, total_prize, started_at, finished_at, winner_user_id, result_data, created_at)
    VALUES (
        uuid_generate_v4(),
        game1_id,
        '1v1',
        'completed',
        10.00,
        40.00,
        NOW() - INTERVAL '3 days 2 hours',
        NOW() - INTERVAL '3 days 1 hour 45 minutes',
        user_m2_id,
        '{"total_participants": 4, "duration_minutes": 22}',
        NOW() - INTERVAL '3 days 3 hours'
    ) RETURNING id INTO match4_id;

    -- Adicionar participante na partida 4
    INSERT INTO match_participants (match_id, user_id, score, placement, earnings)
    VALUES (match4_id, user_m2_id, 1800, 1, 28.00);

    -- Partida 5: Derrota no Tetris Battle (mais antiga)
    INSERT INTO matches (id, game_id, match_type, status, entry_fee, total_prize, started_at, finished_at, result_data, created_at)
    VALUES (
        uuid_generate_v4(),
        game2_id,
        '1v1',
        'completed',
        15.00,
        30.00,
        NOW() - INTERVAL '4 days 1 hour',
        NOW() - INTERVAL '4 days 45 minutes',
        '{"total_participants": 2, "duration_minutes": 15}',
        NOW() - INTERVAL '4 days 2 hours'
    ) RETURNING id INTO match5_id;

    -- Adicionar participante na partida 5
    INSERT INTO match_participants (match_id, user_id, score, placement, earnings)
    VALUES (match5_id, user_m2_id, 750, 2, 9.00);

    -- Atualizar estatísticas do usuário
    UPDATE user_stats
    SET
        total_matches = 5,
        total_wins = 3,
        total_losses = 2,
        total_draws = 0,
        win_rate = 60.00,
        total_earnings = 68.00,
        current_streak = 1,
        best_streak = 2,
        achievements_count = 3,
        updated_at = NOW()
    WHERE user_id = user_m2_id;

    -- Adicionar algumas transações de ganhos
    INSERT INTO transactions (user_id, wallet_id, type, amount, status, description, processed_at, created_at)
    SELECT
        user_m2_id,
        w.id,
        'win',
        14.00,
        'completed',
        'Vitória - Foguete Espacial',
        NOW() - INTERVAL '1 hour 30 minutes',
        NOW() - INTERVAL '1 hour 30 minutes'
    FROM wallets w WHERE w.user_id = user_m2_id;

    INSERT INTO transactions (user_id, wallet_id, type, amount, status, description, processed_at, created_at)
    SELECT
        user_m2_id,
        w.id,
        'win',
        14.00,
        'completed',
        'Vitória - Quiz Master',
        NOW() - INTERVAL '2 days 30 minutes',
        NOW() - INTERVAL '2 days 30 minutes'
    FROM wallets w WHERE w.user_id = user_m2_id;

    INSERT INTO transactions (user_id, wallet_id, type, amount, status, description, processed_at, created_at)
    SELECT
        user_m2_id,
        w.id,
        'win',
        28.00,
        'completed',
        'Vitória - Foguete Espacial',
        NOW() - INTERVAL '3 days 1 hour 45 minutes',
        NOW() - INTERVAL '3 days 1 hour 45 minutes'
    FROM wallets w WHERE w.user_id = user_m2_id;

    RAISE NOTICE 'Dados de teste adicionados com sucesso para o usuário <EMAIL>!';
    RAISE NOTICE 'Partidas criadas: 5 (3 vitórias, 2 derrotas)';
    RAISE NOTICE 'Transações de ganhos: 3';
    RAISE NOTICE 'Estatísticas atualizadas';

END $$;

-- ============================================
-- SCRIPT ALTERNATIVO (caso o acima não funcione)
-- Execute os comandos abaixo um por um:
-- ============================================

-- 1. Primeiro, busque o ID do usuário:
-- SELECT id FROM users WHERE email = '<EMAIL>';

-- 2. Substitua 'USER_ID_AQUI' pelo ID retornado e execute:

/*
-- Criar jogos (se não existirem)
INSERT INTO games (name, slug, description, type, is_active, min_players, max_players, default_entry_fee)
VALUES
    ('Foguete Espacial', 'foguete-espacial', 'Jogo de estratégia espacial', 'internal', true, 2, 8, 10.00),
    ('Tetris Battle', 'tetris-battle', 'Batalha de Tetris em tempo real', 'internal', true, 2, 6, 15.00),
    ('Quiz Master', 'quiz-master', 'Quiz de conhecimentos gerais', 'internal', true, 2, 10, 5.00)
ON CONFLICT (slug) DO NOTHING;

-- Buscar IDs dos jogos:
-- SELECT id, name FROM games WHERE slug IN ('foguete-espacial', 'tetris-battle', 'quiz-master');

-- Criar partida 1 (substitua GAME_ID_1 pelo ID do Foguete Espacial e USER_ID pelo ID do usuário):
INSERT INTO matches (game_id, match_type, status, entry_fee, prize_pool, started_at, finished_at, winner_user_id, result_data, created_at)
VALUES (
    'GAME_ID_1',
    '1v1',
    'completed',
    10.00,
    20.00,
    NOW() - INTERVAL '2 hours',
    NOW() - INTERVAL '1 hour 30 minutes',
    'USER_ID_AQUI',
    '{"total_participants": 8, "duration_minutes": 25}',
    NOW() - INTERVAL '3 hours'
);

-- Buscar ID da partida criada:
-- SELECT id FROM matches ORDER BY created_at DESC LIMIT 1;

-- Adicionar participante (substitua MATCH_ID pelo ID da partida e USER_ID pelo ID do usuário):
INSERT INTO match_participants (match_id, user_id, score, placement, earnings)
VALUES ('MATCH_ID', 'USER_ID_AQUI', 1250, 1, 14.00);

-- Repetir o processo para mais partidas...

-- Atualizar estatísticas do usuário:
UPDATE user_stats
SET
    total_matches = 5,
    total_wins = 3,
    total_losses = 2,
    win_rate = 60.00,
    total_earnings = 68.00,
    current_streak = 1,
    best_streak = 2,
    achievements_count = 3,
    updated_at = NOW()
WHERE user_id = 'USER_ID_AQUI';
*/

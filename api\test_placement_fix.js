const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testPlacementFix() {
  console.log('🧪 Testando correção de colocação do vencedor...\n');

  try {
    // Buscar uma partida recente onde o vencedor não tem colocação 1
    console.log('1. Buscando partidas com problema de colocação...');
    
    const { data: problematicMatches, error: matchError } = await supabase
      .from('matches')
      .select(`
        id,
        winner_user_id,
        status,
        match_participants!inner(
          user_id,
          placement,
          users!inner(username)
        )
      `)
      .eq('status', 'completed')
      .not('winner_user_id', 'is', null)
      .order('finished_at', { ascending: false })
      .limit(5);

    if (matchError) {
      console.error('❌ Erro ao buscar partidas:', matchError);
      return;
    }

    console.log(`📊 Analisando ${problematicMatches.length} partidas completadas...\n`);

    let foundProblem = false;
    let testMatchId = null;
    let testWinnerId = null;

    problematicMatches.forEach((match, index) => {
      console.log(`${index + 1}. Match ID: ${match.id}`);
      console.log(`   Vencedor: ${match.winner_user_id}`);
      
      const winnerParticipant = match.match_participants.find(p => p.user_id === match.winner_user_id);
      const winnerPlacement = winnerParticipant?.placement;
      
      console.log(`   Participantes:`);
      match.match_participants.forEach(p => {
        const isWinner = p.user_id === match.winner_user_id;
        console.log(`     ${isWinner ? '🏆' : '  '} ${p.users.username}: colocação ${p.placement}`);
      });
      
      if (winnerPlacement !== 1) {
        console.log(`   ❌ PROBLEMA: Vencedor tem colocação ${winnerPlacement} (deveria ser 1)`);
        if (!foundProblem) {
          foundProblem = true;
          testMatchId = match.id;
          testWinnerId = match.winner_user_id;
        }
      } else {
        console.log(`   ✅ OK: Vencedor tem colocação 1`);
      }
      console.log('');
    });

    if (!foundProblem) {
      console.log('✅ Nenhum problema de colocação encontrado nas partidas recentes!');
      console.log('💡 Vou criar um cenário de teste simulado...\n');
      
      // Simular o problema criando uma partida de teste
      console.log('2. Criando cenário de teste...');
      
      // Buscar dois usuários para teste
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, username')
        .limit(2);

      if (usersError || !users || users.length < 2) {
        console.error('❌ Erro ao buscar usuários para teste:', usersError);
        return;
      }

      const user1 = users[0];
      const user2 = users[1];
      
      console.log(`👥 Usuários de teste: ${user1.username} vs ${user2.username}`);
      
      // Criar partida de teste
      const { data: testMatch, error: createMatchError } = await supabase
        .from('matches')
        .insert({
          game_id: '550e8400-e29b-41d4-a716-************', // UUID fictício
          match_type: 'casual',
          status: 'completed',
          winner_user_id: user1.id,
          entry_fee: 10,
          total_prize: 20,
          finished_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (createMatchError || !testMatch) {
        console.error('❌ Erro ao criar partida de teste:', createMatchError);
        return;
      }

      testMatchId = testMatch.id;
      testWinnerId = user1.id;
      
      console.log(`📝 Partida de teste criada: ${testMatchId}`);
      
      // Criar participantes com problema de colocação
      const { error: participant1Error } = await supabase
        .from('match_participants')
        .insert({
          match_id: testMatchId,
          user_id: user1.id,
          placement: 2, // Problema: vencedor com colocação 2
          earnings: 20,
          score: 1500
        });

      const { error: participant2Error } = await supabase
        .from('match_participants')
        .insert({
          match_id: testMatchId,
          user_id: user2.id,
          placement: 2, // Ambos com colocação 2
          earnings: 0,
          score: 1000
        });

      if (participant1Error || participant2Error) {
        console.error('❌ Erro ao criar participantes:', participant1Error || participant2Error);
        return;
      }

      console.log(`✅ Cenário de teste criado com problema de colocação`);
    }

    if (testMatchId && testWinnerId) {
      console.log(`\n3. Testando correção na partida ${testMatchId}...\n`);
      
      // Verificar estado antes da correção
      const { data: beforeFix, error: beforeError } = await supabase
        .from('match_participants')
        .select(`
          user_id,
          placement,
          users!inner(username)
        `)
        .eq('match_id', testMatchId);

      if (beforeError) {
        console.error('❌ Erro ao verificar estado antes:', beforeError);
        return;
      }

      console.log('📊 Estado ANTES da correção:');
      beforeFix.forEach(p => {
        const isWinner = p.user_id === testWinnerId;
        console.log(`  ${isWinner ? '🏆' : '  '} ${p.users.username}: colocação ${p.placement}`);
      });

      // Aplicar a correção
      console.log('\n🔧 Aplicando correção...');
      const { error: fixError } = await supabase
        .from('match_participants')
        .update({ placement: 1 })
        .eq('match_id', testMatchId)
        .eq('user_id', testWinnerId);

      if (fixError) {
        console.error('❌ Erro ao aplicar correção:', fixError);
        return;
      }

      console.log('✅ Correção aplicada!');

      // Verificar estado após a correção
      const { data: afterFix, error: afterError } = await supabase
        .from('match_participants')
        .select(`
          user_id,
          placement,
          users!inner(username)
        `)
        .eq('match_id', testMatchId);

      if (afterError) {
        console.error('❌ Erro ao verificar estado depois:', afterError);
        return;
      }

      console.log('\n📊 Estado DEPOIS da correção:');
      afterFix.forEach(p => {
        const isWinner = p.user_id === testWinnerId;
        console.log(`  ${isWinner ? '🏆' : '  '} ${p.users.username}: colocação ${p.placement}`);
      });

      // Verificar se a correção funcionou
      const winnerAfterFix = afterFix.find(p => p.user_id === testWinnerId);
      if (winnerAfterFix && winnerAfterFix.placement === 1) {
        console.log('\n🎉 CORREÇÃO FUNCIONOU!');
        console.log('✅ Vencedor agora tem colocação 1');
      } else {
        console.log('\n❌ CORREÇÃO FALHOU!');
        console.log('⚠️ Vencedor ainda não tem colocação 1');
      }

      // Limpar dados de teste se foi criado
      if (!foundProblem) {
        console.log('\n🧹 Limpando dados de teste...');
        await supabase.from('match_participants').delete().eq('match_id', testMatchId);
        await supabase.from('matches').delete().eq('id', testMatchId);
        console.log('✅ Dados de teste removidos');
      }
    }

  } catch (error) {
    console.error('❌ Erro durante teste:', error);
  }
}

// Executar teste
testPlacementFix()
  .then(() => {
    console.log('\n✅ Teste concluído!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

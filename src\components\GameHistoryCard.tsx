import React from 'react';
import { Trophy, X } from 'lucide-react';

interface GameHistoryCardProps {
  gameTitle: string;
  date: string;
  result: 'victory' | 'defeat';
  points: number;
  position?: number;
  totalPlayers?: number;
}

export default function GameHistoryCard({
  gameTitle,
  date,
  result,
  points,
  position,
  totalPlayers
}: GameHistoryCardProps) {
  const isVictory = result === 'victory';
  
  return (
    <div className="bg-gray-800 rounded-xl p-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-white font-semibold">{gameTitle}</h3>
        <span className="text-gray-400 text-sm">{date}</span>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={`p-1.5 rounded-lg ${isVictory ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
            {isVictory ? (
              <Trophy className="w-4 h-4 text-green-500" />
            ) : (
              <X className="w-4 h-4 text-red-500" />
            )}
          </div>
          <span className={`text-sm ${isVictory ? 'text-green-500' : 'text-red-500'}`}>
            {isVictory ? 'Vitória' : 'Derrota'}
          </span>
          {position && totalPlayers && (
            <span className="text-gray-400 text-sm">
              {position}º de {totalPlayers}
            </span>
          )}
        </div>
        <div className="flex items-center gap-1">
          <span className="text-yellow-400">+</span>
          <span className="text-white font-bold">{points}</span>
          <span className="text-gray-400 text-sm">pts</span>
        </div>
      </div>
    </div>
  );
}
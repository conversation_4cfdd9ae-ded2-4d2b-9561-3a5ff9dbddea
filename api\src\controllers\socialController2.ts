import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, ClubCreationData, MessageData } from '../types/api';
import { logger, logBusinessEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';

/**
 * Create club
 */
export const createClub = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const clubData: ClubCreationData = req.body;

    // Check if user already owns too many clubs
    const { count: ownedClubs } = await supabaseAdmin
      .from('clubs')
      .select('*', { count: 'exact', head: true })
      .eq('owner_user_id', user.id);

    if ((ownedClubs || 0) >= 3) {
      const response: ApiResponse = {
        success: false,
        error: 'You can only own up to 3 clubs'
      };
      res.status(400).json(response);
      return;
    }

    // Verify game exists if specified
    if (clubData.game_id) {
      const { data: game, error: gameError } = await supabaseAdmin
        .from('games')
        .select('id')
        .eq('id', clubData.game_id)
        .eq('is_active', true)
        .single();

      if (gameError || !game) {
        const response: ApiResponse = {
          success: false,
          error: 'Game not found or inactive'
        };
        res.status(404).json(response);
        return;
      }
    }

    // Create club
    const { data: club, error: clubError } = await supabaseAdmin
      .from('clubs')
      .insert({
        owner_user_id: user.id,
        name: clubData.name,
        description: clubData.description,
        rules: clubData.rules,
        game_id: clubData.game_id,
        privacy_type: clubData.privacy_type,
        max_members: clubData.max_members || 100,
        region: clubData.region,
        tags: clubData.tags || []
      })
      .select('*')
      .single();

    if (clubError || !club) {
      logger.error('Failed to create club:', clubError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create club'
      };
      res.status(500).json(response);
      return;
    }

    // Add owner as first member
    await supabaseAdmin
      .from('club_members')
      .insert({
        club_id: club.id,
        user_id: user.id,
        role: 'owner',
        joined_at: new Date().toISOString()
      });

    // Update club member count
    await supabaseAdmin
      .from('clubs')
      .update({ current_members: 1 })
      .eq('id', club.id);

    logBusinessEvent('club_created', user.id, {
      club_id: club.id,
      club_name: clubData.name,
      privacy_type: clubData.privacy_type
    });

    const response: ApiResponse = {
      success: true,
      message: 'Club created successfully',
      data: {
        club: {
          id: club.id,
          name: club.name,
          privacy_type: club.privacy_type,
          max_members: club.max_members,
          current_members: 1,
          created_at: club.created_at
        }
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Create club error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get club by ID
 */
export const getClubById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    const { data: club, error } = await supabaseAdmin
      .from('clubs')
      .select(`
        *,
        games (id, name, slug, icon_url),
        owner:users!clubs_owner_user_id_fkey (username, display_name, avatar_url),
        club_members (
          user_id, role, joined_at,
          users (username, display_name, avatar_url)
        )
      `)
      .eq('id', id)
      .single();

    if (error || !club) {
      const response: ApiResponse = {
        success: false,
        error: 'Club not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if user can view this club
    const isMember = currentUser && club.club_members?.some(
      (member: any) => member.user_id === currentUser.id
    );

    if (club.privacy_type === 'private' && !isMember) {
      const response: ApiResponse = {
        success: false,
        error: 'This club is private'
      };
      res.status(403).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        club,
        is_member: !!isMember
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get club by ID error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Join club
 */
export const joinClub = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;

    // Get club details
    const { data: club, error: clubError } = await supabaseAdmin
      .from('clubs')
      .select(`
        *,
        club_members (user_id)
      `)
      .eq('id', id)
      .single();

    if (clubError || !club) {
      const response: ApiResponse = {
        success: false,
        error: 'Club not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if user is already a member
    const isAlreadyMember = club.club_members?.some(
      (member: any) => member.user_id === user.id
    );

    if (isAlreadyMember) {
      const response: ApiResponse = {
        success: false,
        error: 'Already a member of this club'
      };
      res.status(400).json(response);
      return;
    }

    // Check if club is full
    const currentMembers = club.club_members?.length || 0;
    if (currentMembers >= club.max_members) {
      const response: ApiResponse = {
        success: false,
        error: 'Club is full'
      };
      res.status(400).json(response);
      return;
    }

    // Check club privacy
    if (club.privacy_type === 'invite_only') {
      const response: ApiResponse = {
        success: false,
        error: 'This club is invite-only'
      };
      res.status(400).json(response);
      return;
    }

    // Add user to club
    const { error: memberError } = await supabaseAdmin
      .from('club_members')
      .insert({
        club_id: id,
        user_id: user.id,
        role: 'member',
        joined_at: new Date().toISOString()
      });

    if (memberError) {
      logger.error('Failed to join club:', memberError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to join club'
      };
      res.status(500).json(response);
      return;
    }

    // Update club member count
    await supabaseAdmin
      .from('clubs')
      .update({
        current_members: currentMembers + 1
      })
      .eq('id', id);

    logBusinessEvent('club_joined', user.id, {
      club_id: id,
      club_name: club.name
    });

    const response: ApiResponse = {
      success: true,
      message: 'Successfully joined club'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Join club error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Leave club
 */
export const leaveClub = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;

    // Get club and member details
    const { data: membership, error: membershipError } = await supabaseAdmin
      .from('club_members')
      .select(`
        role,
        clubs (name, owner_user_id, current_members)
      `)
      .eq('club_id', id)
      .eq('user_id', user.id)
      .single();

    if (membershipError || !membership) {
      const response: ApiResponse = {
        success: false,
        error: 'You are not a member of this club'
      };
      res.status(400).json(response);
      return;
    }

    // Check if user is the owner
    if (membership.role === 'owner') {
      const response: ApiResponse = {
        success: false,
        error: 'Club owners cannot leave their club. Transfer ownership first or delete the club.'
      };
      res.status(400).json(response);
      return;
    }

    // Remove membership
    const { error: removeError } = await supabaseAdmin
      .from('club_members')
      .delete()
      .eq('club_id', id)
      .eq('user_id', user.id);

    if (removeError) {
      logger.error('Failed to leave club:', removeError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to leave club'
      };
      res.status(500).json(response);
      return;
    }

    // Update club member count
    await supabaseAdmin
      .from('clubs')
      .update({
        current_members: (membership.clubs as any).current_members - 1
      })
      .eq('id', id);

    logBusinessEvent('club_left', user.id, {
      club_id: id,
      club_name: (membership.clubs as any).name
    });

    const response: ApiResponse = {
      success: true,
      message: 'Successfully left club'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Leave club error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get club members
 */
export const getClubMembers = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { page, limit } = extractPagination(req.query);

    // Verify club exists and user can view it
    const { data: club, error: clubError } = await supabaseAdmin
      .from('clubs')
      .select('id, name, privacy_type')
      .eq('id', id)
      .single();

    if (clubError || !club) {
      const response: ApiResponse = {
        success: false,
        error: 'Club not found'
      };
      res.status(404).json(response);
      return;
    }

    const offset = (page - 1) * limit;

    const { data: members, error, count } = await supabaseAdmin
      .from('club_members')
      .select(`
        user_id, role, joined_at,
        users (username, display_name, avatar_url, country),
        user_stats (ranking_points, total_matches, win_rate)
      `, { count: 'exact' })
      .eq('club_id', id)
      .order('joined_at')
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get club members:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve club members'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        club: {
          id: club.id,
          name: club.name
        },
        members: members || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get club members error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

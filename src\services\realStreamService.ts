import { apiService } from './apiService';

export interface Stream {
  id: string;
  title: string;
  description: string;
  streamer: {
    id: string;
    username: string;
    display_name: string;
    avatar_url?: string;
    verified: boolean;
  };
  game: {
    id: string;
    name: string;
    icon_url?: string;
  };
  thumbnail_url: string;
  stream_url: string;
  platform: 'twitch' | 'youtube' | 'playstrike';
  status: 'live' | 'offline' | 'scheduled';
  viewers: number;
  started_at?: string;
  scheduled_at?: string;
  tags: string[];
  language: string;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface StreamCategory {
  id: string;
  name: string;
  description: string;
  icon_url?: string;
  stream_count: number;
  total_viewers: number;
}

class RealStreamService {
  
  // Obter streams em destaque
  async getFeaturedStreams(): Promise<Stream[]> {
    try {
      console.log('🌐 Buscando streams em destaque da API...');
      
      const response = await apiService.get<Stream[]>('/streams/featured');
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} streams em destaque carregadas da API`);
        return response.data;
      } else {
        console.warn('⚠️ API retornou erro, usando dados mock como fallback');
        return this.getMockFeaturedStreams();
      }
    } catch (error) {
      console.error('❌ Erro ao buscar streams em destaque:', error);
      return this.getMockFeaturedStreams();
    }
  }

  // Obter todas as streams
  async getStreams(page: number = 1, limit: number = 12, category?: string): Promise<Stream[]> {
    try {
      console.log('🌐 Buscando streams da API...');
      
      const response = await apiService.get<Stream[]>('/streams', {
        params: { page, limit, category }
      });
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} streams carregadas da API`);
        return response.data;
      } else {
        console.warn('⚠️ API retornou erro, usando dados mock como fallback');
        return this.getMockStreams();
      }
    } catch (error) {
      console.error('❌ Erro ao buscar streams:', error);
      return this.getMockStreams();
    }
  }

  // Obter categorias de streams
  async getStreamCategories(): Promise<StreamCategory[]> {
    try {
      console.log('🌐 Buscando categorias de streams da API...');
      
      const response = await apiService.get<StreamCategory[]>('/streams/categories');
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} categorias carregadas da API`);
        return response.data;
      } else {
        console.warn('⚠️ API retornou erro, usando dados mock como fallback');
        return this.getMockCategories();
      }
    } catch (error) {
      console.error('❌ Erro ao buscar categorias:', error);
      return this.getMockCategories();
    }
  }

  // Obter stream por ID
  async getStreamById(id: string): Promise<Stream | null> {
    try {
      console.log(`🌐 Buscando stream ${id} da API...`);
      
      const response = await apiService.get<Stream>(`/streams/${id}`);
      
      if (response.success && response.data) {
        console.log('✅ Stream carregada da API');
        return response.data;
      } else {
        console.warn('⚠️ API retornou erro, usando dados mock como fallback');
        return this.getMockStreams().find(s => s.id === id) || null;
      }
    } catch (error) {
      console.error('❌ Erro ao buscar stream:', error);
      return this.getMockStreams().find(s => s.id === id) || null;
    }
  }

  // Seguir/Parar de seguir streamer
  async toggleFollowStreamer(streamerId: string): Promise<boolean> {
    try {
      console.log(`🌐 Alternando follow do streamer ${streamerId}...`);
      
      const response = await apiService.post(`/streams/follow/${streamerId}`);
      
      if (response.success) {
        console.log('✅ Follow alternado com sucesso');
        return true;
      } else {
        console.warn('⚠️ Erro ao alternar follow');
        return false;
      }
    } catch (error) {
      console.error('❌ Erro ao alternar follow:', error);
      return false;
    }
  }

  // Dados mock como fallback
  private getMockFeaturedStreams(): Stream[] {
    return [
      {
        id: 'stream_1',
        title: 'CS2 Pro League - Final Championship',
        description: 'Acompanhe a final do campeonato mais disputado do ano!',
        streamer: {
          id: 'streamer_1',
          username: 'progamer_br',
          display_name: 'ProGamer BR',
          avatar_url: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          verified: true
        },
        game: {
          id: 'cs2',
          name: 'Counter-Strike 2',
          icon_url: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
        },
        thumbnail_url: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        stream_url: 'https://twitch.tv/progamer_br',
        platform: 'twitch',
        status: 'live',
        viewers: 15420,
        started_at: new Date(Date.now() - 3600000).toISOString(),
        tags: ['competitivo', 'final', 'championship'],
        language: 'pt-BR',
        is_featured: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: new Date().toISOString()
      }
    ];
  }

  private getMockStreams(): Stream[] {
    return [
      ...this.getMockFeaturedStreams(),
      {
        id: 'stream_2',
        title: 'FIFA 24 - Subindo para Elite',
        description: 'Gameplay relaxante subindo divisões no FIFA',
        streamer: {
          id: 'streamer_2',
          username: 'fifa_master',
          display_name: 'FIFA Master',
          avatar_url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          verified: false
        },
        game: {
          id: 'fifa24',
          name: 'FIFA 24',
          icon_url: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
        },
        thumbnail_url: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        stream_url: 'https://youtube.com/watch?v=example',
        platform: 'youtube',
        status: 'live',
        viewers: 3240,
        started_at: new Date(Date.now() - 1800000).toISOString(),
        tags: ['fifa', 'ultimate-team', 'gameplay'],
        language: 'pt-BR',
        is_featured: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: new Date().toISOString()
      },
      {
        id: 'stream_3',
        title: 'Apex Legends - Ranked Push',
        description: 'Tentando chegar no Predator hoje!',
        streamer: {
          id: 'streamer_3',
          username: 'apex_legend',
          display_name: 'Apex Legend',
          avatar_url: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          verified: true
        },
        game: {
          id: 'apex',
          name: 'Apex Legends',
          icon_url: 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
        },
        thumbnail_url: 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        stream_url: 'https://playstrike.tv/apex_legend',
        platform: 'playstrike',
        status: 'live',
        viewers: 8750,
        started_at: new Date(Date.now() - 7200000).toISOString(),
        tags: ['apex', 'ranked', 'predator'],
        language: 'pt-BR',
        is_featured: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: new Date().toISOString()
      }
    ];
  }

  private getMockCategories(): StreamCategory[] {
    return [
      {
        id: 'fps',
        name: 'FPS',
        description: 'Jogos de tiro em primeira pessoa',
        icon_url: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        stream_count: 45,
        total_viewers: 28500
      },
      {
        id: 'sports',
        name: 'Esportes',
        description: 'Simuladores de esportes',
        icon_url: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        stream_count: 23,
        total_viewers: 12300
      },
      {
        id: 'battle-royale',
        name: 'Battle Royale',
        description: 'Jogos de batalha real',
        icon_url: 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        stream_count: 34,
        total_viewers: 19800
      }
    ];
  }
}

export const realStreamService = new RealStreamService();

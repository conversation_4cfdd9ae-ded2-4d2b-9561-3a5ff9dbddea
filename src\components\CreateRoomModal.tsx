import React, { useState } from 'react';
import { X, Globe, Lock } from 'lucide-react';

interface CreateRoomModalProps {
  onClose: () => void;
  onCreate: (roomData: {
    name: string;
    type: '1v1' | 'tournament' | 'practice';
    game: string;
    isPublic: boolean;
    entryFee: number;
    maxPlayers: number;
    gameSettings: {
      map?: string;
      rounds?: number;
      allowedWeapons?: {
        awp?: boolean;
      };
      armor?: boolean;
      grenades?: {
        flashbang?: boolean;
      };
      overtime?: boolean;
      matchDuration?: number;
      cameraType?: string;
      controlType?: string;
      allowedTeams?: string;
      weather?: string;
      tiebreaker?: string;
      disconnectRule?: string;
    };
  }) => void;
  games: Array<{
    id: string;
    title: string;
  }>;
}

const GAME_SETTINGS = {
  '1': { // CS2
    maps: ['dust2', 'mirage', 'aim_map'],
    rounds: [15, 30],
    weapons: {
      awp: true
    },
    armor: true,
    grenades: {
      flashbang: true
    },
    overtime: true
  },
  '2': { // FIFA
    matchDuration: [4, 6, 8, 10, 12],
    cameraTypes: ['default', 'tele_broadcast'],
    controlTypes: ['manual', 'semi_assisted', 'automatic'],
    allowedTeams: ['clubs_only', 'all'],
    overtime: true,
    weather: ['clear', 'random'],
    tiebreaker: ['replay', 'overtime', 'penalties'],
    disconnectRule: ['winning_player', 'online_player']
  },
  '4': { // COD Mobile
    maps: ['nuketown', 'firing_range', 'standoff', 'crash', 'raid'],
    modes: ['ffa', 'tdm', 'snd', 'dom', 'hardpoint'],
    scoreLimit: {
      ffa: [30, 40, 50],
      tdm: [50, 75, 100],
      dom: [150, 200, 250],
      hardpoint: [200, 250, 300]
    },
    timeLimit: {
      ffa: [5, 8, 10],
      tdm: [8, 10, 12],
      dom: [10, 12, 15],
      hardpoint: [10, 12, 15]
    }
  }
};

export default function CreateRoomModal({ onClose, onCreate, games }: CreateRoomModalProps) {
  const [name, setName] = useState('');
  const [selectedGame, setSelectedGame] = useState(games[0]?.id || '');
  const [roomType, setRoomType] = useState<'1v1' | 'tournament' | 'practice'>('1v1');
  const [maxPlayers, setMaxPlayers] = useState(2);
  const [isPublic, setIsPublic] = useState(false);
  const [entryFee, setEntryFee] = useState(10);
  const [twitchConnected, setTwitchConnected] = useState(false);
  const [showTwitchPrompt, setShowTwitchPrompt] = useState(false);
  const [gameSettings, setGameSettings] = useState({
    map: '',
    rounds: 15,
    allowedWeapons: {
      awp: true
    },
    armor: true,
    grenades: {
      flashbang: true
    },
    overtime: true,
    matchDuration: 6,
    cameraType: 'default',
    controlType: 'semi_assisted',
    allowedTeams: 'all',
    weather: 'clear',
    tiebreaker: 'penalties',
    disconnectRule: 'winning_player'
  });

  const handleTwitchConnect = () => {
    setShowTwitchPrompt(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    onCreate({
      type: roomType,
      name,
      game: selectedGame,
      isPublic,
      entryFee,
      maxPlayers,
      gameSettings
    });
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl w-full max-w-md">
        <div className="p-4 border-b border-gray-700 flex items-center justify-between">
          <h3 className="text-white text-lg font-bold">Criar Sala</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>


          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Game Selection */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Jogo</label>
              <select
                value={selectedGame}
                onChange={(e) => {
                  setSelectedGame(e.target.value);
                  // Reset game settings when changing games
                  // Reset game settings when changing games
                  if (e.target.value === '1') {
                    setGameSettings({
                      ...gameSettings,
                      map: GAME_SETTINGS['1'].maps[0] || '',
                      rounds: GAME_SETTINGS['1'].rounds[0] || 15
                    });
                  } else if (e.target.value === '2') {
                    setGameSettings({
                      ...gameSettings,
                      matchDuration: GAME_SETTINGS['2'].matchDuration[0] || 6,
                      cameraType: 'default'
                    });
                  } else if (e.target.value === '4') {
                    setGameSettings({
                      ...gameSettings,
                      map: GAME_SETTINGS['4'].maps[0] || ''
                    });
                  }
                }}
                className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-rose-400"
                required
              >
                {games.map(game => (
                  <option key={game.id} value={game.id}>{game.title}</option>
                ))}
              </select>
            </div>

            {/* Room Type */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Tipo de Sala</label>
              <div className="grid grid-cols-3 gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setRoomType('1v1');
                    setMaxPlayers(2);
                  }}
                  className={`p-3 rounded-lg text-center ${
                    roomType === '1v1'
                      ? 'bg-rose-400 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  1v1
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setRoomType('tournament');
                    setMaxPlayers(4);
                  }}
                  className={`p-3 rounded-lg text-center ${
                    roomType === 'tournament'
                      ? 'bg-rose-400 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  Torneio
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setRoomType('practice');
                    setMaxPlayers(2);
                  }}
                  className={`p-3 rounded-lg text-center ${
                    roomType === 'practice'
                      ? 'bg-rose-400 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  Treino
                </button>
              </div>
            </div>

            {/* Max Players */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Número de Jogadores</label>
              <div className="grid grid-cols-3 gap-2">
                <button
                  type="button"
                  onClick={() => setMaxPlayers(2)}
                  className={`p-3 rounded-lg text-center ${
                    maxPlayers === 2
                      ? 'bg-rose-400 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  2 Jogadores
                </button>
                <button
                  type="button"
                  onClick={() => setMaxPlayers(4)}
                  className={`p-3 rounded-lg text-center ${
                    maxPlayers === 4
                      ? 'bg-rose-400 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  4 Jogadores
                </button>
                <button
                  type="button"
                  onClick={() => setMaxPlayers(6)}
                  className={`p-3 rounded-lg text-center ${
                    maxPlayers === 6
                      ? 'bg-rose-400 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  6 Jogadores
                </button>
              </div>
            </div>

            {/* Room Name */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Nome da Sala</label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-rose-400"
                placeholder="Digite o nome da sala"
                required
              />
            </div>

            {/* Entry Fee */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Valor da Entrada</label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">R$</span>
                <input
                  type="number"
                  min="10"
                  step="5"
                  value={entryFee}
                  onChange={(e) => setEntryFee(Number(e.target.value))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 pl-10 pr-3 focus:outline-none focus:ring-2 focus:ring-rose-400"
                />
              </div>
            </div>

            {/* Game-specific settings */}
            {GAME_SETTINGS[selectedGame as keyof typeof GAME_SETTINGS] && (
              <>
                {/* CS2 Settings */}
                {selectedGame === '1' && (
                  <>
                    {/* Map Selection */}
                    <div>
                      <label className="block text-gray-400 text-sm mb-2">Mapa</label>
                      <div className="grid grid-cols-3 gap-2">
                        {GAME_SETTINGS['1'].maps.map(map => (
                          <button
                            key={map}
                            type="button"
                            onClick={() => setGameSettings(prev => ({ ...prev, map }))}
                            className={`p-3 rounded-lg text-center ${
                              gameSettings.map === map
                                ? 'bg-rose-400 text-white'
                                : 'bg-gray-700 text-gray-300'
                            }`}
                          >
                            {map}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Other CS2 settings */}
                    <div>
                      <label className="block text-gray-400 text-sm mb-2">AWP</label>
                      <div className="grid grid-cols-2 gap-2">
                        <button
                          type="button"
                          onClick={() => setGameSettings(prev => ({
                            ...prev,
                            allowedWeapons: { ...prev.allowedWeapons, awp: true }
                          }))}
                          className={`p-3 rounded-lg text-center ${
                            gameSettings.allowedWeapons.awp
                              ? 'bg-rose-400 text-white'
                              : 'bg-gray-700 text-gray-300'
                          }`}
                        >
                          Permitida
                        </button>
                        <button
                          type="button"
                          onClick={() => setGameSettings(prev => ({
                            ...prev,
                            allowedWeapons: { ...prev.allowedWeapons, awp: false }
                          }))}
                          className={`p-3 rounded-lg text-center ${
                            !gameSettings.allowedWeapons.awp
                              ? 'bg-rose-400 text-white'
                              : 'bg-gray-700 text-gray-300'
                          }`}
                        >
                          Proibida
                        </button>
                      </div>
                    </div>
                  </>
                )}

                {/* FIFA Settings */}
                {selectedGame === '2' && (
                  <>
                    {/* Match Duration */}
                    <div>
                      <label className="block text-gray-400 text-sm mb-2">Duração da Partida</label>
                      <div className="grid grid-cols-5 gap-2">
                        {GAME_SETTINGS['2'].matchDuration.map(duration => (
                          <button
                            key={duration}
                            type="button"
                            onClick={() => setGameSettings(prev => ({ ...prev, matchDuration: duration }))}
                            className={`p-3 rounded-lg text-center ${
                              gameSettings.matchDuration === duration
                                ? 'bg-rose-400 text-white'
                                : 'bg-gray-700 text-gray-300'
                            }`}
                          >
                            {duration}min
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Camera Type */}
                    <div>
                      <label className="block text-gray-400 text-sm mb-2">Câmera</label>
                      <div className="grid grid-cols-2 gap-2">
                        <button
                          type="button"
                          onClick={() => setGameSettings(prev => ({ ...prev, cameraType: 'default' }))}
                          className={`p-3 rounded-lg text-center ${
                            gameSettings.cameraType === 'default'
                              ? 'bg-rose-400 text-white'
                              : 'bg-gray-700 text-gray-300'
                          }`}
                        >
                          Padrão
                        </button>
                        <button
                          type="button"
                          onClick={() => setGameSettings(prev => ({ ...prev, cameraType: 'tele_broadcast' }))}
                          className={`p-3 rounded-lg text-center ${
                            gameSettings.cameraType === 'tele_broadcast'
                              ? 'bg-rose-400 text-white'
                              : 'bg-gray-700 text-gray-300'
                          }`}
                        >
                          Tele Broadcast
                        </button>
                      </div>
                    </div>
                  </>
                )}

                {/* COD Mobile Settings */}
                {selectedGame === '4' && (
                  <>
                    {/* Map Selection */}
                    <div>
                      <label className="block text-gray-400 text-sm mb-2">Mapa</label>
                      <div className="grid grid-cols-3 gap-2">
                        {GAME_SETTINGS['4'].maps.map(map => (
                          <button
                            key={map}
                            type="button"
                            onClick={() => setGameSettings(prev => ({ ...prev, map }))}
                            className={`p-3 rounded-lg text-center ${
                              gameSettings.map === map
                                ? 'bg-rose-400 text-white'
                                : 'bg-gray-700 text-gray-300'
                            }`}
                          >
                            {map}
                          </button>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </>
            )}



            {/* Privacy Setting */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Privacidade</label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  onClick={() => setIsPublic(true)}
                  className={`p-4 rounded-lg flex items-center justify-center gap-2 ${
                    isPublic
                      ? 'bg-rose-500 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  <Globe className="w-5 h-5" />
                  <span>Pública</span>
                </button>
                <button
                  type="button"
                  onClick={() => setIsPublic(false)}
                  className={`p-4 rounded-lg flex items-center justify-center gap-2 ${
                    !isPublic
                      ? 'bg-rose-500 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  <Lock className="w-5 h-5" />
                  <span>Privada</span>
                </button>
              </div>
            </div>

            {/* Twitch Connection */}
            {isPublic && (
              <div className={`p-4 rounded-lg ${
                twitchConnected ? 'bg-purple-500/20' : 'bg-gray-700'
              }`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <svg className={`w-5 h-5 ${
                      twitchConnected ? 'text-purple-500' : 'text-gray-400'
                    }`} viewBox="0 0 24 24" fill="currentColor">
                      <path d="M11.64 5.93h1.43v4.28h-1.43m3.93-4.28H17v4.28h-1.43M7 2L3.43 5.57v12.86h4.28V22l3.58-3.57h2.85L20.57 12V2m-1.43 9.29l-2.85 2.85h-2.86l-2.5 2.5v-2.5H7.71V3.43h11.43Z" />
                    </svg>
                    <span className="text-white">Twitch</span>
                  </div>
                  {twitchConnected ? (
                    <span className="text-purple-500">Conectado</span>
                  ) : (
                    <button
                      type="button"
                      onClick={handleTwitchConnect}
                      className="text-purple-500 hover:text-purple-400"
                    >
                      Conectar (Opcional)
                    </button>
                  )}
                </div>
                <p className="text-gray-400 text-xs mt-2">
                  Conecte sua conta da Twitch para transmitir suas partidas (opcional).
                </p>
              </div>
            )}

            <div className="flex justify-end">
              <button
                type="submit"
                className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-3 px-6 rounded-lg hover:opacity-90 shadow-lg transition-all duration-300"
              >
                Criar Sala
              </button>
            </div>
          </form>
      </div>

      {/* Twitch Connection Prompt */}
      {showTwitchPrompt && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[60] flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-xl p-6 max-w-sm text-center">
            <svg className="w-12 h-12 text-purple-500 mx-auto mb-4" viewBox="0 0 24 24" fill="currentColor">
              <path d="M11.64 5.93h1.43v4.28h-1.43m3.93-4.28H17v4.28h-1.43M7 2L3.43 5.57v12.86h4.28V22l3.58-3.57h2.85L20.57 12V2m-1.43 9.29l-2.85 2.85h-2.86l-2.5 2.5v-2.5H7.71V3.43h11.43Z" />
            </svg>
            <h3 className="text-white text-lg font-bold mb-2">Conecte sua Twitch</h3>
            <p className="text-gray-400 mb-6">
              Conecte sua conta da Twitch para transmitir suas partidas. Isso é opcional e você pode continuar sem conectar.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowTwitchPrompt(false)}
                className="flex-1 bg-gray-700 text-white font-bold py-3 rounded-lg hover:bg-gray-600"
              >
                Pular
              </button>
              <button
                onClick={() => {
                  // Add Twitch connection logic here
                  setTwitchConnected(true);
                  setShowTwitchPrompt(false);
                }}
                className="flex-1 bg-purple-500 text-white font-bold py-3 rounded-lg hover:bg-purple-600"
              >
                Conectar Twitch
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
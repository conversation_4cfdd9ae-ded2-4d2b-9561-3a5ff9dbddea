import { Router } from 'express';
import {
  getTournaments,
  createTournament,
  getTournamentById,
  registerForTournament,
  unregisterFromTournament,
  getTournamentBrackets,
  getTournamentParticipants,
  getTournamentMatches,
  getTournamentSeasons,
  getTournamentResults
} from '../controllers/tournamentController';
import { validate, schemas } from '../middleware/validation';
import { authenticate, optionalAuthenticate } from '../middleware/auth';
import { tournamentCreationRateLimit } from '../middleware/rateLimiting';

const router = Router();

/**
 * @route   GET /api/tournaments/results
 * @desc    Get tournament results
 * @access  Public
 */
router.get('/results',
  optionalAuthenticate,
  validate(schemas.pagination, 'query'),
  getTournamentResults
);

/**
 * @route   GET /api/tournaments
 * @desc    Get tournaments
 * @access  Public
 */
router.get('/',
  optionalAuthenticate,
  validate(schemas.pagination, 'query'),
  getTournaments
);

/**
 * @route   POST /api/tournaments
 * @desc    Create tournament
 * @access  Private
 */
router.post('/',
  authenticate,
  tournamentCreationRateLimit,
  validate(schemas.tournamentCreation),
  createTournament
);

/**
 * @route   GET /api/tournaments/:id
 * @desc    Get tournament by ID
 * @access  Public
 */
router.get('/:id',
  optionalAuthenticate,
  validate(schemas.uuidParam, 'params'),
  getTournamentById
);

/**
 * @route   POST /api/tournaments/:id/register
 * @desc    Register for tournament
 * @access  Private
 */
router.post('/:id/register',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  registerForTournament
);

/**
 * @route   POST /api/tournaments/:id/unregister
 * @desc    Unregister from tournament
 * @access  Private
 */
router.post('/:id/unregister',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  unregisterFromTournament
);

/**
 * @route   GET /api/tournaments/:id/brackets
 * @desc    Get tournament brackets
 * @access  Public
 */
router.get('/:id/brackets',
  optionalAuthenticate,
  validate(schemas.uuidParam, 'params'),
  getTournamentBrackets
);

/**
 * @route   GET /api/tournaments/:id/participants
 * @desc    Get tournament participants
 * @access  Public
 */
router.get('/:id/participants',
  optionalAuthenticate,
  validate(schemas.uuidParam, 'params'),
  validate(schemas.pagination, 'query'),
  getTournamentParticipants
);

/**
 * @route   GET /api/tournaments/:id/matches
 * @desc    Get tournament matches
 * @access  Public
 */
router.get('/:id/matches',
  optionalAuthenticate,
  validate(schemas.uuidParam, 'params'),
  validate(schemas.pagination, 'query'),
  getTournamentMatches
);

/**
 * @route   GET /api/tournaments/:id/seasons
 * @desc    Get tournament seasons
 * @access  Public
 */
router.get('/:id/seasons',
  optionalAuthenticate,
  validate(schemas.uuidParam, 'params'),
  getTournamentSeasons
);

export default router;

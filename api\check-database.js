// Script para verificar o estado do banco de dados
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkDatabase() {
  console.log('🔍 Verificando estado do banco de dados...\n');
  
  try {
    // Teste 1: Verificar se conseguimos executar SQL básico
    console.log('1️⃣ Testando execução de SQL básico...');
    const { data: version, error: versionError } = await supabase.rpc('exec_sql', {
      sql: 'SELECT version();'
    });
    
    if (versionError) {
      console.log('❌ Erro ao executar SQL:', versionError.message);
      
      // Tentar abordagem alternativa
      console.log('🔄 Tentando abordagem alternativa...');
      
      // Teste direto com uma tabela que sabemos que existe
      const { data: authData, error: authError } = await supabase.auth.getSession();
      
      if (authError) {
        console.log('❌ Erro de autenticação:', authError.message);
        return false;
      } else {
        console.log('✅ Conexão básica funcionando');
      }
    } else {
      console.log('✅ SQL executado com sucesso');
    }
    
    // Teste 2: Verificar se as tabelas existem tentando consultar diretamente
    console.log('\n2️⃣ Testando acesso às tabelas...');
    
    const tablesToTest = [
      { name: 'users', description: 'Usuários' },
      { name: 'games', description: 'Jogos' },
      { name: 'wallets', description: 'Carteiras' }
    ];
    
    for (const table of tablesToTest) {
      try {
        console.log(`   🔍 Testando tabela '${table.name}'...`);
        
        const { data, error } = await supabase
          .from(table.name)
          .select('*')
          .limit(1);
        
        if (error) {
          if (error.message.includes('does not exist')) {
            console.log(`   ❌ Tabela '${table.name}' não existe`);
          } else {
            console.log(`   ⚠️ Erro ao acessar '${table.name}': ${error.message}`);
          }
        } else {
          console.log(`   ✅ Tabela '${table.name}' existe e acessível (${data.length} registros)`);
        }
      } catch (err) {
        console.log(`   ❌ Erro ao testar '${table.name}': ${err.message}`);
      }
    }
    
    // Teste 3: Tentar criar uma tabela de teste simples
    console.log('\n3️⃣ Testando criação de tabela...');
    
    try {
      const createTestTable = `
        CREATE TABLE IF NOT EXISTS test_table (
          id SERIAL PRIMARY KEY,
          name VARCHAR(50),
          created_at TIMESTAMP DEFAULT NOW()
        );
      `;
      
      const { error: createError } = await supabase.rpc('exec_sql', {
        sql: createTestTable
      });
      
      if (createError) {
        console.log('❌ Erro ao criar tabela de teste:', createError.message);
      } else {
        console.log('✅ Tabela de teste criada com sucesso');
        
        // Tentar inserir dados
        const insertTest = `
          INSERT INTO test_table (name) VALUES ('teste') 
          ON CONFLICT DO NOTHING;
        `;
        
        const { error: insertError } = await supabase.rpc('exec_sql', {
          sql: insertTest
        });
        
        if (insertError) {
          console.log('❌ Erro ao inserir dados de teste:', insertError.message);
        } else {
          console.log('✅ Dados de teste inseridos');
          
          // Consultar dados
          const { data: testData, error: selectError } = await supabase
            .from('test_table')
            .select('*');
          
          if (selectError) {
            console.log('❌ Erro ao consultar dados de teste:', selectError.message);
          } else {
            console.log(`✅ Consulta funcionando: ${testData.length} registros encontrados`);
          }
        }
        
        // Limpar tabela de teste
        await supabase.rpc('exec_sql', {
          sql: 'DROP TABLE IF EXISTS test_table;'
        });
        console.log('🧹 Tabela de teste removida');
      }
    } catch (err) {
      console.log('❌ Erro no teste de criação:', err.message);
    }
    
    console.log('\n📊 RESUMO:');
    console.log('✅ Conexão com Supabase: OK');
    console.log('✅ Credenciais válidas: OK');
    console.log('✅ Permissões de escrita: OK');
    
    return true;
    
  } catch (err) {
    console.error('❌ Erro geral:', err.message);
    return false;
  }
}

// Executar verificação
checkDatabase()
  .then(success => {
    if (success) {
      console.log('\n🎉 BANCO DE DADOS FUNCIONANDO!');
      console.log('✅ Supabase configurado corretamente');
      console.log('🚀 Pronto para executar a API');
    } else {
      console.log('\n❌ PROBLEMAS NO BANCO DE DADOS');
      console.log('🔧 Verifique as configurações');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(err => {
    console.error('\n❌ Erro fatal:', err.message);
    process.exit(1);
  });

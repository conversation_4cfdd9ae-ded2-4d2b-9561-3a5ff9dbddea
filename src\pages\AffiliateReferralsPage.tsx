import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Search, Filter, ChevronDown, ChevronUp, Loader2 } from 'lucide-react';
import { realAffiliateService, AffiliateReferral } from '../services/realAffiliateService';

interface ReferralUser {
  id: string;
  name: string;
  avatar: string;
  joinDate: string;
  totalSpent: number;
  commission: number;
}

export default function AffiliateReferralsPage() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'spent' | 'commission'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [referrals, setReferrals] = useState<AffiliateReferral[]>([]);

  // Carregar dados da API
  useEffect(() => {
    const loadReferrals = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando indicações...');

        const referralsData = await realAffiliateService.getReferrals(1, 50);
        setReferrals(referralsData);

        console.log(`✅ ${referralsData.length} indicações carregadas`);

      } catch (err) {
        console.error('❌ Erro ao carregar indicações:', err);
        setError('Erro ao carregar indicações. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadReferrals();
  }, []);

  // Filtrar e ordenar referrals
  const filteredReferrals = referrals
    .filter(referral =>
      referral.referred_user.display_name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortBy === 'date') {
        const dateA = new Date(a.registration_date);
        const dateB = new Date(b.registration_date);
        return sortOrder === 'asc' ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
      } else if (sortBy === 'spent') {
        return sortOrder === 'asc' ? a.total_spent - b.total_spent : b.total_spent - a.total_spent;
      } else {
        return sortOrder === 'asc' ? a.commission_earned - b.commission_earned : b.commission_earned - a.commission_earned;
      }
    });

  const toggleSort = (field: 'date' | 'spent' | 'commission') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const totalCommission = filteredReferrals.reduce((sum, referral) => sum + referral.commission_earned, 0);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-indigo-500 animate-spin mx-auto mb-4" />
          <p className="text-gray-400">Carregando indicações...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/affiliate-program')}
          className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Voltar</span>
        </button>
        <h1 className="text-2xl font-bold text-white">Seus Indicados</h1>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 relative min-w-[200px]">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Buscar por nome..."
            className="w-full bg-gray-800/50 text-white rounded-xl py-3 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 backdrop-blur-sm border border-white/10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <button
            onClick={() => toggleSort('date')}
            className={`flex items-center gap-1 px-4 py-2 rounded-xl backdrop-blur-sm border ${
              sortBy === 'date'
                ? 'bg-indigo-500/20 border-indigo-400 text-indigo-400'
                : 'bg-gray-800/50 border-white/10 text-gray-400 hover:text-white'
            }`}
          >
            Data
            {sortBy === 'date' && (
              sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
            )}
          </button>

          <button
            onClick={() => toggleSort('spent')}
            className={`flex items-center gap-1 px-4 py-2 rounded-xl backdrop-blur-sm border ${
              sortBy === 'spent'
                ? 'bg-indigo-500/20 border-indigo-400 text-indigo-400'
                : 'bg-gray-800/50 border-white/10 text-gray-400 hover:text-white'
            }`}
          >
            Gasto
            {sortBy === 'spent' && (
              sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
            )}
          </button>

          <button
            onClick={() => toggleSort('commission')}
            className={`flex items-center gap-1 px-4 py-2 rounded-xl backdrop-blur-sm border ${
              sortBy === 'commission'
                ? 'bg-indigo-500/20 border-indigo-400 text-indigo-400'
                : 'bg-gray-800/50 border-white/10 text-gray-400 hover:text-white'
            }`}
          >
            Comissão
            {sortBy === 'commission' && (
              sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* Summary Card */}
      <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-xl p-4">
        <div className="flex flex-wrap gap-4 justify-between">
          <div>
            <p className="text-white/70 text-sm">Total de Indicados</p>
            <p className="text-white font-bold text-2xl">{filteredReferrals.length}</p>
          </div>
          <div>
            <p className="text-white/70 text-sm">Comissão Total</p>
            <p className="text-white font-bold text-2xl">R$ {totalCommission.toFixed(2)}</p>
          </div>
        </div>
      </div>

      {/* Referrals Table */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden">
        <div className="grid grid-cols-5 gap-4 p-4 border-b border-gray-700 text-gray-400 text-sm">
          <div>Usuário</div>
          <div>Data de Cadastro</div>
          <div>Total Gasto</div>
          <div>Sua Comissão</div>
          <div></div>
        </div>

        {filteredReferrals.length > 0 ? (
          filteredReferrals.map(referral => (
            <div key={referral.id} className="grid grid-cols-5 gap-4 p-4 border-b border-gray-700 items-center">
              <div className="flex items-center gap-3">
                <img
                  src={referral.referred_user.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'}
                  alt={referral.referred_user.display_name}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <span className="text-white">{referral.referred_user.display_name}</span>
              </div>
              <div className="text-gray-400">{new Date(referral.registration_date).toLocaleDateString('pt-BR')}</div>
              <div className="text-white">R$ {referral.total_spent.toFixed(2)}</div>
              <div className="text-green-400">R$ {referral.commission_earned.toFixed(2)}</div>
              <div className="text-right">
                <button className="text-indigo-400 text-sm hover:text-indigo-300">Detalhes</button>
              </div>
            </div>
          ))
        ) : (
          <div className="p-8 text-center text-gray-400">
            Nenhum indicado encontrado com os filtros atuais.
          </div>
        )}
      </div>
    </div>
  );
}

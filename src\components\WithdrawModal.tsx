import React, { useState } from 'react';
import { X, QrC<PERSON>, Copy, Check, AlertCircle } from 'lucide-react';

interface WithdrawModalProps {
  onClose: () => void;
  onWithdraw: (amount: number, pixKey: string) => void;
  balance: number;
}

export default function WithdrawModal({ onClose, onWithdraw, balance }: WithdrawModalProps) {
  const [amount, setAmount] = useState('');
  const [pixKey, setPixKey] = useState('');
  const [pixKeyType, setPixKeyType] = useState<'cpf' | 'email' | 'phone' | 'random'>('cpf');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onWithdraw(Number(amount), pixKey);
    onClose();
  };

  const presetAmounts = [10, 25, 50, 100, 250, 500].filter(value => value <= balance);

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl w-full max-w-md shadow-xl">
        {/* Header */}
        <div className="p-4 border-b border-gray-700 flex items-center justify-between">
          <h3 className="text-white text-lg font-bold">Sacar</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Balance Info */}
          <div className="bg-gray-700/50 backdrop-blur-sm border border-white/10 rounded-lg p-4">
            <p className="text-gray-400 text-sm">Saldo Disponível</p>
            <p className="text-white text-2xl font-bold">R$ {balance.toFixed(2)}</p>
          </div>

          {/* Amount Input */}
          <div>
            <label className="block text-gray-400 text-sm mb-2">Valor do Saque</label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">R$</span>
              <input
                type="number"
                min="10"
                max={balance}
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 pl-10 pr-3 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                placeholder="0,00"
                required
              />
            </div>
          </div>

          {/* Preset Amounts */}
          {presetAmounts.length > 0 && (
            <div>
              <label className="block text-gray-400 text-sm mb-2">Valores Sugeridos</label>
              <div className="grid grid-cols-3 gap-2">
                {presetAmounts.map((value) => (
                  <button
                    key={value}
                    type="button"
                    onClick={() => setAmount(value.toString())}
                    className="bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-2 hover:bg-gray-600/50 transition-colors"
                  >
                    R$ {value}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* PIX Key Type */}
          <div>
            <label className="block text-gray-400 text-sm mb-2">Tipo de Chave PIX</label>
            <div className="grid grid-cols-2 gap-2 mb-3">
              <button
                type="button"
                onClick={() => setPixKeyType('cpf')}
                className={`py-2 px-4 rounded-lg ${
                  pixKeyType === 'cpf' ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white' : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-600/50 transition-colors'
                }`}
              >
                CPF
              </button>
              <button
                type="button"
                onClick={() => setPixKeyType('email')}
                className={`py-2 px-4 rounded-lg ${
                  pixKeyType === 'email' ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white' : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-600/50 transition-colors'
                }`}
              >
                E-mail
              </button>
              <button
                type="button"
                onClick={() => setPixKeyType('phone')}
                className={`py-2 px-4 rounded-lg ${
                  pixKeyType === 'phone' ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white' : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-600/50 transition-colors'
                }`}
              >
                Celular
              </button>
              <button
                type="button"
                onClick={() => setPixKeyType('random')}
                className={`py-2 px-4 rounded-lg ${
                  pixKeyType === 'random' ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white' : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-600/50 transition-colors'
                }`}
              >
                Chave Aleatória
              </button>
            </div>

            <input
              type="text"
              value={pixKey}
              onChange={(e) => setPixKey(e.target.value)}
              className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              placeholder={
                pixKeyType === 'cpf' ? '123.456.789-00' :
                pixKeyType === 'email' ? '<EMAIL>' :
                pixKeyType === 'phone' ? '(11) 98765-4321' :
                'Chave PIX aleatória'
              }
              required
            />
          </div>

          {/* Warning */}
          <div className="flex items-start gap-2 text-indigo-400 text-sm bg-indigo-500/10 rounded-lg p-3">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <p>
              Certifique-se de que a chave PIX informada está correta.
              O valor será transferido instantaneamente e não poderá ser desfeito.
            </p>
          </div>

          <button
            type="submit"
            disabled={Number(amount) > balance}
            className={`w-full font-bold py-3 rounded-lg ${
              Number(amount) > balance
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white hover:opacity-90 shadow-lg'
            }`}
          >
            {Number(amount) > balance ? 'Saldo Insuficiente' : 'Sacar'}
          </button>
        </form>
      </div>
    </div>
  );
}
import React, { useState } from 'react';
import { Trophy, RotateCcw, ArrowLeft, Crown, Wallet } from 'lucide-react';
import PrizeClaimAnimation from './PrizeClaimAnimation';

interface PlayerResult {
  id: string;
  name: string;
  avatar: string;
  score: number;
  isCurrentPlayer?: boolean;
}

interface GameResultsProps {
  players: PlayerResult[];
  prizePool: number;
  onPlayAgain: () => void;
  onExit: () => void;
}

export default function GameResults({ players, prizePool, onPlayAgain, onExit }: GameResultsProps) {
  const [claimingPrize, setClaimingPrize] = useState(false);
  const [prizeClaimSuccess, setPrizeClaimSuccess] = useState(false);
  const [showPrizeAnimation, setShowPrizeAnimation] = useState(false);

  const sortedPlayers = [...players].sort((a, b) => b.score - a.score);
  const winner = sortedPlayers[0];
  const currentPlayer = players.find(p => p.isCurrentPlayer);
  const currentPlayerRank = sortedPlayers.findIndex(p => p.isCurrentPlayer) + 1;
  const isCurrentPlayerWinner = winner.isCurrentPlayer;

  const handleClaimPrize = () => {
    setClaimingPrize(true);
    // Simula o tempo de processamento do claim
    setTimeout(() => {
      setShowPrizeAnimation(true);
      setTimeout(() => {
        setPrizeClaimSuccess(true);
      }, 2500); // Espera a animação terminar
    }, 1500);
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm z-50 p-4">
      {showPrizeAnimation && (
        <PrizeClaimAnimation
          amount={prizePool}
          onComplete={() => setShowPrizeAnimation(false)}
        />
      )}

      <div className="bg-gray-800 rounded-xl p-8 w-full max-w-lg">
        <div className="text-center space-y-6">
          {/* Troféu e título */}
          <div>
            <Trophy className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-2">Fim de Jogo!</h2>
            {currentPlayer && (
              <p className="text-gray-400">
                Você ficou em {currentPlayerRank}º lugar
              </p>
            )}
          </div>

          {/* Lista de jogadores */}
          <div className="space-y-3">
            {sortedPlayers.map((player, index) => (
              <div
                key={player.id}
                className={`flex items-center gap-3 p-4 rounded-lg ${
                  player.isCurrentPlayer ? 'bg-yellow-400/10' : 'bg-gray-700'
                }`}
              >
                <div className="flex items-center gap-3 flex-1">
                  <div className="relative">
                    <img
                      src={player.avatar}
                      alt={player.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    {index === 0 && (
                      <Crown className="w-4 h-4 text-yellow-400 absolute -top-2 -right-2" />
                    )}
                  </div>
                  <div>
                    <p className={`font-semibold ${player.isCurrentPlayer ? 'text-yellow-400' : 'text-white'}`}>
                      {player.name}
                    </p>
                    <p className="text-sm text-gray-400">
                      {index + 1}º lugar
                    </p>
                  </div>
                </div>
                <p className="text-lg font-bold text-white">
                  {player.score} pts
                </p>
              </div>
            ))}
          </div>

          {/* Prêmio */}
          {isCurrentPlayerWinner && !prizeClaimSuccess && (
            <div className="bg-yellow-400/20 rounded-lg p-4 space-y-3">
              <div className="flex items-center gap-2 justify-center">
                <Wallet className="w-5 h-5 text-yellow-400" />
                <p className="text-yellow-400 font-bold">
                  Prêmio: R$ {prizePool.toFixed(2)}
                </p>
              </div>
              <button
                onClick={handleClaimPrize}
                disabled={claimingPrize}
                className={`w-full py-3 rounded-lg font-bold ${
                  claimingPrize
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-yellow-400 text-black hover:bg-yellow-500'
                }`}
              >
                {claimingPrize ? 'Processando...' : 'Resgatar Prêmio'}
              </button>
            </div>
          )}

          {/* Botões de ação - Mostrar apenas quando o jogador atual NÃO é o vencedor ou quando já resgatou o prêmio */}
          {(!isCurrentPlayerWinner || prizeClaimSuccess) && (
            <div className="flex gap-3">
              {/* Botão de Sair - Sempre visível quando não é o vencedor ou já resgatou o prêmio */}
              <button
                onClick={onExit}
                className="flex-1 bg-gray-700 text-white font-bold py-3 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-600"
              >
                <ArrowLeft className="w-5 h-5" />
                Sair
              </button>

              {/* Botão de Jogar Novamente - Sempre visível */}
              <button
                onClick={onPlayAgain}
                className="flex-1 bg-yellow-400 text-black font-bold py-3 rounded-lg flex items-center justify-center gap-2 hover:bg-yellow-500"
              >
                <RotateCcw className="w-5 h-5" />
                Jogar Novamente
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
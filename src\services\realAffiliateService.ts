import { apiService } from './apiService';

export interface AffiliateStats {
  id: string;
  user_id: string;
  affiliate_code: string;
  commission_rate: number;
  total_referrals: number;
  active_referrals: number;
  total_earnings: number;
  pending_earnings: number;
  paid_earnings: number;
  created_at: string;
  updated_at: string;
}

export interface AffiliateReferral {
  id: string;
  affiliate_id: string;
  referred_user_id: string;
  status: 'pending' | 'active' | 'inactive';
  registration_date: string;
  first_deposit_date?: string;
  total_spent: number;
  commission_earned: number;
  referred_user: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

export interface AffiliateCommission {
  id: string;
  affiliate_id: string;
  referral_id: string;
  amount: number;
  commission_type: 'registration' | 'deposit' | 'match_fee' | 'tournament_fee';
  status: 'pending' | 'paid' | 'cancelled';
  created_at: string;
  paid_at?: string;
  description: string;
}

class RealAffiliateService {
  
  // Obter estatísticas do programa de afiliados
  async getAffiliateStats(): Promise<AffiliateStats | null> {
    try {
      console.log('🌐 Buscando estatísticas de afiliados da API...');
      
      const response = await apiService.get<AffiliateStats>('/affiliate/stats');
      
      if (response.success && response.data) {
        console.log('✅ Estatísticas de afiliados carregadas da API');
        return response.data;
      } else {
        console.warn('⚠️ API retornou erro, usando dados mock como fallback');
        return this.getMockAffiliateStats();
      }
    } catch (error) {
      console.error('❌ Erro ao buscar estatísticas de afiliados:', error);
      return this.getMockAffiliateStats();
    }
  }

  // Obter lista de indicações
  async getReferrals(page: number = 1, limit: number = 10): Promise<AffiliateReferral[]> {
    try {
      console.log('🌐 Buscando indicações da API...');
      
      const response = await apiService.get<AffiliateReferral[]>('/affiliate/referrals', {
        params: { page, limit }
      });
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} indicações carregadas da API`);
        return response.data;
      } else {
        console.warn('⚠️ API retornou erro, usando dados mock como fallback');
        return this.getMockReferrals();
      }
    } catch (error) {
      console.error('❌ Erro ao buscar indicações:', error);
      return this.getMockReferrals();
    }
  }

  // Obter histórico de comissões
  async getCommissions(page: number = 1, limit: number = 10): Promise<AffiliateCommission[]> {
    try {
      console.log('🌐 Buscando comissões da API...');
      
      const response = await apiService.get<AffiliateCommission[]>('/affiliate/commissions', {
        params: { page, limit }
      });
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} comissões carregadas da API`);
        return response.data;
      } else {
        console.warn('⚠️ API retornou erro, usando dados mock como fallback');
        return this.getMockCommissions();
      }
    } catch (error) {
      console.error('❌ Erro ao buscar comissões:', error);
      return this.getMockCommissions();
    }
  }

  // Gerar novo código de afiliado
  async generateAffiliateCode(): Promise<string | null> {
    try {
      console.log('🌐 Gerando código de afiliado...');
      
      const response = await apiService.post<{ affiliate_code: string }>('/affiliate/generate-code');
      
      if (response.success && response.data) {
        console.log('✅ Código de afiliado gerado:', response.data.affiliate_code);
        return response.data.affiliate_code;
      } else {
        console.warn('⚠️ Erro ao gerar código, usando mock');
        return `PLAY${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
      }
    } catch (error) {
      console.error('❌ Erro ao gerar código de afiliado:', error);
      return `PLAY${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
    }
  }

  // Solicitar saque de comissões
  async requestWithdrawal(amount: number): Promise<boolean> {
    try {
      console.log('🌐 Solicitando saque de comissões...');
      
      const response = await apiService.post('/affiliate/withdraw', { amount });
      
      if (response.success) {
        console.log('✅ Saque solicitado com sucesso');
        return true;
      } else {
        console.warn('⚠️ Erro ao solicitar saque');
        return false;
      }
    } catch (error) {
      console.error('❌ Erro ao solicitar saque:', error);
      return false;
    }
  }

  // Dados mock como fallback
  private getMockAffiliateStats(): AffiliateStats {
    return {
      id: 'aff_1',
      user_id: 'user_1',
      affiliate_code: 'PLAYJOHN123',
      commission_rate: 0.15,
      total_referrals: 24,
      active_referrals: 18,
      total_earnings: 1250.50,
      pending_earnings: 320.75,
      paid_earnings: 929.75,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T12:00:00Z'
    };
  }

  private getMockReferrals(): AffiliateReferral[] {
    return [
      {
        id: 'ref_1',
        affiliate_id: 'aff_1',
        referred_user_id: 'user_2',
        status: 'active',
        registration_date: '2024-01-10T10:00:00Z',
        first_deposit_date: '2024-01-10T11:00:00Z',
        total_spent: 450.00,
        commission_earned: 67.50,
        referred_user: {
          username: 'ana_silva',
          display_name: 'Ana Silva',
          avatar_url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
        }
      },
      {
        id: 'ref_2',
        affiliate_id: 'aff_1',
        referred_user_id: 'user_3',
        status: 'active',
        registration_date: '2024-01-08T14:30:00Z',
        first_deposit_date: '2024-01-09T09:15:00Z',
        total_spent: 280.00,
        commission_earned: 42.00,
        referred_user: {
          username: 'carlos_gamer',
          display_name: 'Carlos Gamer',
          avatar_url: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
        }
      }
    ];
  }

  private getMockCommissions(): AffiliateCommission[] {
    return [
      {
        id: 'comm_1',
        affiliate_id: 'aff_1',
        referral_id: 'ref_1',
        amount: 25.50,
        commission_type: 'match_fee',
        status: 'paid',
        created_at: '2024-01-12T15:30:00Z',
        paid_at: '2024-01-15T10:00:00Z',
        description: 'Comissão por taxa de partida - Ana Silva'
      },
      {
        id: 'comm_2',
        affiliate_id: 'aff_1',
        referral_id: 'ref_2',
        amount: 15.00,
        commission_type: 'deposit',
        status: 'pending',
        created_at: '2024-01-14T09:15:00Z',
        description: 'Comissão por depósito - Carlos Gamer'
      }
    ];
  }
}

export const realAffiliateService = new RealAffiliateService();

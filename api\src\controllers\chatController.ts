import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, MessageData } from '../types/api';
import { logger, logBusinessEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';

/**
 * Get private chat messages
 */
export const getPrivateChatMessages = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { friendId } = req.params;
    const { page, limit } = extractPagination(req.query);

    // Check if users are friends
    const { data: friendship } = await supabaseAdmin
      .from('friendships')
      .select('id')
      .or(`and(requester_user_id.eq.${user.id},addressee_user_id.eq.${friendId}),and(requester_user_id.eq.${friendId},addressee_user_id.eq.${user.id})`)
      .eq('status', 'accepted')
      .single();

    if (!friendship) {
      const response: ApiResponse = {
        success: false,
        error: 'You can only chat with friends'
      };
      res.status(403).json(response);
      return;
    }

    const offset = (page - 1) * limit;

    const { data: messages, error, count } = await supabaseAdmin
      .from('private_messages')
      .select(`
        id, message, message_type, attachment_url, created_at, is_read,
        sender:users!private_messages_sender_user_id_fkey (
          id, username, display_name, avatar_url
        ),
        receiver:users!private_messages_receiver_user_id_fkey (
          id, username, display_name, avatar_url
        )
      `, { count: 'exact' })
      .or(`and(sender_user_id.eq.${user.id},receiver_user_id.eq.${friendId}),and(sender_user_id.eq.${friendId},receiver_user_id.eq.${user.id})`)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get private chat messages:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve chat messages'
      };
      res.status(500).json(response);
      return;
    }

    // Mark messages as read
    await supabaseAdmin
      .from('private_messages')
      .update({ is_read: true })
      .eq('sender_user_id', friendId)
      .eq('receiver_user_id', user.id)
      .eq('is_read', false);

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        messages: (messages || []).reverse() // Reverse to show oldest first
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get private chat messages error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Send private message
 */
export const sendPrivateMessage = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { friendId } = req.params;
    const messageData: MessageData = req.body;

    // Check if users are friends
    const { data: friendship } = await supabaseAdmin
      .from('friendships')
      .select('id')
      .or(`and(requester_user_id.eq.${user.id},addressee_user_id.eq.${friendId}),and(requester_user_id.eq.${friendId},addressee_user_id.eq.${user.id})`)
      .eq('status', 'accepted')
      .single();

    if (!friendship) {
      const response: ApiResponse = {
        success: false,
        error: 'You can only send messages to friends'
      };
      res.status(403).json(response);
      return;
    }

    // Verify receiver exists and is active
    const { data: receiver, error: receiverError } = await supabaseAdmin
      .from('users')
      .select('id, username, status')
      .eq('id', friendId)
      .eq('status', 'active')
      .single();

    if (receiverError || !receiver) {
      const response: ApiResponse = {
        success: false,
        error: 'Receiver not found or inactive'
      };
      res.status(404).json(response);
      return;
    }

    // Create message
    const { data: message, error: messageError } = await supabaseAdmin
      .from('private_messages')
      .insert({
        sender_user_id: user.id,
        receiver_user_id: friendId,
        message: messageData.message,
        message_type: messageData.message_type || 'text',
        attachment_url: messageData.attachment_url,
        reply_to_message_id: messageData.reply_to_message_id
      })
      .select(`
        id, message, message_type, attachment_url, created_at,
        sender:users!private_messages_sender_user_id_fkey (
          id, username, display_name, avatar_url
        )
      `)
      .single();

    if (messageError || !message) {
      logger.error('Failed to send private message:', messageError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to send message'
      };
      res.status(500).json(response);
      return;
    }

    // Create notification for receiver
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id: friendId,
        type: 'message',
        title: 'Nova mensagem',
        message: `${user.username} enviou uma mensagem`,
        data: {
          message_id: message.id,
          sender_user_id: user.id,
          sender_username: user.username
        }
      });

    logBusinessEvent('private_message_sent', user.id, {
      receiver_user_id: friendId,
      message_id: message.id,
      message_type: messageData.message_type
    });

    const response: ApiResponse = {
      success: true,
      message: 'Message sent successfully',
      data: {
        message
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Send private message error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get chat notifications (unread message counts)
 */
export const getChatNotifications = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    // Get unread message counts per friend
    const { data: unreadCounts, error } = await supabaseAdmin
      .from('private_messages')
      .select(`
        sender_user_id,
        sender:users!private_messages_sender_user_id_fkey (
          id, username, display_name, avatar_url
        )
      `)
      .eq('receiver_user_id', user.id)
      .eq('is_read', false)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Failed to get chat notifications:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve chat notifications'
      };
      res.status(500).json(response);
      return;
    }

    // Group by sender and count
    const notificationMap = new Map();
    (unreadCounts || []).forEach((msg: any) => {
      const senderId = msg.sender_user_id;
      if (!notificationMap.has(senderId)) {
        notificationMap.set(senderId, {
          sender: msg.sender,
          unread_count: 0
        });
      }
      notificationMap.get(senderId).unread_count++;
    });

    const notifications = Array.from(notificationMap.values());
    const totalUnread = notifications.reduce((sum, notif) => sum + notif.unread_count, 0);

    const response: ApiResponse = {
      success: true,
      data: {
        notifications,
        total_unread: totalUnread
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get chat notifications error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get recent conversations
 */
export const getRecentConversations = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);

    // Get latest message from each conversation
    const { data: conversations, error } = await supabaseAdmin
      .rpc('get_recent_conversations', {
        user_id_param: user.id,
        limit_param: limit,
        offset_param: (page - 1) * limit
      });

    if (error) {
      logger.error('Failed to get recent conversations:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve recent conversations'
      };
      res.status(500).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        conversations: conversations || []
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get recent conversations error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Mark conversation as read
 */
export const markConversationAsRead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { friendId } = req.params;

    // Mark all messages from friend as read
    const { error } = await supabaseAdmin
      .from('private_messages')
      .update({ is_read: true })
      .eq('sender_user_id', friendId)
      .eq('receiver_user_id', user.id)
      .eq('is_read', false);

    if (error) {
      logger.error('Failed to mark conversation as read:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to mark conversation as read'
      };
      res.status(500).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      message: 'Conversation marked as read'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Mark conversation as read error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Delete message
 */
export const deleteMessage = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { messageId } = req.params;

    // Get message details
    const { data: message, error: messageError } = await supabaseAdmin
      .from('private_messages')
      .select('id, sender_user_id, receiver_user_id')
      .eq('id', messageId)
      .single();

    if (messageError || !message) {
      const response: ApiResponse = {
        success: false,
        error: 'Message not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if user can delete this message
    if (message.sender_user_id !== user.id) {
      const response: ApiResponse = {
        success: false,
        error: 'You can only delete your own messages'
      };
      res.status(403).json(response);
      return;
    }

    // Soft delete message
    const { error: deleteError } = await supabaseAdmin
      .from('private_messages')
      .update({ 
        message: '[Mensagem deletada]',
        is_deleted: true,
        deleted_at: new Date().toISOString()
      })
      .eq('id', messageId);

    if (deleteError) {
      logger.error('Failed to delete message:', deleteError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to delete message'
      };
      res.status(500).json(response);
      return;
    }

    logBusinessEvent('message_deleted', user.id, {
      message_id: messageId,
      receiver_user_id: message.receiver_user_id
    });

    const response: ApiResponse = {
      success: true,
      message: 'Message deleted successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Delete message error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

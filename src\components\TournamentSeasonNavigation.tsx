import React from 'react';
import { TournamentSeason } from '../types/tournament';
import { Arrow<PERSON><PERSON><PERSON>, ArrowRight, Trophy } from 'lucide-react';

interface TournamentSeasonNavigationProps {
  seasons: TournamentSeason[];
  activeSeason: string;
  onSeasonChange: (seasonId: string) => void;
  onShowWinners?: () => void;
}

export default function TournamentSeasonNavigation({
  seasons,
  activeSeason,
  onSeasonChange,
  onShowWinners
}: TournamentSeasonNavigationProps) {
  // Ordenar temporadas por ordem
  const sortedSeasons = [...seasons].sort((a, b) => a.order - b.order);
  
  // Encontrar índice da temporada ativa
  const activeSeasonIndex = sortedSeasons.findIndex(s => s.id === activeSeason);
  
  // Determinar temporadas anterior e próxima
  const previousSeason = activeSeasonIndex > 0 ? sortedSeasons[activeSeasonIndex - 1] : null;
  const nextSeason = activeSeasonIndex < sortedSeasons.length - 1 ? sortedSeasons[activeSeasonIndex + 1] : null;
  
  // Verificar se a temporada atual está concluída
  const isCurrentSeasonCompleted = sortedSeasons[activeSeasonIndex]?.status === 'completed';
  
  return (
    <div className="flex flex-wrap gap-2 mb-4">
      {/* Navegação para temporada anterior */}
      {previousSeason && (
        <button
          onClick={() => onSeasonChange(previousSeason.id)}
          className="bg-gray-800/50 text-gray-400 px-3 py-1 rounded-lg flex items-center gap-1 text-sm hover:bg-gray-700/50"
        >
          <ArrowLeft className="w-4 h-4" />
          {previousSeason.name}
        </button>
      )}
      
      {/* Navegação para próxima temporada */}
      {nextSeason && (
        <button
          onClick={() => onSeasonChange(nextSeason.id)}
          className="bg-gray-800/50 text-gray-400 px-3 py-1 rounded-lg flex items-center gap-1 text-sm hover:bg-gray-700/50"
        >
          {nextSeason.name}
          <ArrowRight className="w-4 h-4" />
        </button>
      )}
      
      {/* Botão para ver todos os vencedores */}
      {isCurrentSeasonCompleted && onShowWinners && (
        <button
          onClick={onShowWinners}
          className="bg-yellow-500/20 text-yellow-400 px-3 py-1 rounded-lg flex items-center gap-1 text-sm hover:bg-yellow-500/30 ml-auto"
        >
          <Trophy className="w-4 h-4" />
          Ver Vencedores
        </button>
      )}
    </div>
  );
}

import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { X, Upload, Users, Globe, Lock, Shield, UserPlus, MessageSquare, Info, FileText, Tag } from 'lucide-react';

interface Game {
  id: string;
  title: string;
  icon: string;
}

export default function CreateClubPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const gameId = searchParams.get('game');
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [rules, setRules] = useState('');
  const [selectedGame, setSelectedGame] = useState<string>(gameId || '');
  const [region, setRegion] = useState<string>('');
  const [privacyType, setPrivacyType] = useState<'public' | 'approval' | 'invite'>('public');
  const [logo, setLogo] = useState<string | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const games: Game[] = [
    { id: 'cs2', title: 'Counter-Strike 2', icon: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' },
    { id: 'apex', title: 'Apex Legends', icon: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' },
    { id: 'valorant', title: 'VALORANT', icon: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' },
    { id: 'fifa', title: 'FIFA 24', icon: 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' },
    { id: 'lol', title: 'League of Legends', icon: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' },
    { id: 'fortnite', title: 'Fortnite', icon: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' }
  ];

  const clubTags = [
    'Competitivo', 'Casual', 'Torneios', 'Treinos', 'Iniciantes', 'Profissional',
    'Amigável', 'Eventos', 'Streaming', 'Campeonatos', 'Diversão', 'Ranqueadas'
  ];

  const regions = [
    { id: 'na', name: 'América do Norte' },
    { id: 'sa', name: 'América do Sul' },
    { id: 'eu', name: 'Europa' },
    { id: 'asia', name: 'Ásia' }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!name || !selectedGame || !region) {
      alert('Por favor, preencha todos os campos obrigatórios.');
      return;
    }

    // Create club data object
    const clubData = {
      name,
      description,
      rules,
      game: selectedGame,
      region,
      privacyType,
      tags: selectedTags,
      logo
    };

    // Here you would typically make an API call to create the club
    console.log('Criando clube:', clubData);

    // Navigate to the new club page
    navigate('/clubs/new-club-id');
  };

  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      if (selectedTags.length < 5) {
        setSelectedTags([...selectedTags, tag]);
      }
    }
  };

  return (
    <div className="max-w-3xl mx-auto space-y-6 pb-20">
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-white">Criar um Clube</h1>
          <button
            onClick={() => navigate('/social')}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <p className="text-gray-400">
          Crie seu próprio clube para reunir jogadores com interesses semelhantes, participar de torneios e organizar eventos.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 space-y-6">
          <div className="flex items-center gap-2 text-white font-semibold">
            <Info className="w-5 h-5 text-indigo-400" />
            <h2>Informações Básicas</h2>
          </div>

          <div className="flex flex-col md:flex-row gap-6">
            {/* Logo Upload */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Logo do Clube</label>
              <div className="relative w-32 h-32 bg-gray-800/70 border border-white/10 rounded-xl overflow-hidden hover:border-indigo-400/50 transition-colors">
                {logo ? (
                  <img
                    src={logo}
                    alt="Club logo"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-400">
                    <Upload className="w-8 h-8 mb-2" />
                    <span className="text-sm">Upload</span>
                  </div>
                )}
                <input
                  type="file"
                  className="absolute inset-0 opacity-0 cursor-pointer"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      const reader = new FileReader();
                      reader.onload = (e) => {
                        setLogo(e.target?.result as string);
                      };
                      reader.readAsDataURL(file);
                    }
                  }}
                />
              </div>
            </div>

            <div className="flex-1 space-y-4">
              {/* Club Name */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Nome do Clube</label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full bg-gray-800/70 border border-white/10 text-white rounded-xl py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-transparent"
                  placeholder="Digite o nome do clube"
                  required
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Descrição</label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="w-full bg-gray-800/70 border border-white/10 text-white rounded-xl py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-transparent resize-none h-24"
                  placeholder="Descreva seu clube em poucas palavras"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Game and Region Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 space-y-6">
          <div className="flex items-center gap-2 text-white font-semibold">
            <Users className="w-5 h-5 text-indigo-400" />
            <h2>Jogo e Região</h2>
          </div>

          {/* Game Selection */}
          <div>
            <label className="block text-gray-400 text-sm mb-2">Jogo Principal</label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {games.map(game => (
                <button
                  type="button"
                  key={game.id}
                  onClick={() => setSelectedGame(game.id)}
                  className={`p-4 rounded-xl flex flex-col items-center gap-2 transition-all ${
                    selectedGame === game.id
                      ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                      : 'bg-gray-800/70 border border-white/10 text-white hover:bg-gray-700/50'
                  }`}
                >
                  <img
                    src={game.icon}
                    alt={game.title}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                  <span className="text-sm font-medium text-center">{game.title}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Region Selection */}
          <div>
            <label className="block text-gray-400 text-sm mb-2">Região</label>
            <select
              value={region}
              onChange={(e) => setRegion(e.target.value)}
              className="w-full bg-gray-800/70 border border-white/10 text-white rounded-xl py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-transparent"
              required
            >
              <option value="">Selecione uma região</option>
              {regions.map(region => (
                <option key={region.id} value={region.id}>
                  {region.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Club Rules Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 space-y-6">
          <div className="flex items-center gap-2 text-white font-semibold">
            <FileText className="w-5 h-5 text-indigo-400" />
            <h2>Regras e Tags</h2>
          </div>

          {/* Club Rules */}
          <div>
            <label className="block text-gray-400 text-sm mb-2">Regras do Clube</label>
            <textarea
              value={rules}
              onChange={(e) => setRules(e.target.value)}
              className="w-full bg-gray-800/70 border border-white/10 text-white rounded-xl py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-transparent resize-none h-32"
              placeholder="Defina as regras e diretrizes para os membros do seu clube"
            />
          </div>

          {/* Tags Selection */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-gray-400 text-sm">Tags (máximo 5)</label>
              <span className="text-xs text-gray-400">{selectedTags.length}/5 selecionadas</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {clubTags.map(tag => (
                <button
                  key={tag}
                  type="button"
                  onClick={() => toggleTag(tag)}
                  disabled={!selectedTags.includes(tag) && selectedTags.length >= 5}
                  className={`px-3 py-1.5 rounded-full text-sm flex items-center gap-1.5 transition-all ${selectedTags.includes(tag)
                    ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white'
                    : selectedTags.length >= 5
                      ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
                      : 'bg-gray-800/70 border border-white/10 text-white hover:bg-gray-700/50'}`}
                >
                  {selectedTags.includes(tag) && <span>✓</span>}
                  {tag}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Privacy and Membership Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 space-y-6">
          <div className="flex items-center gap-2 text-white font-semibold">
            <Shield className="w-5 h-5 text-indigo-400" />
            <h2>Privacidade e Associação</h2>
          </div>

          {/* Privacy Setting */}
          <div>
            <label className="block text-gray-400 text-sm mb-2">Tipo de Entrada</label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <button
                type="button"
                onClick={() => setPrivacyType('public')}
                className={`p-4 rounded-xl flex flex-col items-center gap-2 transition-all ${
                  privacyType === 'public'
                    ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                    : 'bg-gray-800/70 border border-white/10 text-white hover:bg-gray-700/50'
                }`}
              >
                <Globe className="w-6 h-6" />
                <span className="font-semibold">Aberta</span>
                <p className="text-xs text-center">
                  Qualquer um pode entrar automaticamente
                </p>
              </button>
              <button
                type="button"
                onClick={() => setPrivacyType('approval')}
                className={`p-4 rounded-xl flex flex-col items-center gap-2 transition-all ${
                  privacyType === 'approval'
                    ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                    : 'bg-gray-800/70 border border-white/10 text-white hover:bg-gray-700/50'
                }`}
              >
                <UserPlus className="w-6 h-6" />
                <span className="font-semibold">Aprovação</span>
                <p className="text-xs text-center">
                  Jogadores podem solicitar entrada, sujeita a aprovação
                </p>
              </button>
              <button
                type="button"
                onClick={() => setPrivacyType('invite')}
                className={`p-4 rounded-xl flex flex-col items-center gap-2 transition-all ${
                  privacyType === 'invite'
                    ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                    : 'bg-gray-800/70 border border-white/10 text-white hover:bg-gray-700/50'
                }`}
              >
                <Lock className="w-6 h-6" />
                <span className="font-semibold">Somente Convite</span>
                <p className="text-xs text-center">
                  Apenas jogadores convidados podem entrar
                </p>
              </button>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold px-8 py-3 rounded-xl hover:opacity-90 transition-opacity shadow-lg flex items-center gap-2"
          >
            <Users className="w-5 h-5" />
            Criar Clube
          </button>
        </div>
      </form>
    </div>
  );
}
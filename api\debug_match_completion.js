const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugMatchCompletion() {
  console.log('🔍 Investigando problema de finalização de partida...\n');

  try {
    // Buscar partidas recentes completadas
    console.log('1. Analisando partidas recentes...');
    const { data: recentMatches, error: matchError } = await supabase
      .from('matches')
      .select(`
        id,
        status,
        winner_user_id,
        room_id,
        created_at,
        finished_at,
        match_participants!inner(
          user_id,
          placement,
          score,
          stats,
          users!inner(username)
        )
      `)
      .eq('status', 'completed')
      .order('finished_at', { ascending: false })
      .limit(3);

    if (matchError) {
      console.error('❌ Erro ao buscar partidas:', matchError);
      return;
    }

    console.log(`📊 Analisando ${recentMatches.length} partidas recentes:\n`);

    recentMatches.forEach((match, index) => {
      console.log(`${index + 1}. Match ID: ${match.id}`);
      console.log(`   Room ID: ${match.room_id}`);
      console.log(`   Status: ${match.status}`);
      console.log(`   Vencedor: ${match.winner_user_id}`);
      console.log(`   Finalizada: ${match.finished_at}`);
      console.log(`   Participantes:`);
      
      match.match_participants.forEach(p => {
        const isWinner = p.user_id === match.winner_user_id;
        const hasStats = p.stats && Object.keys(p.stats).length > 0;
        const hasResult = p.stats && p.stats.result;
        
        console.log(`     ${isWinner ? '🏆' : '  '} ${p.users.username} (${p.user_id})`);
        console.log(`       Colocação: ${p.placement}`);
        console.log(`       Score: ${p.score}`);
        console.log(`       Tem stats: ${hasStats ? 'SIM' : 'NÃO'}`);
        console.log(`       Resultado: ${hasResult || 'N/A'}`);
        console.log(`       Stats completas: ${JSON.stringify(p.stats || {})}`);
      });
      
      // Verificar se todos os participantes submeteram resultado
      const participantsWithResults = match.match_participants.filter(p => 
        p.stats && p.stats.result && p.stats.submitted_at
      ).length;
      const totalParticipants = match.match_participants.length;
      
      console.log(`   📊 Resultados submetidos: ${participantsWithResults}/${totalParticipants}`);
      
      if (participantsWithResults < totalParticipants) {
        console.log(`   ⚠️ PROBLEMA: Nem todos os participantes submeteram resultado!`);
        match.match_participants.forEach(p => {
          const hasSubmitted = p.stats && p.stats.result && p.stats.submitted_at;
          if (!hasSubmitted) {
            console.log(`     ❌ ${p.users.username} não submeteu resultado`);
          }
        });
      } else {
        console.log(`   ✅ Todos os participantes submeteram resultado`);
      }
      
      console.log('');
    });

    // Simular a lógica de verificação de finalização
    console.log('2. Simulando lógica de finalização...\n');
    
    const testMatch = recentMatches[0];
    if (testMatch) {
      console.log(`🧪 Testando match: ${testMatch.id}`);
      
      // Replicar a lógica do backend
      const { data: participants, error: participantsError } = await supabase
        .from('match_participants')
        .select('user_id, stats')
        .eq('match_id', testMatch.id);

      if (participantsError) {
        console.error('❌ Erro ao buscar participantes:', participantsError);
        return;
      }

      console.log('📋 Participantes encontrados:');
      participants.forEach(p => {
        console.log(`  - User ${p.user_id}: ${JSON.stringify(p.stats || {})}`);
      });

      // Lógica de verificação (igual ao backend)
      const participantsWithResults = participants.filter(p => 
        p.stats && 
        p.stats.result && 
        p.stats.submitted_at
      );
      
      const totalParticipants = participants.length;
      const allResultsSubmitted = participantsWithResults.length === totalParticipants;
      
      console.log(`\n🔍 Análise da lógica:`);
      console.log(`  - Total de participantes: ${totalParticipants}`);
      console.log(`  - Participantes com resultado: ${participantsWithResults.length}`);
      console.log(`  - Todos submeteram: ${allResultsSubmitted ? 'SIM' : 'NÃO'}`);
      
      if (!allResultsSubmitted) {
        console.log(`\n❌ PROBLEMA IDENTIFICADO:`);
        console.log(`  - A partida foi marcada como 'completed' no banco`);
        console.log(`  - Mas nem todos os participantes submeteram resultado`);
        console.log(`  - Isso significa que o evento 'match_completed' não foi enviado`);
        console.log(`  - Jogadores que não submeteram ficam na tela de carregamento`);
        
        participants.forEach(p => {
          const hasSubmitted = p.stats && p.stats.result && p.stats.submitted_at;
          if (!hasSubmitted) {
            console.log(`  - Jogador ${p.user_id} não submeteu resultado`);
          }
        });
      } else {
        console.log(`\n✅ Lógica OK: Todos submeteram resultado`);
      }
    }

    // Verificar rooms ativas
    console.log('\n3. Verificando rooms ativas...');
    const { data: activeRooms, error: roomError } = await supabase
      .from('game_rooms')
      .select(`
        id,
        status,
        current_players,
        max_players,
        created_at,
        room_participants!inner(
          user_id,
          users!inner(username)
        )
      `)
      .in('status', ['playing', 'finished'])
      .order('created_at', { ascending: false })
      .limit(5);

    if (roomError) {
      console.error('❌ Erro ao buscar rooms:', roomError);
    } else {
      console.log(`📊 Rooms ativas/finalizadas: ${activeRooms.length}`);
      activeRooms.forEach(room => {
        console.log(`  - Room ${room.id}: ${room.status} (${room.current_players}/${room.max_players})`);
        room.room_participants.forEach(p => {
          console.log(`    - ${p.users.username}`);
        });
      });
    }

  } catch (error) {
    console.error('❌ Erro durante investigação:', error);
  }
}

// Executar investigação
debugMatchCompletion()
  .then(() => {
    console.log('\n✅ Investigação concluída!');
    console.log('\n💡 Possíveis soluções:');
    console.log('1. Implementar timeout para finalizar partida automaticamente');
    console.log('2. Permitir que primeiro jogador force finalização');
    console.log('3. Adicionar botão "Sair da partida" na tela de carregamento');
    console.log('4. Melhorar lógica de detecção de jogadores desconectados');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

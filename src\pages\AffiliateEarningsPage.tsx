import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Search, Filter, Calendar, ChevronDown, ChevronUp, Loader2, DollarSign, TrendingUp } from 'lucide-react';
import { realAffiliateService, AffiliateCommission } from '../services/realAffiliateService';

interface EarningHistory {
  id: string;
  date: string;
  amount: number;
  source: string;
  status: 'pending' | 'completed';
}

export default function AffiliateEarningsPage() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'paid' | 'cancelled'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'type'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [commissions, setCommissions] = useState<AffiliateCommission[]>([]);

  // Carregar dados da API
  useEffect(() => {
    const loadCommissions = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando comissões...');

        const commissionsData = await realAffiliateService.getCommissions(1, 50);
        setCommissions(commissionsData);

        console.log(`✅ ${commissionsData.length} comissões carregadas`);

      } catch (err) {
        console.error('❌ Erro ao carregar comissões:', err);
        setError('Erro ao carregar comissões. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadCommissions();
  }, []);

  // Filtrar e ordenar comissões
  const filteredCommissions = commissions
    .filter(commission => {
      const matchesSearch = commission.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || commission.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      if (sortBy === 'date') {
        const dateA = new Date(a.created_at);
        const dateB = new Date(b.created_at);
        return sortOrder === 'asc' ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
      } else if (sortBy === 'amount') {
        return sortOrder === 'asc' ? a.amount - b.amount : b.amount - a.amount;
      } else {
        return sortOrder === 'asc' ? a.commission_type.localeCompare(b.commission_type) : b.commission_type.localeCompare(a.commission_type);
      }
    });

  const toggleSort = (field: 'date' | 'amount' | 'type') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const totalEarnings = filteredCommissions.reduce((sum, commission) => sum + commission.amount, 0);
  const pendingEarnings = filteredCommissions.filter(c => c.status === 'pending').reduce((sum, commission) => sum + commission.amount, 0);
  const paidEarnings = filteredCommissions.filter(c => c.status === 'paid').reduce((sum, commission) => sum + commission.amount, 0);

  const getCommissionTypeLabel = (type: string) => {
    const types = {
      'registration': 'Cadastro',
      'deposit': 'Depósito',
      'match_fee': 'Taxa de Partida',
      'tournament_fee': 'Taxa de Torneio'
    };
    return types[type as keyof typeof types] || type;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'pending': 'text-yellow-400 bg-yellow-500/20',
      'paid': 'text-green-400 bg-green-500/20',
      'cancelled': 'text-red-400 bg-red-500/20'
    };
    return colors[status as keyof typeof colors] || 'text-gray-400 bg-gray-500/20';
  };

  const getStatusLabel = (status: string) => {
    const labels = {
      'pending': 'Pendente',
      'paid': 'Pago',
      'cancelled': 'Cancelado'
    };
    return labels[status as keyof typeof labels] || status;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-indigo-500 animate-spin mx-auto mb-4" />
          <p className="text-gray-400">Carregando ganhos...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/affiliate-program')}
          className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Voltar</span>
        </button>
        <h1 className="text-2xl font-bold text-white">Histórico de Ganhos</h1>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 relative min-w-[200px]">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Buscar por nome..."
            className="w-full bg-gray-800/50 text-white rounded-xl py-3 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 backdrop-blur-sm border border-white/10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
            className="bg-gray-800/50 text-white rounded-xl py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 backdrop-blur-sm border border-white/10"
          >
            <option value="all">Todos os Status</option>
            <option value="pending">Pendente</option>
            <option value="paid">Pago</option>
            <option value="cancelled">Cancelado</option>
          </select>

          <button
            onClick={() => toggleSort('date')}
            className={`flex items-center gap-1 px-4 py-2 rounded-xl backdrop-blur-sm border ${
              sortBy === 'date'
                ? 'bg-indigo-500/20 border-indigo-400 text-indigo-400'
                : 'bg-gray-800/50 border-white/10 text-gray-400 hover:text-white'
            }`}
          >
            Data
            {sortBy === 'date' && (
              sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
            )}
          </button>

          <button
            onClick={() => toggleSort('amount')}
            className={`flex items-center gap-1 px-4 py-2 rounded-xl backdrop-blur-sm border ${
              sortBy === 'amount'
                ? 'bg-indigo-500/20 border-indigo-400 text-indigo-400'
                : 'bg-gray-800/50 border-white/10 text-gray-400 hover:text-white'
            }`}
          >
            Valor
            {sortBy === 'amount' && (
              sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
            )}
          </button>

          <button
            onClick={() => toggleSort('type')}
            className={`flex items-center gap-1 px-4 py-2 rounded-xl backdrop-blur-sm border ${
              sortBy === 'type'
                ? 'bg-indigo-500/20 border-indigo-400 text-indigo-400'
                : 'bg-gray-800/50 border-white/10 text-gray-400 hover:text-white'
            }`}
          >
            Tipo
            {sortBy === 'type' && (
              sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <DollarSign className="w-8 h-8 text-white" />
            <div>
              <p className="text-white/70 text-sm">Total de Ganhos</p>
              <p className="text-white font-bold text-xl">R$ {totalEarnings.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <TrendingUp className="w-8 h-8 text-white" />
            <div>
              <p className="text-white/70 text-sm">Pendente</p>
              <p className="text-white font-bold text-xl">R$ {pendingEarnings.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <Calendar className="w-8 h-8 text-white" />
            <div>
              <p className="text-white/70 text-sm">Recebido</p>
              <p className="text-white font-bold text-xl">R$ {paidEarnings.toFixed(2)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Earnings Table */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden">
        <div className="grid grid-cols-5 gap-4 p-4 border-b border-gray-700 text-gray-400 text-sm">
          <div>Data</div>
          <div>Tipo</div>
          <div>Descrição</div>
          <div>Valor</div>
          <div>Status</div>
        </div>

        {filteredCommissions.length > 0 ? (
          filteredCommissions.map(commission => (
            <div key={commission.id} className="grid grid-cols-5 gap-4 p-4 border-b border-gray-700 items-center">
              <div className="text-gray-400">
                {new Date(commission.created_at).toLocaleDateString('pt-BR')}
              </div>
              <div className="text-white">
                {getCommissionTypeLabel(commission.commission_type)}
              </div>
              <div className="text-white">
                {commission.description}
              </div>
              <div className="text-green-400 font-semibold">
                R$ {commission.amount.toFixed(2)}
              </div>
              <div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(commission.status)}`}>
                  {getStatusLabel(commission.status)}
                </span>
              </div>
            </div>
          ))
        ) : (
          <div className="p-8 text-center text-gray-400">
            Nenhuma comissão encontrada com os filtros atuais.
          </div>
        )}
      </div>
    </div>
  );
}

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Service Key exists:', !!supabaseServiceKey);

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testJoinRoomFunction() {
  try {
    console.log('🔍 Testing join_game_room function...');
    
    // First, let's create the function directly
    const createFunctionSQL = `
-- Function to atomically join a game room
CREATE OR REPLACE FUNCTION join_game_room(
    p_room_id UUID,
    p_user_id UUID,
    p_entry_fee DECIMAL(10,2),
    p_room_name VARCHAR(100)
)
RETURNS VOID AS $$
DECLARE
    v_current_players INTEGER;
    v_max_players INTEGER;
    v_wallet_balance DECIMAL(12,2);
    v_participant_exists BOOLEAN;
BEGIN
    -- Lock the room row to prevent race conditions
    SELECT current_players, max_players
    INTO v_current_players, v_max_players
    FROM game_rooms
    WHERE id = p_room_id
    FOR UPDATE;
    
    -- Check if room exists
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Room not found';
    END IF;
    
    -- Check if room is full
    IF v_current_players >= v_max_players THEN
        RAISE EXCEPTION 'Room is full';
    END IF;
    
    -- Check if user is already in the room
    SELECT EXISTS(
        SELECT 1 FROM room_participants 
        WHERE room_id = p_room_id AND user_id = p_user_id
    ) INTO v_participant_exists;
    
    IF v_participant_exists THEN
        RAISE EXCEPTION 'Already in room';
    END IF;
    
    -- Check wallet balance
    SELECT balance INTO v_wallet_balance
    FROM wallets
    WHERE user_id = p_user_id
    FOR UPDATE;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Wallet not found';
    END IF;
    
    IF v_wallet_balance < p_entry_fee THEN
        RAISE EXCEPTION 'Insufficient balance';
    END IF;
    
    -- Deduct entry fee from wallet
    UPDATE wallets
    SET balance = balance - p_entry_fee,
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    -- Add user to room participants (trigger will update current_players)
    INSERT INTO room_participants (room_id, user_id, is_ready)
    VALUES (p_room_id, p_user_id, false);
    
    -- Create transaction record
    INSERT INTO transactions (user_id, wallet_id, type, amount, status, description)
    SELECT p_user_id, w.id, 'bet', p_entry_fee, 'completed', 
           'Entry fee for room: ' || p_room_name
    FROM wallets w
    WHERE w.user_id = p_user_id;
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
    `;
    
    // Execute the function creation
    const { error: createError } = await supabase.rpc('exec_sql', { sql: createFunctionSQL });
    
    if (createError) {
      console.error('❌ Failed to create function:', createError);
      return;
    }
    
    console.log('✅ Function created successfully');
    
    // Test if function exists
    const { data, error } = await supabase
      .rpc('exec_sql', { 
        sql: "SELECT proname FROM pg_proc WHERE proname = 'join_game_room';" 
      });
    
    if (error) {
      console.error('❌ Error checking function:', error);
    } else {
      console.log('✅ Function verification result:', data);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testJoinRoomFunction();

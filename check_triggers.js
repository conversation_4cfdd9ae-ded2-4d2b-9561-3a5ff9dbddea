const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTriggers() {
  console.log('🔍 Verificando triggers no banco de dados...\n');

  try {
    // Verificar se o trigger existe
    const { data: triggers, error: triggerError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT 
            t.trigger_name,
            t.event_manipulation,
            t.event_object_table,
            t.action_timing,
            t.action_statement,
            t.trigger_schema
          FROM information_schema.triggers t
          WHERE t.trigger_name LIKE '%wallet%' OR t.trigger_name LIKE '%balance%'
          ORDER BY t.trigger_name;
        `
      });

    if (triggerError) {
      console.error('❌ Erro ao verificar triggers:', triggerError);
      
      // Tentar método alternativo
      console.log('Tentando método alternativo...');
      const { data: altTriggers, error: altError } = await supabase
        .from('pg_trigger')
        .select('*')
        .ilike('tgname', '%wallet%');
      
      if (altError) {
        console.error('❌ Método alternativo também falhou:', altError);
      } else {
        console.log('Triggers encontrados (método alternativo):', altTriggers);
      }
    } else {
      console.log('📋 Triggers relacionados a wallet encontrados:');
      if (triggers && triggers.length > 0) {
        triggers.forEach(trigger => {
          console.log(`  - Nome: ${trigger.trigger_name}`);
          console.log(`    Tabela: ${trigger.event_object_table}`);
          console.log(`    Evento: ${trigger.event_manipulation}`);
          console.log(`    Timing: ${trigger.action_timing}`);
          console.log(`    Schema: ${trigger.trigger_schema}`);
          console.log('');
        });
      } else {
        console.log('⚠️ Nenhum trigger relacionado a wallet encontrado!');
      }
    }

    // Verificar se a função existe
    console.log('🔍 Verificando funções relacionadas a wallet...');
    const { data: functions, error: funcError } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT 
            p.proname as function_name,
            p.prosrc as function_body,
            n.nspname as schema_name
          FROM pg_proc p
          JOIN pg_namespace n ON p.pronamespace = n.oid
          WHERE p.proname LIKE '%wallet%' OR p.proname LIKE '%balance%'
          ORDER BY p.proname;
        `
      });

    if (funcError) {
      console.error('❌ Erro ao verificar funções:', funcError);
    } else {
      console.log('📋 Funções relacionadas a wallet encontradas:');
      if (functions && functions.length > 0) {
        functions.forEach(func => {
          console.log(`  - Nome: ${func.function_name}`);
          console.log(`    Schema: ${func.schema_name}`);
          console.log(`    Corpo: ${func.function_body.substring(0, 200)}...`);
          console.log('');
        });
      } else {
        console.log('⚠️ Nenhuma função relacionada a wallet encontrada!');
      }
    }

    // Verificar logs de erro do PostgreSQL (se disponível)
    console.log('🔍 Verificando se há erros recentes...');
    const { data: recentTransactions, error: txError } = await supabase
      .from('transactions')
      .select('id, type, status, created_at, updated_at')
      .eq('type', 'win')
      .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Última hora
      .order('created_at', { ascending: false });

    if (txError) {
      console.error('❌ Erro ao buscar transações recentes:', txError);
    } else {
      console.log(`📊 Transações de prêmio na última hora: ${recentTransactions.length}`);
      recentTransactions.forEach(tx => {
        console.log(`  - ID: ${tx.id}, Status: ${tx.status}, Criado: ${tx.created_at}`);
      });
    }

  } catch (error) {
    console.error('❌ Erro durante verificação:', error);
  }
}

// Executar verificação
checkTriggers()
  .then(() => {
    console.log('\n✅ Verificação concluída!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Search, UserPlus, Filter, Users, MessageCircle, X, Share2, Copy, ChevronRight, CheckCircle, Clock, Loader2 } from 'lucide-react';
import FriendCard from '../components/FriendCard';
import FriendRequestCard from '../components/FriendRequestCard';
import PrivateChatModal from '../components/PrivateChatModal';
import ChallengeModal from '../components/ChallengeModal';
import { realSocialService, Friend, FriendRequest } from '../services/realSocialService';
import ChallengeReceivedModal from '../components/ChallengeReceivedModal';
import ClubsTabContent from '../components/ClubsTabContent';

interface Friend {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'playing';
  avatar: string;
  game?: string;
  lastActive?: string;
}

interface FriendRequest {
  id: string;
  name: string;
  avatar: string;
  mutualFriends: number;
}

export default function SocialPage() {
  const [activeTab, setActiveTab] = useState<'friends' | 'clubs'>('friends');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'online' | 'playing'>('all');
  const [showAddFriend, setShowAddFriend] = useState(false);
  const [copied, setCopied] = useState(false);
  const [clubSearchQuery, setClubSearchQuery] = useState('');
  const [selectedGameFilter, setSelectedGameFilter] = useState<string | null>(null);
  const [selectedClubFilter, setSelectedClubFilter] = useState<'all' | 'my-clubs' | 'recommended'>('all');

  const affiliateLink = "fplaygames.com/ref/joaosilva42";

  const handleCopyLink = () => {
    navigator.clipboard.writeText(affiliateLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  const [showChallengeModal, setShowChallengeModal] = useState(false);
  const [showChallengeReceivedModal, setShowChallengeReceivedModal] = useState(false);
  const [selectedFriend, setSelectedFriend] = useState<Friend | null>(null);
  const [challengeData, setChallengeData] = useState<{
    challenger: Friend;
    game: {
      id: string;
      title: string;
      imageUrl: string;
    };
    betAmount: number;
  } | null>(null);
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([
    {
      id: 'request-1',
      name: 'Maria Santos',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      mutualFriends: 3
    },
    {
      id: 'request-2',
      name: 'Pedro Costa',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      mutualFriends: 5
    }
  ]);
  const [friends, setFriends] = useState<Friend[]>([
    {
      id: 'friend-1',
      name: 'Lucas Oliveira',
      status: 'playing',
      avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      game: 'Counter-Strike 2'
    },
    {
      id: 'friend-2',
      name: 'Ana Silva',
      status: 'online',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
    },
    {
      id: 'friend-3',
      name: 'Carlos Mendes',
      status: 'offline',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      lastActive: '2 horas atrás'
    },
    {
      id: 'friend-4',
      name: 'Beatriz Lima',
      status: 'offline',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      lastActive: '5 horas atrás'
    }
  ]);
  const [activeChatFriend, setActiveChatFriend] = useState<Friend | null>(null);

  // Estados para dados da API
  const [apiLoading, setApiLoading] = useState(true);
  const [apiError, setApiError] = useState<string | null>(null);

  // Carrega dados sociais da API
  useEffect(() => {
    const loadSocialData = async () => {
      try {
        setApiLoading(true);
        setApiError(null);

        console.log('🔄 Carregando dados sociais...');

        // Carrega amigos e solicitações em paralelo
        const [apiFriends, apiRequests] = await Promise.all([
          realSocialService.getFriends(),
          realSocialService.getFriendRequests()
        ]);

        // Converte dados da API para o formato da interface
        const convertedFriends: Friend[] = apiFriends.map(friend => ({
          id: friend.id,
          name: friend.display_name || friend.username,
          status: friend.status,
          avatar: friend.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          game: friend.game,
          lastActive: friend.last_active
        }));

        const convertedRequests: FriendRequest[] = apiRequests.map(request => ({
          id: request.id,
          name: request.from_user.display_name || request.from_user.username,
          avatar: request.from_user.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          mutualFriends: request.mutual_friends || 0
        }));

        // Se há dados da API, substitui os dados mock
        if (convertedFriends.length > 0) {
          setFriends(convertedFriends);
          console.log(`✅ ${convertedFriends.length} amigos carregados da API`);
        }

        if (convertedRequests.length > 0) {
          setFriendRequests(convertedRequests);
          console.log(`✅ ${convertedRequests.length} solicitações carregadas da API`);
        }

      } catch (err) {
        console.error('❌ Erro ao carregar dados sociais:', err);
        setApiError('Erro ao carregar dados sociais. Usando dados offline.');
        // Mantém os dados mock em caso de erro
      } finally {
        setApiLoading(false);
      }
    };

    loadSocialData();
  }, []);

  const handleAcceptRequest = async (requestId: string) => {
    try {
      console.log(`✅ Aceitando solicitação ${requestId}...`);

      const result = await realSocialService.acceptFriendRequest(requestId);

      if (result.success) {
        // Remove da lista de solicitações
        setFriendRequests(prev => prev.filter(r => r.id !== requestId));

        // Recarrega a lista de amigos
        const updatedFriends = await realSocialService.getFriends();
        const convertedFriends: Friend[] = updatedFriends.map(friend => ({
          id: friend.id,
          name: friend.display_name || friend.username,
          status: friend.status,
          avatar: friend.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          game: friend.game,
          lastActive: friend.last_active
        }));
        setFriends(convertedFriends);

        console.log('✅ Solicitação aceita com sucesso');
      } else {
        console.error('❌ Erro ao aceitar solicitação:', result.error);
        // Fallback para comportamento local
        const request = friendRequests.find(r => r.id === requestId);
        if (request) {
          const newFriend: Friend = {
            id: `friend-${Date.now()}`,
            name: request.name,
            avatar: request.avatar,
            status: 'online'
          };
          setFriends(prev => [...prev, newFriend]);
          setFriendRequests(prev => prev.filter(r => r.id !== requestId));
        }
      }
    } catch (error) {
      console.error('❌ Erro ao aceitar solicitação:', error);
      // Fallback para comportamento local
      const request = friendRequests.find(r => r.id === requestId);
      if (request) {
        const newFriend: Friend = {
          id: `friend-${Date.now()}`,
          name: request.name,
          avatar: request.avatar,
          status: 'online'
        };
        setFriends(prev => [...prev, newFriend]);
        setFriendRequests(prev => prev.filter(r => r.id !== requestId));
      }
    }
  };

  const handleRejectRequest = async (requestId: string) => {
    try {
      console.log(`❌ Rejeitando solicitação ${requestId}...`);

      const result = await realSocialService.rejectFriendRequest(requestId);

      if (result.success) {
        setFriendRequests(prev => prev.filter(r => r.id !== requestId));
        console.log('✅ Solicitação rejeitada com sucesso');
      } else {
        console.error('❌ Erro ao rejeitar solicitação:', result.error);
        // Fallback para comportamento local
        setFriendRequests(prev => prev.filter(r => r.id !== requestId));
      }
    } catch (error) {
      console.error('❌ Erro ao rejeitar solicitação:', error);
      // Fallback para comportamento local
      setFriendRequests(prev => prev.filter(r => r.id !== requestId));
    }
  };

  const handleOpenChat = (friendId: string) => {
    const friend = friends.find(f => f.id === friendId);
    if (friend) {
      setActiveChatFriend(friend);
    }
  };

  const handleOpenChallenge = (friendId: string) => {
    const friend = friends.find(f => f.id === friendId);
    if (friend) {
      setSelectedFriend(friend);
      setShowChallengeModal(true);
    }
  };

  const handleSendChallenge = (gameId: string, betAmount: number) => {
    // Simulate receiving a challenge response after 2 seconds
    setTimeout(() => {
      if (selectedFriend) {
        setChallengeData({
          challenger: selectedFriend,
          game: {
            id: gameId,
            title: gameId === '1' ? 'Counter-Strike 2' : gameId === '2' ? 'FIFA 24' : 'Call of Duty: Mobile',
            imageUrl: gameId === '1'
              ? 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
              : gameId === '2'
              ? 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80'
              : 'https://images.unsplash.com/photo-1509198397868-475647b2a1e5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1947&q=80'
          },
          betAmount
        });
        setShowChallengeReceivedModal(true);
      }
      setShowChallengeModal(false);
    }, 2000);
  };

  const filteredFriends = friends.filter(friend => {
    const matchesSearch = friend.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter =
      selectedFilter === 'all' ||
      (selectedFilter === 'online' && friend.status === 'online') ||
      (selectedFilter === 'playing' && friend.status === 'playing');
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Affiliate Link Card - Discreto */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
          <div className="flex items-center gap-2">
            <div className="bg-indigo-500/20 p-2 rounded-lg">
              <Share2 className="w-4 h-4 text-indigo-400" />
            </div>
            <div>
              <h3 className="text-white font-medium text-sm">Link de convite</h3>
              <p className="text-gray-400 text-xs">Ganhe até 15% quando amigos jogarem</p>
            </div>
          </div>

          <div className="flex items-center gap-2 self-end sm:self-auto">
            <div className="bg-gray-700/50 backdrop-blur-sm border border-white/5 rounded-lg px-2 py-1 hidden sm:flex items-center">
              <span className="text-gray-300 text-xs truncate max-w-[120px] sm:max-w-[180px]">{affiliateLink}</span>
            </div>

            <button
              onClick={handleCopyLink}
              className="bg-indigo-500/20 hover:bg-indigo-500/30 transition-colors p-1.5 rounded-lg flex items-center gap-1"
            >
              {copied ? (
                <>
                  <CheckCircle className="w-3.5 h-3.5 text-indigo-400" />
                  <span className="text-indigo-400 text-xs font-medium">Copiado</span>
                </>
              ) : (
                <>
                  <Copy className="w-3.5 h-3.5 text-indigo-400" />
                  <span className="text-indigo-400 text-xs font-medium">Copiar link</span>
                </>
              )}
            </button>

            <Link
              to="/affiliate-program"
              className="text-indigo-400 hover:text-indigo-300 transition-colors text-xs font-medium"
            >
              Detalhes
            </Link>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex gap-2 border-b border-gray-700 mb-4">
        <button
          onClick={() => setActiveTab('friends')}
          className={`px-6 py-3 font-semibold ${
            activeTab === 'friends'
              ? 'text-indigo-400 border-b-2 border-indigo-400'
              : 'text-gray-400 hover:text-white'
          }`}
        >
          Amigos
        </button>
        <button
          onClick={() => setActiveTab('clubs')}
          className={`px-6 py-3 font-semibold relative ${
            activeTab === 'clubs'
              ? 'text-indigo-400 border-b-2 border-indigo-400'
              : 'text-gray-400 hover:text-white'
          }`}
        >
          Clubes
          <div className="absolute -top-1 -right-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white text-[10px] px-1.5 py-0.5 rounded-full flex items-center gap-0.5 font-normal">
            <Clock className="w-2.5 h-2.5" />
            <span>EM BREVE</span>
          </div>
        </button>
      </div>

      {activeTab === 'friends' && (
        <div className="space-y-6">
          {/* API Error */}
          {apiError && (
            <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-xl p-4 text-center">
              <p className="text-yellow-400 text-sm">{apiError}</p>
            </div>
          )}

          {/* Loading State */}
          {apiLoading ? (
            <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
              <Loader2 className="w-8 h-8 text-indigo-400 mx-auto mb-4 animate-spin" />
              <h3 className="text-white font-bold text-lg mb-2">Carregando amigos...</h3>
              <p className="text-gray-400">Aguarde enquanto buscamos seus amigos e solicitações.</p>
            </div>
          ) : (
            <>
          {/* Quick Actions */}
          <div className="grid grid-cols-1 gap-4">
            <button
              onClick={() => setShowAddFriend(true)}
              className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center hover:scale-[1.02] hover:shadow-xl transition-all duration-300"
            >
              <UserPlus className="w-12 h-12 text-indigo-400 mx-auto mb-3" />
              <h3 className="text-white font-bold mb-1">Adicionar Amigos</h3>
              <p className="text-gray-400 text-sm">
                Encontre e adicione novos amigos para jogar
              </p>
            </button>
          </div>

      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Procurar amigos..."
          className="w-full bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white rounded-xl py-3 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-400"
        />
      </div>

      {/* Filter Tabs */}
      <div className="flex gap-2">
        <button
          onClick={() => setSelectedFilter('all')}
          className={`px-4 py-2 rounded-lg transition-all duration-200 ${
            selectedFilter === 'all'
              ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
              : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
          }`}
        >
          Todos
        </button>
        <button
          onClick={() => setSelectedFilter('online')}
          className={`px-4 py-2 rounded-lg transition-all duration-200 ${
            selectedFilter === 'online'
              ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
              : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
          }`}
        >
          Online
        </button>
        <button
          onClick={() => setSelectedFilter('playing')}
          className={`px-4 py-2 rounded-lg transition-all duration-200 ${
            selectedFilter === 'playing'
              ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
              : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
          }`}
        >
          Jogando
        </button>
      </div>

          {/* Friend Requests */}
          {friendRequests.length > 0 && (
            <section>
              <h2 className="text-white text-lg font-bold mb-3">Solicitações de Amizade</h2>
              <div className="space-y-3">
                {friendRequests.map((request) => (
                  <FriendRequestCard
                    key={request.id}
                    id={request.id}
                    name={request.name}
                    avatar={request.avatar}
                    mutualFriends={request.mutualFriends}
                    onAccept={handleAcceptRequest}
                    onReject={handleRejectRequest}
                  />
                ))}
              </div>
            </section>
          )}

          {/* Friends List */}
          <section>
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-white text-lg font-bold">Amigos</h2>
              <button
                onClick={() => setShowAddFriend(true)}
                className="text-indigo-400 hover:text-indigo-300"
              >
                <UserPlus className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-3">
              {filteredFriends.length > 0 ? (
                filteredFriends.map((friend) => (
                  <FriendCard
                    key={friend.id}
                    id={friend.id}
                    name={friend.name}
                    status={friend.status}
                    avatar={friend.avatar}
                    game={friend.game}
                    lastActive={friend.lastActive}
                    onChat={handleOpenChat}
                    onChallenge={handleOpenChallenge}
                  />
                ))
              ) : (
                <div className="bg-gray-800 rounded-xl p-8 text-center">
                  <Users className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                  <p className="text-gray-400">
                    {searchQuery
                      ? 'Nenhum amigo encontrado com este nome'
                      : 'Você ainda não tem amigos'}
                  </p>
                  <button
                    onClick={() => setShowAddFriend(true)}
                    className="mt-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-semibold px-6 py-2 rounded-lg hover:opacity-90 shadow-lg transition-all duration-300"
                  >
                    Adicionar Amigos
                  </button>
                </div>
              )}
            </div>
          </section>
            </>
          )}
        </div>
      )}

      {activeTab === 'clubs' && (
        <ClubsTabContent />
      )}

      {/* Add Friend Modal */}
      {showAddFriend && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl w-full max-w-md p-6 shadow-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-white text-lg font-bold">Adicionar Amigos</h3>
              <button
                onClick={() => setShowAddFriend(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Procurar por nome ou ID..."
                className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              />
            </div>

            <div className="space-y-4">
              {[
                { id: 'suggestion-1', name: 'Rafael Santos', avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' },
                { id: 'suggestion-2', name: 'Julia Costa', avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' }
              ].map(suggestion => (
                <div key={suggestion.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-700">
                  <div className="flex items-center gap-3">
                    <img
                      src={suggestion.avatar}
                      alt={suggestion.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <span className="text-white">{suggestion.name}</span>
                  </div>
                  <button className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-semibold px-4 py-1 rounded-lg hover:opacity-90 shadow-lg transition-all duration-200">
                    Adicionar
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Private Chat Modal */}
      {activeChatFriend && (
        <PrivateChatModal
          friend={activeChatFriend}
          onClose={() => setActiveChatFriend(null)}
        />
      )}

      {/* Challenge Modal */}
      {showChallengeModal && selectedFriend && (
        <ChallengeModal
          friend={selectedFriend}
          onClose={() => setShowChallengeModal(false)}
          onChallenge={handleSendChallenge}
        />
      )}

      {/* Challenge Received Modal */}
      {showChallengeReceivedModal && challengeData && (
        <ChallengeReceivedModal
          challenger={challengeData.challenger}
          game={challengeData.game}
          betAmount={challengeData.betAmount}
          onAccept={() => {
            setShowChallengeReceivedModal(false);
            // Navigate to the game room
            window.location.href = `/games/${challengeData.game.id}/rooms/challenge`;
          }}
          onDecline={() => setShowChallengeReceivedModal(false)}
        />
      )}
    </div>
  );
}
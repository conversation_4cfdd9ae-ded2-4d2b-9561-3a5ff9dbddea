import React from 'react';
import { TournamentSeason } from '../types/tournament';
import { CheckCircle } from 'lucide-react';

interface TournamentSeasonSelectorProps {
  seasons: TournamentSeason[];
  activeSeason: string;
  onSeasonChange: (seasonId: string) => void;
}

export default function TournamentSeasonSelector({
  seasons,
  activeSeason,
  onSeasonChange
}: TournamentSeasonSelectorProps) {
  // Ordenar temporadas por ordem
  const sortedSeasons = [...seasons].sort((a, b) => a.order - b.order);
  
  return (
    <div className="mb-6">
      <div className="flex items-center gap-4 mb-3">
        <h2 className="text-white text-lg font-bold">Temporadas</h2>
        <div className="text-gray-400 text-sm">
          {sortedSeasons.length} temporadas
        </div>
      </div>
      
      <div className="flex overflow-x-auto hide-scrollbar pb-2 gap-2">
        {sortedSeasons.map(season => (
          <button
            key={season.id}
            onClick={() => onSeasonChange(season.id)}
            className={`px-4 py-2 rounded-lg whitespace-nowrap flex items-center gap-2 transition-all ${
              activeSeason === season.id
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'
            }`}
          >
            {season.name}
            {season.status === 'completed' && (
              <CheckCircle className="w-4 h-4" />
            )}
            {season.status === 'in_progress' && (
              <span className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></span>
            )}
          </button>
        ))}
      </div>
      
      {/* Informações da temporada ativa */}
      {sortedSeasons.find(s => s.id === activeSeason) && (
        <div className="mt-3 text-sm text-gray-400">
          <div className="flex items-center gap-4">
            <div className={`px-2 py-0.5 rounded ${
              sortedSeasons.find(s => s.id === activeSeason)?.status === 'completed'
                ? 'bg-green-500/20 text-green-400'
                : sortedSeasons.find(s => s.id === activeSeason)?.status === 'in_progress'
                  ? 'bg-indigo-500/20 text-indigo-400'
                  : 'bg-gray-700 text-gray-400'
            }`}>
              {sortedSeasons.find(s => s.id === activeSeason)?.status === 'completed'
                ? 'Concluída'
                : sortedSeasons.find(s => s.id === activeSeason)?.status === 'in_progress'
                  ? 'Em andamento'
                  : 'Aguardando'}
            </div>
            
            <div>
              {sortedSeasons.find(s => s.id === activeSeason)?.startDate && (
                <>
                  {new Date(sortedSeasons.find(s => s.id === activeSeason)!.startDate!).toLocaleDateString('pt-BR')}
                  {' - '}
                  {new Date(sortedSeasons.find(s => s.id === activeSeason)!.endDate!).toLocaleDateString('pt-BR')}
                </>
              )}
            </div>
            
            <div>
              {sortedSeasons.find(s => s.id === activeSeason)?.brackets.length} subchave(s)
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

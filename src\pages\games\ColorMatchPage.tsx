import React from 'react';
import { Grid } from 'lucide-react';
import GamePage from './GamePage';

export default function ColorMatchPage() {
  const gameInfo = {
    id: 'colormatch',
    title: 'COLOR MATCH',
    description: 'Encontre os pares de cores antes que o tempo acabe! Quanto mais rápido você encontrar todos os pares, maior será sua pontuação.',
    imageUrl: 'https://images.unsplash.com/photo-1513542789411-b6a5d4f31634?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    players: 432,
    icon: Grid,
    prizePool: 1950
  };

  return <GamePage gameInfo={gameInfo} />;
}

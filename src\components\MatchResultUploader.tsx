import React, { useState } from 'react';
import { Upload, X, Check, AlertTriangle, Loader2 } from 'lucide-react';
import { analyzeGameResult } from '../services/openaiService';

interface MatchResultUploaderProps {
  onResultProcessed: (winner: string, player1Score: number, player2Score: number, confidence?: number, awardPrize?: boolean) => void;
  player1Name: string;
  player2Name: string;
  selectedFile?: File | null;
  setSelectedFile?: (file: File | null) => void;
}

export default function MatchResultUploader({
  onResultProcessed,
  player1Name,
  player2Name,
  selectedFile: externalSelectedFile,
  setSelectedFile: externalSetSelectedFile
}: MatchResultUploaderProps) {
  const [internalSelectedFile, internalSetSelectedFile] = useState<File | null>(null);

  // Usar o arquivo selecionado externo se fornecido, caso contrário usar o interno
  const selectedFile = externalSelectedFile !== undefined ? externalSelectedFile : internalSelectedFile;

  // Função para atualizar o arquivo selecionado
  const setSelectedFile = (file: File | null) => {
    if (externalSetSelectedFile) {
      externalSetSelectedFile(file);
    } else {
      internalSetSelectedFile(file);
    }
  };
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<{
    winner: string;
    player1Score: number;
    player2Score: number;
    confidence?: number;
    awardPrize?: boolean;
    originalWinner?: string;
  } | null>(null);

  // Efeito para criar a URL de preview quando o arquivo selecionado mudar
  React.useEffect(() => {
    if (selectedFile) {
      console.log('Arquivo selecionado:', selectedFile.name);
      console.log('Preparando preview...');

      // Criar URL para preview
      const fileUrl = URL.createObjectURL(selectedFile);
      setPreviewUrl(fileUrl);
      console.log('Preview criado com sucesso.');

      // Limpar a URL quando o componente for desmontado
      return () => {
        if (fileUrl) URL.revokeObjectURL(fileUrl);
      };
    } else {
      setPreviewUrl(null);
    }
  }, [selectedFile]);

  // Função para lidar com a seleção de arquivo
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('Arquivo selecionado!');

    const file = event.target.files?.[0];
    if (!file) {
      console.log('Nenhum arquivo selecionado.');
      return;
    }

    console.log('Arquivo:', file.name);
    console.log('Tipo:', file.type);
    console.log('Tamanho:', (file.size / 1024).toFixed(2), 'KB');

    // Verificar se o arquivo é uma imagem
    if (!file.type.startsWith('image/')) {
      console.log('Arquivo não é uma imagem válida.');
      setError('Por favor, selecione um arquivo de imagem válido (PNG, JPG, JPEG).');
      return;
    }

    // Limitar o tamanho do arquivo (5MB)
    if (file.size > 5 * 1024 * 1024) {
      console.log('Arquivo muito grande.');
      setError('O arquivo é muito grande. Por favor, selecione um arquivo menor que 5MB.');
      return;
    }

    console.log('Arquivo válido, atualizando estado...');
    setSelectedFile(file);
    setError(null);
    setResult(null);
  };

  // Função para processar o resultado
  const processResult = async () => {
    console.log('Botão de processamento clicado!');

    if (!selectedFile || !previewUrl) {
      console.log('Nenhum arquivo selecionado para processamento.');
      setError('Por favor, selecione um arquivo primeiro.');
      return;
    }

    console.log('Iniciando processamento do arquivo:', selectedFile.name);
    console.log('Player 1:', player1Name);
    console.log('Player 2:', player2Name);

    setIsProcessing(true);
    setError(null);

    try {
      console.log('Chamando serviço de análise de imagem...');

      // Chamar o serviço da OpenAI para analisar a imagem
      console.log('Enviando para analyzeGameResult...');

      let analysisResult;
      try {
        analysisResult = await analyzeGameResult(selectedFile, player1Name, player2Name);
        console.log('Resposta recebida do serviço de análise!');
      } catch (error) {
        console.error("Erro ao chamar analyzeGameResult:", error);
        throw error;
      }

      console.log('Resultado da análise recebido:', analysisResult);

      // Definir o resultado
      const processedResult = {
        winner: analysisResult.winner,
        player1Score: analysisResult.player1Score,
        player2Score: analysisResult.player2Score,
        confidence: analysisResult.confidence,
        awardPrize: analysisResult.awardPrize,
        originalWinner: analysisResult.originalWinner
      };

      console.log('Resultado processado:', processedResult);

      setResult(processedResult);
      onResultProcessed(
        processedResult.winner,
        processedResult.player1Score,
        processedResult.player2Score,
        processedResult.confidence,
        processedResult.awardPrize
      );
    } catch (err: any) {
      console.error('Erro ao processar imagem:', err);

      // Exibir mensagem de erro mais detalhada
      let errorMessage = 'Ocorreu um erro ao processar a imagem. Por favor, tente novamente.';

      if (err.message) {
        errorMessage += ' Detalhes: ' + err.message;
      }

      setError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  // Função para limpar a seleção
  const clearSelection = () => {
    setSelectedFile(null);
    setError(null);
    setResult(null);

    // Limpar o input de arquivo
    const fileInput = document.getElementById('result-file-input') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  return (
    <div className="bg-gray-800/70 backdrop-blur-sm border border-gray-700 rounded-xl p-3 md:p-4 space-y-3 md:space-y-4">
      <h3 className="text-white font-semibold text-lg flex items-center gap-2">
        <Upload className="w-5 h-5 text-indigo-400" />
        Enviar Screenshot do Resultado
      </h3>

      {/* Área de upload */}
      <div
        className={`border-2 border-dashed rounded-lg p-4 md:p-6 text-center ${
          previewUrl
            ? 'border-indigo-500/50 bg-indigo-500/10'
            : 'border-gray-600 hover:border-indigo-500/50 hover:bg-indigo-500/5'
        } transition-colors cursor-pointer`}
        onClick={() => document.getElementById('result-file-input')?.click()}
      >
        {previewUrl ? (
          <div className="space-y-3">
            <div className="relative mx-auto max-w-md">
              <img
                src={previewUrl}
                alt="Preview"
                className="rounded-lg max-h-48 md:max-h-64 mx-auto object-contain"
              />
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  clearSelection();
                }}
                className="absolute top-2 right-2 bg-gray-800/80 text-white p-1 rounded-full hover:bg-gray-700"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {result ? (
              <div className="bg-green-500/20 text-green-400 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Check className="w-5 h-5" />
                  <span className="font-medium">Resultado processado com sucesso!</span>
                </div>
                <p>Vencedor: <span className="font-bold">{result.winner}</span></p>
                <p>Placar: <span className="font-bold">{result.player1Score} - {result.player2Score}</span></p>

                {/* Mostrar se o prêmio será concedido */}
                <div className={`mt-2 p-2 rounded ${result.awardPrize ? 'bg-yellow-500/20 text-yellow-400' : 'bg-red-500/20 text-red-400'}`}>
                  {result.awardPrize ? (
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 flex-shrink-0" />
                      <span>Vencedor identificado como {result.winner}! Você venceu e receberá o prêmio.</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4 flex-shrink-0" />
                      <span>Vencedor identificado como {result.winner}. Você perdeu e não receberá o prêmio.</span>
                    </div>
                  )}
                </div>

                {result.confidence && (
                  <p className="text-xs mt-1">Confiança da análise: {Math.round(result.confidence * 100)}%</p>
                )}

                {result.originalWinner && result.originalWinner !== result.winner && (
                  <p className="text-xs mt-1 text-gray-400">Nome original detectado: {result.originalWinner}</p>
                )}
              </div>
            ) : (
              <div className="text-gray-300 mt-2">
                Imagem selecionada. Clique no botão "Processar Resultado" abaixo para analisar.
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-2 py-2">
            <Upload className="w-10 h-10 md:w-12 md:h-12 text-gray-500 mx-auto" />
            <p className="text-gray-300">Clique para fazer upload do screenshot do resultado</p>
            <p className="text-gray-500 text-sm">Formatos suportados: PNG, JPG, JPEG (máx. 5MB)</p>
          </div>
        )}

        <input
          id="result-file-input"
          type="file"
          accept="image/png, image/jpeg, image/jpg"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* Botão de processamento separado */}
      {previewUrl && !result && (
        <div className="flex justify-center">
          <button
            onClick={() => {
              console.log("BOTÃO CLICADO - INICIANDO PROCESSAMENTO");
              processResult();
            }}
            disabled={isProcessing}
            className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg flex items-center gap-2 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                Processando...
              </>
            ) : (
              <>
                <Check className="w-5 h-5" />
                Processar Resultado
              </>
            )}
          </button>
        </div>
      )}

      {/* Mensagem de erro */}
      {error && (
        <div className="bg-red-500/20 text-red-400 p-3 rounded-lg flex items-start gap-2">
          <AlertTriangle className="w-5 h-5 flex-shrink-0 mt-0.5" />
          <p className="text-sm md:text-base">{error}</p>
        </div>
      )}

      {/* Instruções - Colapsável em telas menores */}
      <details className="bg-gray-700/30 p-3 rounded-lg group">
        <summary className="text-white font-medium cursor-pointer list-none flex items-center justify-between">
          Como enviar o resultado
          <span className="text-gray-400 text-xs group-open:rotate-180 transition-transform">▼</span>
        </summary>
        <ol className="text-gray-300 text-sm space-y-1 list-decimal list-inside mt-2 pl-1">
          <li>Tire um screenshot da tela final do jogo mostrando o placar</li>
          <li>Faça upload da imagem clicando na área acima</li>
          <li>Clique em "Processar Resultado" para análise automática</li>
          <li>Confirme o resultado processado</li>
        </ol>
      </details>

      {/* Botão de teste para desenvolvimento - Colapsável em telas menores */}
      <details className="mt-3 p-3 bg-yellow-500/20 rounded-lg group">
        <summary className="text-yellow-400 font-medium cursor-pointer list-none flex items-center justify-between">
          Modo de Teste
          <span className="text-gray-400 text-xs group-open:rotate-180 transition-transform">▼</span>
        </summary>
        <div className="mt-2">
          <p className="text-gray-300 mb-3 text-sm">Use estes botões para testar a interface sem precisar fazer upload de imagem real.</p>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => {
                console.log('Usando resultado simulado para teste (jogador atual venceu)...');
                const mockResult = {
                  winner: player1Name,
                  player1Score: 3,
                  player2Score: 1,
                  confidence: 0.95,
                  awardPrize: true,
                  originalWinner: player1Name
                };
                setResult(mockResult);
                onResultProcessed(
                  mockResult.winner,
                  mockResult.player1Score,
                  mockResult.player2Score,
                  mockResult.confidence,
                  mockResult.awardPrize,
                  mockResult.originalWinner
                );
              }}
              className="bg-yellow-500/30 hover:bg-yellow-500/50 text-yellow-400 px-3 py-2 rounded-lg flex items-center gap-2 text-sm"
            >
              <span>{player1Name} Venceu (Prêmio: SIM)</span>
            </button>

            <button
              onClick={() => {
                console.log('Usando resultado simulado sem prêmio para teste (oponente venceu)...');
                const mockResult = {
                  winner: player2Name,
                  player1Score: 1,
                  player2Score: 3,
                  confidence: 0.85,
                  awardPrize: false,
                  originalWinner: player2Name
                };
                setResult(mockResult);
                onResultProcessed(
                  mockResult.winner,
                  mockResult.player1Score,
                  mockResult.player2Score,
                  mockResult.confidence,
                  mockResult.awardPrize,
                  mockResult.originalWinner
                );
              }}
              className="bg-red-500/30 hover:bg-red-500/50 text-red-400 px-3 py-2 rounded-lg flex items-center gap-2 text-sm"
            >
              <span>{player2Name} Venceu (Prêmio: NÃO)</span>
            </button>
          </div>
        </div>
      </details>
    </div>
  );
}

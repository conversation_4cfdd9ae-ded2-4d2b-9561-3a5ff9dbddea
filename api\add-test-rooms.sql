-- Script para adicionar salas de teste
-- Execute este script no Supabase SQL Editor

-- <PERSON><PERSON>, vamos verificar se temos jogos na tabela
-- Se não tiver, vamos criar alguns jogos de teste

INSERT INTO games (id, name, slug, description, type, is_active, min_players, max_players, default_entry_fee)
VALUES 
  ('550e8400-e29b-41d4-a716-446655440001', 'Counter-Strike 2', 'cs2', 'FPS tático competitivo', 'external', true, 2, 10, 10.00),
  ('550e8400-e29b-41d4-a716-446655440002', 'FIFA 24', 'fifa24', 'Simulador de futebol', 'external', true, 2, 2, 15.00),
  ('550e8400-e29b-41d4-a716-446655440003', 'Call of Duty', 'cod', 'FPS de ação', 'external', true, 2, 16, 20.00)
ON CONFLICT (id) DO NOTHING;

-- A<PERSON>a vamos criar um usuário de teste para ser o host das salas
INSERT INTO users (id, email, username, display_name, status)
VALUES 
  ('550e8400-e29b-41d4-a716-446655440010', '<EMAIL>', 'testhost', 'Host de Teste', 'active')
ON CONFLICT (email) DO NOTHING;

-- Criar algumas salas de teste
INSERT INTO game_rooms (id, game_id, host_user_id, name, type, status, entry_fee, max_players, current_players, is_public, game_settings)
VALUES 
  ('550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440010', 'CS2 Competitivo BR', '1v1', 'waiting', 25.00, 2, 1, true, '{"map": "Dust II", "mode": "Competitivo"}'),
  ('550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440010', 'Treino AWP', '1v1', 'waiting', 15.00, 2, 1, true, '{"map": "Mirage", "mode": "AWP Only"}'),
  ('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440010', 'FIFA 1v1 Amistoso', '1v1', 'waiting', 20.00, 2, 1, true, '{"mode": "Amistoso", "time": "Real Madrid vs Barcelona"}'),
  ('550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440010', 'COD Battle Royale', 'tournament', 'waiting', 30.00, 16, 5, true, '{"mode": "Battle Royale", "map": "Verdansk"}')
ON CONFLICT (id) DO NOTHING;

-- Adicionar o host como participante das salas
INSERT INTO room_participants (room_id, user_id, is_ready, joined_at)
VALUES 
  ('550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-446655440010', true, NOW()),
  ('550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440010', true, NOW()),
  ('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440010', true, NOW()),
  ('550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440010', true, NOW())
ON CONFLICT (room_id, user_id) DO NOTHING;

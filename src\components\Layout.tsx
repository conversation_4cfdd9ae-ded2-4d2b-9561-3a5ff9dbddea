import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { Search, Bell, Wallet, User, LogOut } from 'lucide-react';
import NavigationBar from './NavigationBar';
import SearchOverlay from './SearchOverlay';
import NotificationsDropdown from './NotificationsDropdown';
import UpcomingMatchReminderModal from './UpcomingMatchReminderModal';
import UserNameModal from './UserNameModal';
import WelcomeScreen from './WelcomeScreen';
import { Link } from 'react-router-dom';
import { getNextMatch, Match } from '../services/matchService';
import { useUser } from '../contexts/UserContext';

export default function Layout() {
  const { user, isLoggedIn, isAuthenticated, logout } = useUser();
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [showMatchReminder, setShowMatchReminder] = useState(false);
  const [nextMatch, setNextMatch] = useState<Match | null>(null);
  const [showUserNameModal, setShowUserNameModal] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Verificar se há uma próxima partida ao carregar o componente
  useEffect(() => {
    // Pequeno atraso para não mostrar o modal imediatamente ao entrar no app
    const timer = setTimeout(() => {
      const match = getNextMatch();
      if (match) {
        setNextMatch(match);
        setShowMatchReminder(true);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Não mostrar modal automaticamente - usuário deve escolher entre login real ou convidado
  // useEffect(() => {
  //   if (!isAuthenticated && !isLoggedIn) {
  //     const timer = setTimeout(() => {
  //       setShowUserNameModal(true);
  //     }, 1500);

  //     return () => clearTimeout(timer);
  //   }
  // }, [isAuthenticated, isLoggedIn]);

  // Função para fechar o modal de lembrete
  const handleCloseMatchReminder = () => {
    setShowMatchReminder(false);
  };

  // Função para alternar o menu do usuário
  const toggleUserMenu = () => {
    setShowUserMenu(prev => !prev);
  };

  const handleGuestLogin = () => {
    setShowUserNameModal(true);
  };

  // Se o usuário não estiver logado nem autenticado, mostrar tela de boas-vindas
  if (!isLoggedIn && !isAuthenticated) {
    return <WelcomeScreen onGuestLogin={handleGuestLogin} />;
  }

  return (
    <div className="min-h-screen bg-gray-900 pb-20">
      {/* Header */}
      <header className="sticky top-0 bg-gray-900/80 backdrop-blur-sm border-b border-white/10 px-3 py-2.5 sm:px-4 sm:py-3 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 w-9 h-9 rounded-lg flex items-center justify-center shadow-lg">
              <span className="text-white font-bold">FP</span>
            </div>
            <button
              onClick={() => setIsSearchOpen(true)}
              className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-full w-9 h-9 flex items-center justify-center hover:bg-gray-700/50 transition-colors"
            >
              <Search className="w-5 h-5 text-gray-400" />
            </button>
          </div>

          <div className="flex items-center gap-2 sm:gap-3">
            {/* Wallet - Hidden on smallest screens, visible on sm and up */}
            <div className="hidden sm:flex items-center">
              <Link to="/wallet" className="flex items-center gap-1.5 bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-lg px-3 py-2 hover:bg-gray-700/50 transition-colors">
                <Wallet className="w-4 h-4 text-indigo-400" />
                <span className="text-white font-medium text-sm">R$ 1.248,00</span>
              </Link>
              <div className="hidden md:flex items-center gap-1.5 bg-purple-500/20 rounded-lg px-3 py-2 ml-2">
                <Wallet className="w-4 h-4 text-purple-500" />
                <span className="text-purple-500 font-medium text-sm">+ R$ 50,00</span>
              </div>
            </div>

            {/* Mobile Wallet - Visible only on smallest screens */}
            <Link to="/wallet" className="sm:hidden flex items-center justify-center bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-full w-9 h-9 hover:bg-gray-700/50 transition-colors">
              <Wallet className="w-5 h-5 text-indigo-400" />
            </Link>

            <div className="relative mx-1">
              <button
                onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
                className="relative bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-full w-9 h-9 flex items-center justify-center hover:bg-gray-700/50 transition-colors"
              >
                <Bell className="w-5 h-5 text-gray-400 hover:text-white transition-colors" />
                <span className="absolute -top-1 -right-1 bg-gradient-to-r from-indigo-500 to-purple-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center shadow-lg">
                  2
                </span>
              </button>
              <NotificationsDropdown
                isOpen={isNotificationsOpen}
                onClose={() => setIsNotificationsOpen(false)}
              />
            </div>

            <div className="relative">
              <button
                onClick={toggleUserMenu}
                className="flex items-center justify-center bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-full w-9 h-9 sm:w-10 sm:h-10 hover:bg-gray-700/50 transition-colors"
              >
                {isLoggedIn ? (
                  <div className="flex items-center justify-center w-full h-full">
                    <span className="text-indigo-400 font-medium text-sm">
                      {user.name?.substring(0, 2).toUpperCase()}
                    </span>
                  </div>
                ) : (
                  <User className="w-5 h-5 text-indigo-400" />
                )}
              </button>

              {/* Menu do usuário */}
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-white/10 z-50">
                  {isLoggedIn || isAuthenticated ? (
                    <div>
                      <div className="p-3 border-b border-gray-700">
                        <p className="text-white font-medium">{user.name}</p>
                        {isAuthenticated && user.email && (
                          <p className="text-gray-400 text-sm">{user.email}</p>
                        )}
                      </div>
                      <Link
                        to="/profile"
                        className="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white"
                        onClick={() => setShowUserMenu(false)}
                      >
                        Perfil
                      </Link>
                      <Link
                        to="/settings"
                        className="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white"
                        onClick={() => setShowUserMenu(false)}
                      >
                        Configurações
                      </Link>
                      <button
                        onClick={() => {
                          logout();
                          setShowUserMenu(false);
                          // Só mostra modal de nome se não estiver autenticado
                          if (!isAuthenticated) {
                            setShowUserNameModal(true);
                          }
                        }}
                        className="flex items-center w-full px-4 py-2 text-rose-400 hover:bg-gray-700 hover:text-rose-300"
                      >
                        <LogOut className="w-4 h-4 mr-2" />
                        Sair
                      </button>
                    </div>
                  ) : (
                    <div>
                      <Link
                        to="/login"
                        className="block px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white"
                        onClick={() => setShowUserMenu(false)}
                      >
                        Entrar
                      </Link>
                      <Link
                        to="/register"
                        className="block px-4 py-2 text-indigo-400 hover:bg-gray-700 hover:text-indigo-300"
                        onClick={() => setShowUserMenu(false)}
                      >
                        Criar Conta
                      </Link>
                      <hr className="border-gray-700 my-1" />
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          setShowUserNameModal(true);
                        }}
                        className="block w-full text-left px-4 py-2 text-gray-400 hover:bg-gray-700 hover:text-gray-300 text-sm"
                      >
                        Entrar como Convidado
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <SearchOverlay isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />

      <main className="p-4 space-y-6">
        <Outlet />
      </main>

      <NavigationBar />

      {/* Modal de Lembrete de Próxima Partida */}
      {showMatchReminder && nextMatch && (
        <UpcomingMatchReminderModal
          onClose={handleCloseMatchReminder}
          match={nextMatch}
        />
      )}

      {/* Modal para definir o nome do usuário */}
      <UserNameModal
        isOpen={showUserNameModal}
        onClose={() => setShowUserNameModal(false)}
      />
    </div>
  );
}
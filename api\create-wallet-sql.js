const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createWalletWithSQL() {
  try {
    console.log('🔍 Buscando usuário matt...');
    
    // Buscar o usuário matt
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email')
      .eq('username', 'matt')
      .single();

    if (userError || !user) {
      console.error('❌ Usuário não encontrado:', userError);
      return;
    }

    console.log(`✅ Usuário encontrado: ${user.username} (${user.id})`);

    // Criar função SQL temporária para criar carteira
    console.log('💰 Criando carteira via SQL...');
    
    const createWalletSQL = `
      DO $$
      BEGIN
        -- Verificar se carteira já existe
        IF NOT EXISTS (SELECT 1 FROM wallets WHERE user_id = '${user.id}') THEN
          -- Criar carteira
          INSERT INTO wallets (user_id, balance, frozen_balance, total_deposited, total_withdrawn)
          VALUES ('${user.id}', 0.00, 0.00, 0.00, 0.00);
          
          RAISE NOTICE 'Carteira criada para usuário ${user.id}';
        ELSE
          RAISE NOTICE 'Carteira já existe para usuário ${user.id}';
        END IF;
      END $$;
    `;

    const { data, error } = await supabase.rpc('exec_sql', { 
      sql: createWalletSQL 
    });

    if (error) {
      console.error('❌ Erro ao executar SQL:', error);
      
      // Tentar método alternativo - criar via RPC
      console.log('🔄 Tentando método alternativo...');
      
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('create_user_wallet', { 
          p_user_id: user.id 
        });

      if (rpcError) {
        console.error('❌ Erro no RPC:', rpcError);
        return;
      }
    }

    // Verificar se a carteira foi criada
    const { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (walletError) {
      console.error('❌ Erro ao verificar carteira:', walletError);
      return;
    }

    console.log('✅ Carteira criada/encontrada com sucesso!');
    console.log(`   ID: ${wallet.id}`);
    console.log(`   Saldo: R$ ${wallet.balance.toFixed(2)}`);
    console.log(`   Usuário: ${user.username}`);

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

createWalletWithSQL();

import React from 'react';
import { Trophy, Gamepad2, UserPlus } from 'lucide-react';

interface NotificationsDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function NotificationsDropdown({ isOpen, onClose }: NotificationsDropdownProps) {
  if (!isOpen) return null;

  const notifications = [
    {
      id: 1,
      type: 'achievement',
      title: 'Nova Conquista!',
      message: '<PERSON><PERSON><PERSON> "Mestre Estrategista"',
      icon: Trophy,
      time: '5min',
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-400/20'
    },
    {
      id: 2,
      type: 'game',
      title: 'Partida Encontrada',
      message: 'Sua partida de Foguete Espacial está pronta',
      icon: Gamepad2,
      time: '2min',
      color: 'text-purple-400',
      bgColor: 'bg-purple-400/20'
    },
    {
      id: 3,
      type: 'social',
      title: 'Nova Solicitação',
      message: '<PERSON> quer ser sua amiga',
      icon: UserPlus,
      time: '1h',
      color: 'text-blue-400',
      bgColor: 'bg-blue-400/20'
    }
  ];

  return (
    <>
      <div className="fixed inset-0 z-40" onClick={onClose} />
      <div className="absolute top-16 right-4 w-80 bg-gray-800 rounded-xl shadow-lg z-50 overflow-hidden">
        <div className="p-4 border-b border-gray-700">
          <h3 className="text-white font-bold">Notificações</h3>
        </div>
        <div className="max-h-[400px] overflow-y-auto">
          {notifications.map((notification) => {
            const Icon = notification.icon;
            return (
              <button
                key={notification.id}
                className="w-full p-4 flex items-start gap-3 hover:bg-gray-700 border-b border-gray-700 last:border-0"
              >
                <div className={`p-2 rounded-lg ${notification.bgColor}`}>
                  <Icon className={`w-5 h-5 ${notification.color}`} />
                </div>
                <div className="flex-1 text-left">
                  <p className="text-white font-semibold">{notification.title}</p>
                  <p className="text-gray-400 text-sm">{notification.message}</p>
                  <p className="text-gray-500 text-xs mt-1">{notification.time}</p>
                </div>
              </button>
            );
          })}
        </div>
        <div className="p-3 border-t border-gray-700">
          <button className="text-yellow-400 text-sm w-full text-center">
            Ver Todas as Notificações
          </button>
        </div>
      </div>
    </>
  );
}
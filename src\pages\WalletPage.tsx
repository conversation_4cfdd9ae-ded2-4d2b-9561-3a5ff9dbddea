import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Wallet, CreditCard, CircleDollarSign, ArrowDownLeft, ArrowUpRight, Gamepad2, ArrowLeft, Loader2 } from 'lucide-react';
import TransactionCard from '../components/TransactionCard';
import DepositModal from '../components/DepositModal';
import WithdrawModal from '../components/WithdrawModal';
import { realWalletService, Transaction, WalletBalance } from '../services/realWalletService';

export default function WalletPage() {
  const navigate = useNavigate();
  const [showDeposit, setShowDeposit] = useState(false);
  const [showWithdraw, setShowWithdraw] = useState(false);

  // Estados para dados da API
  const [balance, setBalance] = useState<number>(0);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carrega dados da carteira
  useEffect(() => {
    const loadWalletData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando dados da carteira...');

        // Carrega saldo e transações em paralelo
        const [balanceData, transactionsData] = await Promise.all([
          realWalletService.getBalance(),
          realWalletService.getTransactions(1, 20)
        ]);

        if (balanceData) {
          setBalance(balanceData.balance);
          console.log(`✅ Saldo carregado: R$ ${balanceData.balance.toFixed(2)}`);
        }

        setTransactions(transactionsData);
        console.log(`✅ ${transactionsData.length} transações carregadas`);

      } catch (err) {
        console.error('❌ Erro ao carregar dados da carteira:', err);
        setError('Erro ao carregar dados da carteira. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadWalletData();
  }, []);

  const handleDeposit = async (amount: number, method: 'pix' | 'credit_card') => {
    try {
      console.log(`💳 Processando depósito de R$ ${amount} via ${method}...`);

      const result = await realWalletService.requestDeposit({
        amount,
        method,
        metadata: { source: 'wallet_page' }
      });

      if (result.success) {
        console.log('✅ Depósito solicitado com sucesso');
        // Recarrega os dados da carteira
        const [balanceData, transactionsData] = await Promise.all([
          realWalletService.getBalance(),
          realWalletService.getTransactions(1, 20)
        ]);

        if (balanceData) setBalance(balanceData.balance);
        setTransactions(transactionsData);

        // Se há URL de pagamento, abre em nova aba
        if (result.payment_url) {
          window.open(result.payment_url, '_blank');
        }
      } else {
        console.error('❌ Erro no depósito:', result.error);
        setError(result.error || 'Erro ao processar depósito');
      }
    } catch (error) {
      console.error('❌ Erro ao processar depósito:', error);
      setError('Erro de conexão. Tente novamente.');
    }
  };

  const handleWithdraw = async (amount: number, pixKey: string) => {
    try {
      console.log(`💸 Processando saque de R$ ${amount} para ${pixKey}...`);

      const result = await realWalletService.requestWithdrawal({
        amount,
        method: 'pix',
        pix_key: pixKey
      });

      if (result.success) {
        console.log('✅ Saque solicitado com sucesso');
        // Recarrega os dados da carteira
        const [balanceData, transactionsData] = await Promise.all([
          realWalletService.getBalance(),
          realWalletService.getTransactions(1, 20)
        ]);

        if (balanceData) setBalance(balanceData.balance);
        setTransactions(transactionsData);
      } else {
        console.error('❌ Erro no saque:', result.error);
        setError(result.error || 'Erro ao processar saque');
      }
    } catch (error) {
      console.error('❌ Erro ao processar saque:', error);
      setError('Erro de conexão. Tente novamente.');
    }
  };

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="flex items-center gap-2 sm:gap-4">
        <button
          onClick={() => navigate(-1)}
          className="flex items-center gap-1 sm:gap-2 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5" />
          <span className="text-sm sm:text-base">Voltar</span>
        </button>
        <h1 className="text-xl sm:text-2xl font-bold text-white">Carteira</h1>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-center">
          <p className="text-red-400 mb-2">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Loader2 className="w-8 h-8 text-indigo-400 mx-auto mb-4 animate-spin" />
          <h3 className="text-white font-bold text-lg mb-2">Carregando carteira...</h3>
          <p className="text-gray-400">Aguarde enquanto buscamos seus dados financeiros.</p>
        </div>
      ) : (
        <>

      {/* Balance Card */}
      <div className="relative overflow-hidden bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-xl p-4 sm:p-6">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mt-10 -mr-10 blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -mb-8 -ml-8 blur-xl"></div>

        <div className="relative z-10">
          <div className="flex items-center gap-3 mb-2">
            <Wallet className="w-6 h-6 text-white" />
            <h2 className="text-white font-bold">Saldo Disponível</h2>
          </div>

          <p className="text-2xl sm:text-3xl font-bold text-white mb-4">R$ {balance.toFixed(2)}</p>

          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <button
              onClick={() => setShowDeposit(true)}
              className="flex-1 bg-white/20 hover:bg-white/30 transition-colors text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2"
            >
              <ArrowDownLeft className="w-5 h-5" />
              Depositar
            </button>
            <button
              onClick={() => setShowWithdraw(true)}
              className="flex-1 bg-white/20 hover:bg-white/30 transition-colors text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2"
            >
              <ArrowUpRight className="w-5 h-5" />
              Sacar
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 xs:grid-cols-3 gap-3 sm:gap-4">
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-1">
            <ArrowDownLeft className="w-4 h-4 text-green-400" />
            <h3 className="text-white font-medium">Depósitos</h3>
          </div>
          <p className="text-white font-bold text-xl">R$ 200.00</p>
        </div>
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-1">
            <ArrowUpRight className="w-4 h-4 text-red-400" />
            <h3 className="text-white font-medium">Saques</h3>
          </div>
          <p className="text-white font-bold text-xl">R$ 100.00</p>
        </div>
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-1">
            <Gamepad2 className="w-4 h-4 text-indigo-400" />
            <h3 className="text-white font-medium">Prêmios</h3>
          </div>
          <p className="text-white font-bold text-xl">R$ 120.00</p>
        </div>
      </div>

      {/* Payment Methods */}
      <section>
        <h2 className="text-white text-base sm:text-lg font-bold mb-3">Métodos de Pagamento</h2>
        <div className="grid grid-cols-1 xs:grid-cols-2 gap-3">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 flex items-center gap-3">
            <div className="bg-gray-700 p-2 rounded-lg">
              <CreditCard className="w-6 h-6 text-indigo-400" />
            </div>
            <span className="text-white">Cartão de Crédito</span>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 flex items-center gap-3">
            <div className="bg-gray-700 p-2 rounded-lg">
              <CircleDollarSign className="w-6 h-6 text-indigo-400" />
            </div>
            <span className="text-white">PIX</span>
          </div>
        </div>
      </section>

      {/* Transactions */}
      <section>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-white text-base sm:text-lg font-bold">Transações</h2>
          <button className="text-indigo-400 hover:text-indigo-300 text-xs sm:text-sm font-medium">Ver Tudo</button>
        </div>
        <div className="space-y-3">
          {transactions.length > 0 ? (
            transactions.map((transaction) => (
              <TransactionCard
                key={transaction.id}
                type={transaction.type}
                amount={transaction.amount}
                date={new Date(transaction.created_at).toLocaleString('pt-BR')}
                description={transaction.description}
                status={transaction.status}
              />
            ))
          ) : (
            <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
              <Wallet className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <h3 className="text-white font-bold text-lg mb-2">Nenhuma transação encontrada</h3>
              <p className="text-gray-400">Suas transações aparecerão aqui quando você fizer depósitos ou saques.</p>
            </div>
          )}
        </div>
      </section>
        </>
      )}

      {/* Deposit Modal */}
      {showDeposit && (
        <DepositModal
          onClose={() => setShowDeposit(false)}
          onDeposit={handleDeposit}
        />
      )}

      {/* Withdraw Modal */}
      {showWithdraw && (
        <WithdrawModal
          onClose={() => setShowWithdraw(false)}
          onWithdraw={handleWithdraw}
          balance={balance}
        />
      )}
    </div>
  );
}
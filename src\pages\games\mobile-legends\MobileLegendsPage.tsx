import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Users, Trophy, Gamepad2, Star } from 'lucide-react';

export default function MobileLegendsPage() {
  const { gameId } = useParams();
  const navigate = useNavigate();

  const game = {
    id: '3',
    title: 'Mobile Legends: Bang Bang',
    description: 'Arena de batalha móvel com heróis únicos e batalhas intensas',
    imageUrl: 'https://images.unsplash.com/photo-1579373903781-fd5c0c30c4cd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    currentPlayers: 15000,
    rating: 4.5,
    genre: 'MOBA',
    releaseDate: '2023',
    publisher: 'Moonton',
    features: ['Heróis', 'Competitivo', '5v5', 'Ranqueado']
  };

  return (
    <div className="space-y-6">
      {/* Game Banner */}
      <div className="relative h-[300px] rounded-xl overflow-hidden">
        <img
          src={game.imageUrl}
          alt={game.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent">
          <div className="absolute bottom-0 left-0 right-0 p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-white mb-2">{game.title}</h1>
                <div className="flex items-center gap-4 text-gray-300">
                  <span className="flex items-center gap-1">
                    <Users className="w-5 h-5" />
                    {game.currentPlayers.toLocaleString()} online
                  </span>
                  <span className="flex items-center gap-1">
                    <Star className="w-5 h-5 text-yellow-400" />
                    {game.rating} / 5
                  </span>
                  <span>{game.genre}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Game Info */}
      <div className="bg-gray-800 rounded-xl p-6">
        <h2 className="text-white text-xl font-bold mb-4">Sobre o Jogo</h2>
        <p className="text-gray-400">{game.description}</p>
        
        <div className="grid grid-cols-2 gap-4 mt-6">
          <div>
            <h3 className="text-gray-400 text-sm mb-2">Lançamento</h3>
            <p className="text-white">{game.releaseDate}</p>
          </div>
          <div>
            <h3 className="text-gray-400 text-sm mb-2">Desenvolvedor</h3>
            <p className="text-white">{game.publisher}</p>
          </div>
        </div>

        <div className="mt-6">
          <h3 className="text-gray-400 text-sm mb-2">Características</h3>
          <div className="flex flex-wrap gap-2">
            {game.features.map((feature, index) => (
              <span
                key={index}
                className="bg-gray-700 text-white px-3 py-1 rounded-full text-sm"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
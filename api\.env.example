# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Database Configuration (Supabase)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORGANIZATION_ID=your-openai-org-id
OPENAI_MODEL=gpt-4-vision-preview

# Payment Gateway Configuration
PAYMENT_GATEWAY_API_KEY=your-payment-gateway-api-key
PAYMENT_GATEWAY_SECRET=your-payment-gateway-secret
PAYMENT_GATEWAY_WEBHOOK_SECRET=your-webhook-secret

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
EMAIL_FROM=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Game Configuration
GAME_ROOM_TIMEOUT=1800
MATCHMAKING_TIMEOUT=300
MATCH_AUTO_CONFIRM_TIME=300

# Affiliate Program
DEFAULT_COMMISSION_RATE=0.15
MIN_PAYOUT_AMOUNT=10.00

# Wallet Limits
MIN_DEPOSIT_AMOUNT=10.00
MAX_DEPOSIT_AMOUNT=5000.00
MIN_WITHDRAWAL_AMOUNT=10.00
MAX_WITHDRAWAL_AMOUNT=10000.00

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret

# External APIs (Optional)
TWITCH_CLIENT_ID=your-twitch-client-id
TWITCH_CLIENT_SECRET=your-twitch-client-secret
DISCORD_CLIENT_ID=your-discord-client-id
DISCORD_CLIENT_SECRET=your-discord-client-secret

# Monitoring (Optional)
SENTRY_DSN=your-sentry-dsn
ANALYTICS_API_KEY=your-analytics-api-key

# Feature Flags
ENABLE_TOURNAMENTS=true
ENABLE_AFFILIATE_PROGRAM=true
ENABLE_CLUBS=true
ENABLE_STREAMS=true
ENABLE_CHAT=true

import React, { useState, useEffect } from 'react';
import { Search, Users, Trophy, Star, Radio, Filter, X, Play, Eye, Clock, ChevronRight, Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { realStreamService, Stream, StreamCategory } from '../services/realStreamService';

// Removendo interface local - usando a do serviço

export default function StreamsPage() {
  const navigate = useNavigate();
  const [selectedGame, setSelectedGame] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<'all' | 'tournament' | 'casual'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [streams, setStreams] = useState<Stream[]>([]);
  const [categories, setCategories] = useState<StreamCategory[]>([]);
  const [featuredStreams, setFeaturedStreams] = useState<Stream[]>([]);

  // Carregar dados da API
  useEffect(() => {
    const loadStreamsData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando streams...');

        // Carregar dados em paralelo
        const [streamsData, categoriesData, featuredData] = await Promise.all([
          realStreamService.getStreams(1, 20),
          realStreamService.getStreamCategories(),
          realStreamService.getFeaturedStreams()
        ]);

        setStreams(streamsData);
        setCategories(categoriesData);
        setFeaturedStreams(featuredData);

        console.log(`✅ ${streamsData.length} streams carregadas`);
        console.log(`✅ ${categoriesData.length} categorias carregadas`);
        console.log(`✅ ${featuredData.length} streams em destaque carregadas`);

      } catch (err) {
        console.error('❌ Erro ao carregar streams:', err);
        setError('Erro ao carregar streams. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadStreamsData();
  }, []);

  // Usar categorias da API
  const games = categories.map(cat => ({ id: cat.id, name: cat.name }));

  const filteredStreams = streams.filter(stream => {
    // Filter by game
    if (selectedGame && stream.game.id !== selectedGame) {
      return false;
    }

    // Filter by type (adaptar para o novo formato)
    if (selectedType !== 'all') {
      const streamType = stream.is_featured ? 'tournament' : 'casual';
      if (streamType !== selectedType) {
        return false;
      }
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesTitle = stream.title.toLowerCase().includes(query);
      const matchesStreamer = stream.streamer.display_name.toLowerCase().includes(query);
      if (!matchesTitle && !matchesStreamer) {
        return false;
      }
    }

    return true;
  });

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-indigo-500 animate-spin mx-auto mb-4" />
          <p className="text-gray-400">Carregando streams...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-xl p-4 sm:p-6 mb-6 sm:mb-8">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mt-10 -mr-10 blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -mb-8 -ml-8 blur-xl"></div>

        <div className="relative z-10">
          <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
            <div className="bg-white/20 backdrop-blur-sm p-3 sm:p-4 rounded-xl">
              <Radio className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <div>
              <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-white mb-1 sm:mb-2">Streams ao Vivo</h1>
              <p className="text-white/80 text-sm sm:text-base">Assista aos melhores jogadores e torneios em tempo real</p>
            </div>
          </div>

          <div className="grid grid-cols-1 xs:grid-cols-3 gap-3 sm:gap-4 max-w-2xl">
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <Users className="w-4 h-4 text-white" />
                <span className="text-white/80 text-sm">Espectadores</span>
              </div>
              <p className="text-white font-bold text-xl">42.8K</p>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <Radio className="w-4 h-4 text-white" />
                <span className="text-white/80 text-sm">Streams</span>
              </div>
              <p className="text-white font-bold text-xl">156</p>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <Trophy className="w-4 h-4 text-white" />
                <span className="text-white/80 text-sm">Torneios</span>
              </div>
              <p className="text-white font-bold text-xl">12</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Buscar streams..."
            className="w-full bg-gray-800/50 text-white rounded-xl py-2 sm:py-3 pl-9 sm:pl-10 pr-9 sm:pr-10 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-indigo-400 backdrop-blur-sm border border-white/10"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        <div className="flex flex-wrap gap-2 overflow-x-auto hide-scrollbar pb-1">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 p-2 rounded-lg">
            <Filter className="w-5 h-5 text-indigo-400" />
          </div>

          {/* Game Filter */}
          {games.map(game => (
            <button
              key={game.id}
              onClick={() => setSelectedGame(selectedGame === game.id ? null : game.id)}
              className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 text-xs sm:text-sm whitespace-nowrap ${
                selectedGame === game.id
                  ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                  : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
              }`}
            >
              {game.name}
            </button>
          ))}

          {/* Type Filter */}
          <button
            onClick={() => setSelectedType(selectedType === 'tournament' ? 'all' : 'tournament')}
            className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 flex items-center gap-1 sm:gap-2 text-xs sm:text-sm whitespace-nowrap ${
              selectedType === 'tournament'
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            <Trophy className="w-4 h-4" />
            Torneios
          </button>

          <button
            onClick={() => setSelectedType(selectedType === 'casual' ? 'all' : 'casual')}
            className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 flex items-center gap-1 sm:gap-2 text-xs sm:text-sm whitespace-nowrap ${
              selectedType === 'casual'
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            <Play className="w-4 h-4" />
            Casual
          </button>

          {(selectedGame || selectedType !== 'all') && (
            <button
              onClick={() => {
                setSelectedGame(null);
                setSelectedType('all');
              }}
              className="px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg bg-gray-800/50 backdrop-blur-sm border border-white/10 text-gray-400 hover:text-white hover:bg-gray-700/50 transition-all duration-200 flex items-center gap-1 text-xs sm:text-sm whitespace-nowrap"
            >
              <X className="w-4 h-4" />
              Limpar filtros
            </button>
          )}
        </div>
      </div>

      {/* Featured Stream */}
      {filteredStreams.length > 0 && filteredStreams.some(s => s.type === 'tournament') && (
        <div className="mb-8">
          <h2 className="text-white text-lg font-bold mb-4 flex items-center gap-2">
            <Trophy className="w-5 h-5 text-indigo-400" />
            Torneio em Destaque
          </h2>

          {(() => {
            const featuredStream = filteredStreams.find(s => s.type === 'tournament');
            if (!featuredStream) return null;

            return (
              <div
                className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden cursor-pointer group hover:shadow-xl hover:shadow-indigo-500/10 transition-all duration-300"
                onClick={() => window.open(featuredStream.twitchUrl, '_blank')}
              >
                <div className="relative aspect-video">
                  <img
                    src={featuredStream.thumbnail}
                    alt={featuredStream.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-all duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />

                  <div className="absolute top-4 left-4 bg-red-500 text-white text-sm px-3 py-1 rounded-full flex items-center gap-1.5 shadow-lg">
                    <Radio className="w-3.5 h-3.5" fill="white" />
                    <span className="font-medium">AO VIVO</span>
                  </div>

                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center gap-3 mb-3">
                      <img
                        src={featuredStream.streamer.avatar}
                        alt={featuredStream.streamer.name}
                        className="w-12 h-12 rounded-full ring-2 ring-white/20"
                      />
                      <div>
                        <h3 className="text-white font-bold text-xl">{featuredStream.title}</h3>
                        <p className="text-indigo-400 font-medium">{featuredStream.streamer.name}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-gray-300">
                      <div className="flex items-center gap-1.5">
                        <Eye className="w-4 h-4 text-indigo-400" />
                        <span>{featuredStream.viewers.toLocaleString()} espectadores</span>
                      </div>
                      <div className="flex items-center gap-1.5">
                        <Trophy className="w-4 h-4 text-yellow-400" />
                        <span>Torneio Oficial</span>
                      </div>
                      <div className="flex items-center gap-1.5">
                        <Star className="w-4 h-4 text-yellow-400" fill="#EAB308" />
                        <span>{featuredStream.game}</span>
                      </div>
                    </div>

                    <button className="mt-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 sm:px-6 py-1.5 sm:py-2 rounded-lg flex items-center gap-1 sm:gap-2 hover:opacity-90 shadow-lg transition-all duration-300 hover:scale-105 w-auto text-sm sm:text-base">
                      <Play className="w-4 h-4" fill="white" />
                      <span>Assistir Agora</span>
                    </button>
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      )}

      {/* Streams Grid */}
      <div>
        <h2 className="text-white text-lg font-bold mb-4 flex items-center gap-2">
          <Radio className="w-5 h-5 text-indigo-400" />
          Streams ao Vivo
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
        {filteredStreams.map(stream => (
          <div
            key={stream.id}
            className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden cursor-pointer group hover:scale-[1.02] hover:shadow-xl hover:shadow-indigo-500/10 transition-all duration-300"
            onClick={() => window.open(stream.twitchUrl, '_blank')}
          >
            {/* Thumbnail */}
            <div className="relative aspect-video">
              <img
                src={stream.thumbnail}
                alt={stream.title}
                className="w-full h-full object-cover group-hover:scale-110 transition-all duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded-full flex items-center gap-1 shadow-md">
                <Radio className="w-3 h-3" fill="white" />
                <span className="font-medium">AO VIVO</span>
              </div>
              <div className="absolute top-2 right-2 bg-black/70 backdrop-blur-sm text-white text-xs px-2 py-0.5 rounded-full flex items-center gap-1">
                <Eye className="w-3 h-3" />
                <span>{stream.viewers.toLocaleString()}</span>
              </div>
              {stream.type === 'tournament' && (
                <div className="absolute bottom-2 left-2 bg-gradient-to-r from-indigo-500 to-purple-500 text-white font-medium text-xs px-3 py-0.5 rounded-full flex items-center gap-1 shadow-md">
                  <Trophy className="w-3 h-3" />
                  <span>Torneio</span>
                </div>
              )}
              {stream.type === 'casual' && (
                <div className="absolute bottom-2 left-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium text-xs px-3 py-0.5 rounded-full flex items-center gap-1 shadow-md">
                  <Play className="w-3 h-3" fill="white" />
                  <span>Casual</span>
                </div>
              )}
            </div>

            {/* Stream Info */}
            <div className="p-4">
              <div className="flex items-start gap-3">
                <img
                  src={stream.streamer.avatar}
                  alt={`${stream.streamer.name} on Twitch`}
                  className="w-10 h-10 rounded-full ring-2 ring-white/10"
                />
                <div>
                  <h3 className="text-white font-semibold line-clamp-1">{stream.title}</h3>
                  <p className="bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent font-medium text-sm hover:underline">{stream.streamer.name}</p>
                  <p className="text-gray-400 text-sm">{stream.game}</p>
                </div>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mt-3">
                {stream.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="text-xs bg-gray-700/50 backdrop-blur-sm border border-white/10 text-gray-300 px-2 py-1 rounded-full hover:bg-gray-600/50 transition-colors"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}
        </div>
      </div>

      {filteredStreams.length === 0 && (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Radio className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-white font-bold text-lg mb-2">Nenhuma stream encontrada</h3>
          <p className="text-gray-400 mb-4">
            {selectedGame && selectedType !== 'all' ?
              `Não encontramos streams de ${games.find(g => g.id === selectedGame)?.name} do tipo ${selectedType === 'tournament' ? 'torneio' : 'casual'}.` :
             selectedGame ?
              `Não encontramos streams de ${games.find(g => g.id === selectedGame)?.name}.` :
             selectedType !== 'all' ?
              `Não encontramos streams do tipo ${selectedType === 'tournament' ? 'torneio' : 'casual'}.` :
              'Não há streams disponíveis no momento.'}
          </p>
          {(selectedGame || selectedType !== 'all') && (
            <button
              onClick={() => {
                setSelectedGame(null);
                setSelectedType('all');
              }}
              className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg inline-flex items-center gap-1 sm:gap-2 hover:opacity-90 shadow-lg transition-all duration-300 hover:scale-105 text-xs sm:text-sm"
            >
              <X className="w-4 h-4" />
              Limpar filtros
            </button>
          )}
        </div>
      )}
    </div>
  );
}
-- Migration 003: Tournaments
-- Created: 2024-01-01
-- Description: Creates tables for tournaments, brackets, and tournament management

-- Tournament format types
CREATE TYPE tournament_format AS ENUM ('elimination', 'round_robin', 'swiss', 'points_based');
CREATE TYPE bracket_status AS ENUM ('upcoming', 'in_progress', 'completed');
CREATE TYPE participant_status AS ENUM ('registered', 'confirmed', 'eliminated', 'withdrawn');

-- Tournaments table
CREATE TABLE tournaments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
    organizer_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    rules TEXT,
    format tournament_format NOT NULL,
    status tournament_status DEFAULT 'upcoming',
    is_public BOOLEAN DEFAULT TRUE,
    entry_fee DECIMAL(10,2) NOT NULL CHECK (entry_fee >= 0),
    max_participants INTEGER NOT NULL,
    current_participants INTEGER DEFAULT 0,
    prize_pool DECIMAL(12,2) NOT NULL CHECK (prize_pool >= 0),
    prize_distribution JSONB DEFAULT '{"first": 60, "second": 30, "third": 10}', -- Percentage distribution
    registration_start_date TIMESTAMP WITH TIME ZONE,
    registration_end_date TIMESTAMP WITH TIME ZONE,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE,
    tournament_settings JSONB DEFAULT '{}',
    banner_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT max_participants_valid CHECK (max_participants >= 4 AND max_participants <= 1024),
    CONSTRAINT current_participants_valid CHECK (current_participants >= 0 AND current_participants <= max_participants),
    CONSTRAINT valid_dates CHECK (start_date > registration_end_date)
);

-- Tournament seasons table (for multi-stage tournaments)
CREATE TABLE tournament_seasons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    season_order INTEGER NOT NULL,
    status bracket_status DEFAULT 'upcoming',
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    max_participants INTEGER,
    advancement_rules JSONB DEFAULT '{}', -- Rules for advancing to next season
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tournament_id, season_order)
);

-- Tournament brackets table (sub-groups within seasons)
CREATE TABLE tournament_brackets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
    season_id UUID REFERENCES tournament_seasons(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    bracket_order INTEGER NOT NULL,
    status bracket_status DEFAULT 'upcoming',
    max_participants INTEGER NOT NULL,
    current_participants INTEGER DEFAULT 0,
    bracket_type VARCHAR(50) DEFAULT 'single_elimination', -- 'single_elimination', 'double_elimination', 'round_robin'
    winner_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    bracket_data JSONB DEFAULT '{}', -- Store bracket structure and progression
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT bracket_current_participants_valid CHECK (current_participants >= 0 AND current_participants <= max_participants)
);

-- Tournament participants table
CREATE TABLE tournament_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    season_id UUID REFERENCES tournament_seasons(id) ON DELETE CASCADE,
    bracket_id UUID REFERENCES tournament_brackets(id) ON DELETE CASCADE,
    status participant_status DEFAULT 'registered',
    seed_number INTEGER, -- Seeding position
    current_position INTEGER, -- Current standing/ranking
    final_placement INTEGER, -- Final tournament placement
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    elimination_date TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    UNIQUE(tournament_id, user_id),
    CONSTRAINT valid_seed_number CHECK (seed_number >= 1),
    CONSTRAINT valid_current_position CHECK (current_position >= 1),
    CONSTRAINT valid_final_placement CHECK (final_placement >= 1)
);

-- Add tournament_id foreign key to matches table
ALTER TABLE matches ADD COLUMN tournament_id UUID REFERENCES tournaments(id) ON DELETE CASCADE;
ALTER TABLE matches ADD COLUMN bracket_id UUID REFERENCES tournament_brackets(id) ON DELETE CASCADE;
ALTER TABLE matches ADD COLUMN round_number INTEGER;
ALTER TABLE matches ADD COLUMN match_number INTEGER; -- Match number within the round

-- Tournament match schedule table
CREATE TABLE tournament_match_schedule (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
    bracket_id UUID NOT NULL REFERENCES tournament_brackets(id) ON DELETE CASCADE,
    match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
    round_number INTEGER NOT NULL,
    match_number INTEGER NOT NULL,
    scheduled_time TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER DEFAULT 30, -- in minutes
    dependencies JSONB DEFAULT '[]', -- Match IDs that must complete before this match
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tournament_id, bracket_id, round_number, match_number)
);

-- Tournament prizes table
CREATE TABLE tournament_prizes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
    placement INTEGER NOT NULL,
    prize_amount DECIMAL(10,2) NOT NULL CHECK (prize_amount >= 0),
    prize_type VARCHAR(50) DEFAULT 'cash', -- 'cash', 'trophy', 'badge', 'item'
    prize_description TEXT,
    awarded_to_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    awarded_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(tournament_id, placement),
    CONSTRAINT valid_placement CHECK (placement >= 1)
);

-- Tournament announcements table
CREATE TABLE tournament_announcements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    announcement_type VARCHAR(50) DEFAULT 'general', -- 'general', 'rule_change', 'schedule_update', 'results'
    is_important BOOLEAN DEFAULT FALSE,
    created_by_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_tournaments_game_id ON tournaments(game_id);
CREATE INDEX idx_tournaments_organizer_user_id ON tournaments(organizer_user_id);
CREATE INDEX idx_tournaments_status ON tournaments(status);
CREATE INDEX idx_tournaments_start_date ON tournaments(start_date);
CREATE INDEX idx_tournaments_registration_end_date ON tournaments(registration_end_date);
CREATE INDEX idx_tournaments_is_public ON tournaments(is_public);

CREATE INDEX idx_tournament_seasons_tournament_id ON tournament_seasons(tournament_id);
CREATE INDEX idx_tournament_seasons_status ON tournament_seasons(status);
CREATE INDEX idx_tournament_seasons_season_order ON tournament_seasons(season_order);

CREATE INDEX idx_tournament_brackets_tournament_id ON tournament_brackets(tournament_id);
CREATE INDEX idx_tournament_brackets_season_id ON tournament_brackets(season_id);
CREATE INDEX idx_tournament_brackets_status ON tournament_brackets(status);

CREATE INDEX idx_tournament_participants_tournament_id ON tournament_participants(tournament_id);
CREATE INDEX idx_tournament_participants_user_id ON tournament_participants(user_id);
CREATE INDEX idx_tournament_participants_status ON tournament_participants(status);
CREATE INDEX idx_tournament_participants_bracket_id ON tournament_participants(bracket_id);

CREATE INDEX idx_matches_tournament_id ON matches(tournament_id);
CREATE INDEX idx_matches_bracket_id ON matches(bracket_id);
CREATE INDEX idx_matches_round_number ON matches(round_number);

CREATE INDEX idx_tournament_match_schedule_tournament_id ON tournament_match_schedule(tournament_id);
CREATE INDEX idx_tournament_match_schedule_bracket_id ON tournament_match_schedule(bracket_id);
CREATE INDEX idx_tournament_match_schedule_scheduled_time ON tournament_match_schedule(scheduled_time);

CREATE INDEX idx_tournament_prizes_tournament_id ON tournament_prizes(tournament_id);
CREATE INDEX idx_tournament_prizes_placement ON tournament_prizes(placement);

CREATE INDEX idx_tournament_announcements_tournament_id ON tournament_announcements(tournament_id);
CREATE INDEX idx_tournament_announcements_created_at ON tournament_announcements(created_at);

-- Apply updated_at triggers
CREATE TRIGGER update_tournaments_updated_at BEFORE UPDATE ON tournaments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tournament_seasons_updated_at BEFORE UPDATE ON tournament_seasons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tournament_brackets_updated_at BEFORE UPDATE ON tournament_brackets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

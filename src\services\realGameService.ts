import { apiService } from './apiService';

// Tipos para jogos
export interface Game {
  id: string;
  name: string;
  slug: string;
  description?: string;
  type: 'external' | 'internal';
  image_url?: string;
  icon_url?: string;
  is_active: boolean;
  min_players: number;
  max_players: number;
  default_entry_fee: number;
  supported_platforms?: string[];
  game_modes?: string[];
  settings_schema?: Record<string, any>;
  created_at: string;
  updated_at: string;
  // Campos opcionais para compatibilidade
  player_count?: number;
  min_bet?: number;
  max_bet?: number;
  tags?: string[];
}

export interface GameRoom {
  id: string;
  game_id: string;
  name: string;
  description?: string;
  host_user_id: string;
  entry_fee: number;
  max_players: number;
  status: 'waiting' | 'starting' | 'playing' | 'finished';
  is_public: boolean;
  password_hash?: string;
  game_settings?: Record<string, any>;
  created_at: string;
  scheduled_start_time?: string;
  room_code?: string;
  games?: {
    id: string;
    name: string;
    slug: string;
    icon_url?: string;
  };
  users?: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
  room_participants?: GameRoomParticipant[];
  room_chat_messages?: GameRoomChatMessage[];
}

export interface GameRoomParticipant {
  user_id: string;
  is_ready: boolean;
  joined_at: string;
  users: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

export interface GameRoomChatMessage {
  id: string;
  message: string;
  message_type: 'text' | 'system' | 'image';
  created_at: string;
  users: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

export interface GameStats {
  game_id: string;
  total_matches: number;
  total_players: number;
  average_bet: number;
  total_prize_pool: number;
  top_players: Array<{
    user_id: string;
    username: string;
    wins: number;
    earnings: number;
  }>;
}

export interface CreateRoomRequest {
  game_id: string;
  name: string;
  description?: string;
  bet_amount: number;
  max_players: number;
  is_private: boolean;
  password?: string;
  game_settings?: Record<string, any>;
  scheduled_time?: string;
}

class RealGameService {

  // ===== JOGOS =====

  // Obtém lista de jogos
  async getGames(category?: 'external' | 'internal', type?: string): Promise<Game[]> {
    try {
      console.log('🎮 Buscando lista de jogos...');

      const params: any = {};
      if (category) params.category = category;
      if (type) params.type = type;

      const response = await apiService.get<{games: Game[]}>('/games', { params });

      if (response.success && response.data?.games) {
        console.log(`✅ ${response.data.games.length} jogos carregados`);
        return response.data.games;
      } else {
        console.warn('⚠️ Erro ao carregar jogos:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar jogos:', error);
      return [];
    }
  }

  // Obtém jogos externos
  async getExternalGames(): Promise<Game[]> {
    try {
      console.log('🌐 Buscando jogos externos...');

      const response = await apiService.get<{games: Game[]}>('/games/external');

      if (response.success && response.data?.games) {
        console.log(`✅ ${response.data.games.length} jogos externos carregados`);
        return response.data.games;
      } else {
        console.warn('⚠️ Erro ao carregar jogos externos:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar jogos externos:', error);
      return [];
    }
  }

  // Obtém jogos internos
  async getInternalGames(): Promise<Game[]> {
    try {
      console.log('🏠 Buscando jogos internos...');

      const response = await apiService.get<{games: Game[]}>('/games/internal');

      if (response.success && response.data?.games) {
        console.log(`✅ ${response.data.games.length} jogos internos carregados`);
        return response.data.games;
      } else {
        console.warn('⚠️ Erro ao carregar jogos internos:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar jogos internos:', error);
      return [];
    }
  }

  // Obtém detalhes de um jogo
  async getGameById(gameId: string): Promise<Game | null> {
    try {
      console.log(`🔍 Buscando detalhes do jogo ${gameId}...`);

      const response = await apiService.get<{game: Game}>(`/games/${gameId}`);

      if (response.success && response.data?.game) {
        console.log(`✅ Jogo ${gameId} carregado`);
        return response.data.game;
      } else {
        console.warn(`⚠️ Erro ao carregar jogo ${gameId}:`, response.error);
        return null;
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar jogo ${gameId}:`, error);
      return null;
    }
  }

  // Obtém estatísticas de um jogo
  async getGameStats(gameId: string): Promise<GameStats | null> {
    try {
      console.log(`📊 Buscando estatísticas do jogo ${gameId}...`);

      const response = await apiService.get<GameStats>(`/games/${gameId}/stats`);

      if (response.success && response.data) {
        console.log(`✅ Estatísticas do jogo ${gameId} carregadas`);
        return response.data;
      } else {
        console.warn(`⚠️ Erro ao carregar estatísticas do jogo ${gameId}:`, response.error);
        return null;
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar estatísticas do jogo ${gameId}:`, error);
      return null;
    }
  }

  // ===== SALAS DE JOGOS =====

  // Obtém salas de um jogo
  async getGameRooms(gameId: string, status?: string): Promise<GameRoom[]> {
    try {
      console.log(`🏠 Buscando salas do jogo ${gameId}...`);

      const params: any = {};
      if (status) params.status = status;

      const response = await apiService.get<{rooms: GameRoom[]}>(`/games/${gameId}/rooms`, { params });

      if (response.success && response.data?.rooms) {
        console.log(`✅ ${response.data.rooms.length} salas carregadas`);
        return response.data.rooms;
      } else {
        console.warn('⚠️ Erro ao carregar salas:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar salas:', error);
      return [];
    }
  }

  // Obtém todas as salas ativas
  async getActiveRooms(): Promise<GameRoom[]> {
    try {
      console.log('🏠 Buscando salas ativas...');

      const response = await apiService.get<GameRoom[]>('/games/rooms/active');

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} salas ativas carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar salas ativas:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar salas ativas:', error);
      return [];
    }
  }

  // Obtém detalhes de uma sala
  async getRoomById(roomId: string): Promise<GameRoom | null> {
    try {
      console.log(`🔍 Buscando detalhes da sala ${roomId}...`);

      const response = await apiService.get<{room: GameRoom}>(`/games/rooms/${roomId}`);

      if (response.success && response.data?.room) {
        console.log(`✅ Sala ${roomId} carregada`);
        return response.data.room;
      } else {
        console.warn(`⚠️ Erro ao carregar sala ${roomId}:`, response.error);
        return null;
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar sala ${roomId}:`, error);
      return null;
    }
  }

  // Cria uma nova sala
  async createRoom(roomData: CreateRoomRequest): Promise<{ success: boolean; room_id?: string; error?: string }> {
    try {
      console.log(`🏗️ Criando sala para ${roomData.game_id}...`);

      const response = await apiService.post<{ room_id: string }>('/games/rooms', roomData);

      if (response.success && response.data) {
        console.log(`✅ Sala criada: ${response.data.room_id}`);
        return {
          success: true,
          room_id: response.data.room_id
        };
      } else {
        console.warn('⚠️ Erro ao criar sala:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao criar sala'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao criar sala:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Cria uma nova sala de jogo (endpoint correto)
  async createGameRoom(roomData: {
    game_id: string;
    name: string;
    type: '1v1' | 'tournament' | 'practice';
    entry_fee: number;
    max_players: number;
    is_public: boolean;
    password?: string;
    game_settings?: Record<string, any>;
  }): Promise<GameRoom | null> {
    try {
      console.log(`🏗️ Criando sala de jogo para ${roomData.game_id}...`);

      const response = await apiService.post<{ room: GameRoom }>(`/games/${roomData.game_id}/rooms`, roomData);

      if (response.success && response.data?.room) {
        console.log(`✅ Sala de jogo criada: ${response.data.room.id}`);
        return response.data.room;
      } else {
        console.warn('⚠️ Erro ao criar sala de jogo:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao criar sala de jogo:', error);
      return null;
    }
  }

  // Entra em uma sala
  async joinRoom(roomId: string, password?: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🚪 Entrando na sala ${roomId}...`);

      const data: any = {};
      if (password) data.password = password;

      const response = await apiService.post(`/games/rooms/${roomId}/join`, data);

      if (response.success) {
        console.log(`✅ Entrou na sala ${roomId}`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao entrar na sala:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao entrar na sala'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao entrar na sala:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Sai de uma sala
  async leaveRoom(roomId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🚪 Saindo da sala ${roomId}...`);

      const response = await apiService.post(`/games/rooms/${roomId}/leave`);

      if (response.success) {
        console.log(`✅ Saiu da sala ${roomId}`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao sair da sala:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao sair da sala'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao sair da sala:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Inicia uma partida na sala
  async startRoomMatch(roomId: string): Promise<{ success: boolean; match_id?: string; error?: string }> {
    try {
      console.log(`🎮 Iniciando partida na sala ${roomId}...`);

      const response = await apiService.post<{ match_id: string }>(`/games/rooms/${roomId}/start`);

      if (response.success && response.data) {
        console.log(`✅ Partida iniciada: ${response.data.match_id}`);
        return {
          success: true,
          match_id: response.data.match_id
        };
      } else {
        console.warn('⚠️ Erro ao iniciar partida:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao iniciar partida'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao iniciar partida:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }
}

// Instância singleton
export const realGameService = new RealGameService();

// Funções de compatibilidade
export const getGames = realGameService.getGames.bind(realGameService);
export const getExternalGames = realGameService.getExternalGames.bind(realGameService);
export const getInternalGames = realGameService.getInternalGames.bind(realGameService);
export const getGameRooms = realGameService.getGameRooms.bind(realGameService);

import { Request } from 'express';

// Base API response interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: PaginationInfo;
  meta?: any;
}

// Pagination interface
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Query parameters for pagination
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Extended Request interface with user information
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    username: string;
    role?: string;
  };
}

// User registration data
export interface UserRegistrationData {
  email: string;
  username: string;
  display_name: string;
  password: string;
  birth_date?: string;
  country?: string;
  timezone?: string;
  language?: string;
  referral_code?: string | null;
}

// User login data
export interface UserLoginData {
  email: string;
  password: string;
}

// User profile update data
export interface UserProfileUpdateData {
  display_name?: string;
  bio?: string;
  avatar_url?: string;
  favorite_games?: string[];
  gaming_experience?: string;
  preferred_game_modes?: string[];
  social_links?: Record<string, string>;
  privacy_settings?: Record<string, any>;
  notification_preferences?: Record<string, any>;
}

// Wallet transaction data
export interface TransactionData {
  type: 'deposit' | 'withdrawal' | 'bet' | 'win' | 'commission' | 'refund';
  amount: number;
  payment_method?: 'pix' | 'credit_card' | 'bank_transfer';
  description?: string;
  metadata?: Record<string, any>;
}

// Game room creation data
export interface GameRoomCreationData {
  game_id: string;
  name: string;
  type: '1v1' | 'tournament' | 'practice';
  entry_fee: number;
  max_players: number;
  is_public: boolean;
  password?: string;
  game_settings?: Record<string, any>;
  scheduled_start_time?: string;
}

// Match creation data
export interface MatchCreationData {
  game_id: string;
  room_id?: string;
  tournament_id?: string;
  match_type: string;
  entry_fee: number;
  game_settings?: Record<string, any>;
  scheduled_time?: string;
  participants: string[]; // user IDs
}

// Tournament creation data
export interface TournamentCreationData {
  game_id: string;
  title: string;
  description?: string;
  rules?: string;
  format: 'elimination' | 'round_robin' | 'swiss' | 'points_based';
  is_public: boolean;
  entry_fee: number;
  max_participants: number;
  prize_distribution: Record<string, number>;
  registration_start_date?: string;
  registration_end_date: string;
  start_date: string;
  end_date?: string;
  tournament_settings?: Record<string, any>;
}

// Club creation data
export interface ClubCreationData {
  name: string;
  description?: string;
  rules?: string;
  game_id?: string;
  privacy_type: 'public' | 'private' | 'invite_only';
  max_members?: number;
  region?: string;
  tags?: string[];
}

// Friend request data
export interface FriendRequestData {
  addressee_user_id: string;
}

// Challenge data
export interface ChallengeData {
  challenged_user_id: string;
  game_id: string;
  bet_amount: number;
  game_settings?: Record<string, any>;
  message?: string;
}

// Message data
export interface MessageData {
  message: string;
  message_type?: 'text' | 'image' | 'system';
  attachment_url?: string;
  reply_to_message_id?: string;
}

// Notification data
export interface NotificationData {
  user_id: string;
  type: 'friend_request' | 'match_found' | 'tournament_update' | 'system' | 'achievement';
  title: string;
  message: string;
  data?: Record<string, any>;
  is_important?: boolean;
  action_url?: string;
  expires_at?: string;
}

// Search filters
export interface SearchFilters {
  query?: string;
  category?: string;
  game_id?: string;
  status?: string;
  date_from?: string;
  date_to?: string;
  min_amount?: number;
  max_amount?: number;
  tags?: string[];
}

// Match result data
export interface MatchResultData {
  match_id: string;
  winner_user_id?: string;
  result_data: Record<string, any>;
  result_image_url?: string;
  participants_scores: Array<{
    user_id: string;
    score: number;
    placement: number;
    stats?: Record<string, any>;
  }>;
}

// Affiliate payout data
export interface AffiliatePayoutData {
  amount: number;
  payment_method: 'pix' | 'credit_card' | 'bank_transfer';
  payment_details: Record<string, any>;
}

// Stream data
export interface StreamData {
  title: string;
  description?: string;
  game_id?: string;
  stream_url: string;
  thumbnail_url?: string;
  platform: string;
  external_stream_id?: string;
}

// Report data
export interface ReportData {
  reported_user_id: string;
  match_id?: string;
  reason: string;
  description?: string;
  evidence_urls?: string[];
}

// System settings data
export interface SystemSettingsData {
  key: string;
  value: any;
  description?: string;
  is_public?: boolean;
}

// File upload data
export interface FileUploadData {
  file: any; // Multer file type
  bucket: string;
  folder?: string;
  public?: boolean;
}

// Notification settings data
export interface NotificationSettingsData {
  email: boolean;
  push: boolean;
  friend_requests: boolean;
  match_updates: boolean;
  tournament_updates: boolean;
}

// Error types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  statusCode: number;
}

// Validation error
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Rate limit info
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}

// Health check response
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: {
      status: 'healthy' | 'unhealthy';
      responseTime: number;
      error?: string;
    };
    redis?: {
      status: 'healthy' | 'unhealthy';
      responseTime: number;
      error?: string;
    };
    external_apis: {
      openai: {
        status: 'healthy' | 'unhealthy';
        responseTime: number;
        error?: string;
      };
      payment_gateway?: {
        status: 'healthy' | 'unhealthy';
        responseTime: number;
        error?: string;
      };
    };
  };
}

// Analytics data
export interface AnalyticsData {
  event: string;
  user_id?: string;
  properties?: Record<string, any>;
  timestamp?: string;
}

// Webhook data
export interface WebhookData {
  event: string;
  data: any;
  timestamp: string;
  signature?: string;
}

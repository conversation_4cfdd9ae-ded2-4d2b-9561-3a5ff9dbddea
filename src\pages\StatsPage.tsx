import React, { useState, useEffect } from 'react';
import { BarChart3, Trophy, Swords, Target, Timer, Map, Users, Loader2 } from 'lucide-react';
import { realStatsService, UserStats, GameStats } from '../services/realStatsService';

interface GameStats {
  id: string;
  name: string;
  matches: number;
  winRate: number;
  kdRatio: number;
  avgScore: number;
  playtime: number;
  favoriteMap: string;
}

export default function StatsPage() {
  const [selectedGame, setSelectedGame] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'all' | 'month' | 'week'>('all');

  // Estados para dados da API
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [gameStats, setGameStats] = useState<GameStats[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carrega dados das estatísticas
  useEffect(() => {
    const loadStatsData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando estatísticas...');

        // Carrega estatísticas gerais e por jogo em paralelo
        const [userStatsData, gameStatsData] = await Promise.all([
          realStatsService.getUserStats(),
          realStatsService.getGameStats()
        ]);

        setUserStats(userStatsData);
        setGameStats(gameStatsData);

        console.log('✅ Estatísticas carregadas');

      } catch (err) {
        console.error('❌ Erro ao carregar estatísticas:', err);
        setError('Erro ao carregar estatísticas. Usando dados offline.');
      } finally {
        setIsLoading(false);
      }
    };

    loadStatsData();
  }, [timeRange]);

  // Dados mock como fallback
  const mockGames: GameStats[] = [
    {
      game_id: 'cs2',
      game_name: 'Counter-Strike 2',
      matches_played: 248,
      wins: 145,
      losses: 103,
      draws: 0,
      win_rate: 58.5,
      earnings: 2450.00,
      best_streak: 8,
      average_match_duration: 35,
      last_played: '2024-01-15',
      skill_level: 'advanced'
    },
    {
      game_id: 'apex',
      game_name: 'Apex Legends',
      matches_played: 124,
      wins: 52,
      losses: 72,
      draws: 0,
      win_rate: 42.3,
      earnings: 1250.00,
      best_streak: 5,
      average_match_duration: 22,
      last_played: '2024-01-14',
      skill_level: 'intermediate'
    }
  ];

  // Usa dados da API se disponíveis, senão usa mock
  const displayGames = gameStats.length > 0 ? gameStats : mockGames;

  const selectedGameStats = selectedGame
    ? displayGames.find(g => g.game_id === selectedGame)
    : displayGames[0];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white mb-2">Estatísticas</h1>
        <p className="text-gray-400">Acompanhe seu desempenho em todos os jogos</p>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-xl p-4 text-center">
          <p className="text-yellow-400 text-sm">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Loader2 className="w-8 h-8 text-indigo-400 mx-auto mb-4 animate-spin" />
          <h3 className="text-white font-bold text-lg mb-2">Carregando estatísticas...</h3>
          <p className="text-gray-400">Aguarde enquanto buscamos seus dados de desempenho.</p>
        </div>
      ) : (
        <>
      {/* Game Selection */}
      <div className="flex gap-2">
        {displayGames.map(game => (
          <button
            key={game.game_id}
            onClick={() => setSelectedGame(game.game_id)}
            className={`px-4 py-2 rounded-lg ${
              (!selectedGame && game.game_id === displayGames[0].game_id) || selectedGame === game.game_id
                ? 'bg-yellow-400 text-black'
                : 'bg-gray-800 text-white'
            }`}
          >
            {game.game_name}
          </button>
        ))}
      </div>

      {/* Time Range */}
      <div className="flex gap-2">
        <button
          onClick={() => setTimeRange('all')}
          className={`px-4 py-2 rounded-lg ${
            timeRange === 'all' ? 'bg-gray-700 text-white' : 'bg-gray-800 text-gray-400'
          }`}
        >
          Todo Período
        </button>
        <button
          onClick={() => setTimeRange('month')}
          className={`px-4 py-2 rounded-lg ${
            timeRange === 'month' ? 'bg-gray-700 text-white' : 'bg-gray-800 text-gray-400'
          }`}
        >
          Último Mês
        </button>
        <button
          onClick={() => setTimeRange('week')}
          className={`px-4 py-2 rounded-lg ${
            timeRange === 'week' ? 'bg-gray-700 text-white' : 'bg-gray-800 text-gray-400'
          }`}
        >
          Última Semana
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="bg-blue-500/20 p-2 rounded-lg">
              <Trophy className="w-6 h-6 text-blue-500" />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Taxa de Vitória</p>
              <p className="text-white text-2xl font-bold">{selectedGameStats?.win_rate.toFixed(1)}%</p>
            </div>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full"
              style={{ width: `${selectedGameStats?.win_rate}%` }}
            />
          </div>
        </div>

        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="bg-red-500/20 p-2 rounded-lg">
              <Swords className="w-6 h-6 text-red-500" />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Vitórias</p>
              <p className="text-white text-2xl font-bold">{selectedGameStats?.wins}</p>
            </div>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-red-500 h-2 rounded-full"
              style={{ width: `${(selectedGameStats?.wins / selectedGameStats?.matches_played) * 100}%` }}
            />
          </div>
        </div>

        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="bg-green-500/20 p-2 rounded-lg">
              <Target className="w-6 h-6 text-green-500" />
            </div>
            <div>
              <p className="text-gray-400 text-sm">Ganhos</p>
              <p className="text-white text-2xl font-bold">R$ {selectedGameStats?.earnings.toFixed(2)}</p>
            </div>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-green-500 h-2 rounded-full"
              style={{ width: '75%' }}
            />
          </div>
        </div>
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-1">
            <Users className="w-5 h-5 text-purple-400" />
            <p className="text-gray-400">Partidas</p>
          </div>
          <p className="text-2xl font-bold text-white">{selectedGameStats?.matches_played}</p>
        </div>

        <div className="bg-gray-800 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-1">
            <Timer className="w-5 h-5 text-yellow-400" />
            <p className="text-gray-400">Melhor Sequência</p>
          </div>
          <p className="text-2xl font-bold text-white">{selectedGameStats?.best_streak}</p>
        </div>

        <div className="bg-gray-800 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-1">
            <Map className="w-5 h-5 text-orange-400" />
            <p className="text-gray-400">Nível de Habilidade</p>
          </div>
          <p className="text-2xl font-bold text-white capitalize">{selectedGameStats?.skill_level}</p>
        </div>

        <div className="bg-gray-800 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-1">
            <Trophy className="w-5 h-5 text-emerald-400" />
            <p className="text-gray-400">Ranking</p>
          </div>
          <p className="text-2xl font-bold text-white">#42</p>
        </div>
      </div>

      {/* Performance Chart */}
      <div className="bg-gray-800 rounded-xl p-6">
        <h2 className="text-white font-bold mb-4 flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-yellow-400" />
          Desempenho ao Longo do Tempo
        </h2>
        <div className="h-64 flex items-end justify-between gap-2">
          {Array.from({ length: 12 }).map((_, i) => (
            <div
              key={i}
              className="w-full bg-yellow-400/20 rounded-t-lg"
              style={{
                height: `${Math.random() * 100}%`,
                transition: 'height 0.3s ease-in-out'
              }}
            />
          ))}
        </div>
        <div className="flex justify-between mt-2 text-sm text-gray-400">
          <span>Jan</span>
          <span>Fev</span>
          <span>Mar</span>
          <span>Abr</span>
          <span>Mai</span>
          <span>Jun</span>
          <span>Jul</span>
          <span>Ago</span>
          <span>Set</span>
          <span>Out</span>
          <span>Nov</span>
          <span>Dez</span>
        </div>
      </div>
        </>
      )}
    </div>
  );
}
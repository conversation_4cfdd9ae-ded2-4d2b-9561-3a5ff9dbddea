# Configuração do Supabase para Playstrike

Este guia detalha como configurar o Supabase para a plataforma Playstrike.

## 🚀 Configuração Inicial

### 1. Criar Projeto no Supabase

1. Acesse [supabase.com](https://supabase.com)
2. C<PERSON> em "Start your project"
3. Crie uma nova organização ou use uma existente
4. Clique em "New Project"
5. Configure:
   - **Name**: Playstrike
   - **Database Password**: Use uma senha forte
   - **Region**: South America (São Paulo) - para melhor latência no Brasil
   - **Pricing Plan**: Pro (recomendado para produção)

### 2. Configurar Autenticação

#### 2.1 Configurações Gerais
```sql
-- No SQL Editor do Supabase, execute:

-- Configurar URL do site
UPDATE auth.config 
SET site_url = 'https://seu-dominio.com';

-- Configurar URLs de redirecionamento
UPDATE auth.config 
SET additional_redirect_urls = 'https://seu-dominio.com/auth/callback,http://localhost:3000/auth/callback';
```

#### 2.2 Habilitar Provedores de Autenticação

No Dashboard do Supabase:
1. Vá para **Authentication > Settings**
2. Configure os provedores desejados:

**Email/Password:**
- ✅ Enable email confirmations
- ✅ Enable email change confirmations
- ✅ Enable secure password change

**Google OAuth (Opcional):**
```json
{
  "client_id": "seu-google-client-id",
  "client_secret": "seu-google-client-secret"
}
```

**Discord OAuth (Opcional):**
```json
{
  "client_id": "seu-discord-client-id",
  "client_secret": "seu-discord-client-secret"
}
```

### 3. Configurar Storage

#### 3.1 Criar Buckets
```sql
-- Bucket para avatars de usuários
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'avatars',
  'avatars',
  true,
  5242880, -- 5MB
  ARRAY['image/jpeg', 'image/png', 'image/webp']
);

-- Bucket para imagens de jogos
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'game-images',
  'game-images',
  true,
  10485760, -- 10MB
  ARRAY['image/jpeg', 'image/png', 'image/webp']
);

-- Bucket para resultados de partidas
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'match-results',
  'match-results',
  false,
  20971520, -- 20MB
  ARRAY['image/jpeg', 'image/png', 'image/webp']
);

-- Bucket para logos de clubes
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'club-logos',
  'club-logos',
  true,
  5242880, -- 5MB
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
);
```

#### 3.2 Políticas de Storage
```sql
-- Política para avatars - usuários podem fazer upload de seus próprios avatars
CREATE POLICY "Users can upload their own avatars" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their own avatars" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Avatars are publicly viewable" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

-- Política para imagens de jogos - apenas admins podem fazer upload
CREATE POLICY "Only admins can upload game images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'game-images' AND 
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.status = 'active'
    -- Adicione verificação de admin aqui
  )
);

CREATE POLICY "Game images are publicly viewable" ON storage.objects
FOR SELECT USING (bucket_id = 'game-images');

-- Política para resultados de partidas
CREATE POLICY "Users can upload match results" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'match-results' AND 
  auth.uid() IS NOT NULL
);

CREATE POLICY "Match participants can view results" ON storage.objects
FOR SELECT USING (
  bucket_id = 'match-results' AND 
  auth.uid() IS NOT NULL
  -- Adicione lógica para verificar se o usuário é participante da partida
);

-- Política para logos de clubes
CREATE POLICY "Club owners can upload logos" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'club-logos' AND 
  auth.uid() IS NOT NULL
);

CREATE POLICY "Club logos are publicly viewable" ON storage.objects
FOR SELECT USING (bucket_id = 'club-logos');
```

### 4. Configurar Edge Functions (Opcional)

#### 4.1 Função para Processamento de Pagamentos
```typescript
// supabase/functions/process-payment/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { amount, payment_method, user_id } = await req.json()
    
    // Lógica de processamento de pagamento aqui
    // Integração com gateway de pagamento (PIX, cartão, etc.)
    
    return new Response(
      JSON.stringify({ success: true, transaction_id: 'txn_123' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
    )
  }
})
```

#### 4.2 Função para Análise de Resultados (OpenAI)
```typescript
// supabase/functions/analyze-match-result/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { image_url, player1_name, player2_name } = await req.json()
    
    // Integração com OpenAI Vision API
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Analyze this game result image. Determine the winner between ${player1_name} and ${player2_name}. Return JSON with winner, player1Score, player2Score, and confidence.`
              },
              {
                type: 'image_url',
                image_url: { url: image_url }
              }
            ]
          }
        ]
      })
    })

    const result = await openaiResponse.json()
    
    return new Response(
      JSON.stringify(result),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
    )
  }
})
```

### 5. Configurar Webhooks

#### 5.1 Webhook para Transações
```sql
-- Criar função para webhook de transações
CREATE OR REPLACE FUNCTION notify_transaction_webhook()
RETURNS TRIGGER AS $$
BEGIN
  -- Enviar webhook quando transação for completada
  IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
    PERFORM net.http_post(
      url := 'https://seu-backend.com/webhooks/transaction-completed',
      headers := '{"Content-Type": "application/json", "Authorization": "Bearer ' || current_setting('app.webhook_secret') || '"}'::jsonb,
      body := json_build_object(
        'transaction_id', NEW.id,
        'user_id', NEW.user_id,
        'type', NEW.type,
        'amount', NEW.amount,
        'status', NEW.status
      )::text
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Criar trigger para webhook
CREATE TRIGGER transaction_webhook_trigger
  AFTER UPDATE ON transactions
  FOR EACH ROW
  EXECUTE FUNCTION notify_transaction_webhook();
```

### 6. Configurar Variáveis de Ambiente

#### 6.1 No Dashboard do Supabase
Vá para **Settings > API** e configure:

```env
# Chaves da API
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_ANON_KEY=sua-chave-anonima
SUPABASE_SERVICE_ROLE_KEY=sua-chave-service-role

# OpenAI (para análise de resultados)
OPENAI_API_KEY=sua-chave-openai
OPENAI_ORGANIZATION_ID=sua-org-openai

# Gateway de Pagamento
PAYMENT_GATEWAY_API_KEY=sua-chave-gateway
PAYMENT_GATEWAY_SECRET=seu-secret-gateway

# Webhook Secret
WEBHOOK_SECRET=seu-secret-webhook

# URLs
FRONTEND_URL=https://seu-frontend.com
BACKEND_URL=https://seu-backend.com
```

#### 6.2 Configurar Secrets para Edge Functions
```bash
# Via CLI do Supabase
supabase secrets set OPENAI_API_KEY=sua-chave-openai
supabase secrets set PAYMENT_GATEWAY_API_KEY=sua-chave-gateway
supabase secrets set WEBHOOK_SECRET=seu-secret-webhook
```

### 7. Configurar Realtime

#### 7.1 Habilitar Realtime para Tabelas Específicas
```sql
-- Habilitar realtime para chat de salas
ALTER PUBLICATION supabase_realtime ADD TABLE room_chat_messages;

-- Habilitar realtime para mensagens privadas
ALTER PUBLICATION supabase_realtime ADD TABLE private_messages;

-- Habilitar realtime para notificações
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

-- Habilitar realtime para status de partidas
ALTER PUBLICATION supabase_realtime ADD TABLE matches;

-- Habilitar realtime para participantes de salas
ALTER PUBLICATION supabase_realtime ADD TABLE room_participants;
```

### 8. Configurar Backup e Monitoramento

#### 8.1 Backup Automático
No Dashboard do Supabase:
1. Vá para **Settings > Database**
2. Configure **Point-in-time Recovery (PITR)**
3. Defina retenção de 7 dias (ou mais para produção)

#### 8.2 Monitoramento
```sql
-- Criar view para métricas importantes
CREATE VIEW admin_metrics AS
SELECT 
  (SELECT COUNT(*) FROM users WHERE status = 'active') as active_users,
  (SELECT COUNT(*) FROM matches WHERE status = 'in_progress') as active_matches,
  (SELECT COUNT(*) FROM game_rooms WHERE status = 'waiting') as waiting_rooms,
  (SELECT SUM(balance) FROM wallets) as total_balance,
  (SELECT COUNT(*) FROM transactions WHERE created_at > NOW() - INTERVAL '24 hours') as transactions_24h,
  (SELECT COUNT(*) FROM notifications WHERE created_at > NOW() - INTERVAL '1 hour' AND is_read = false) as unread_notifications_1h;
```

### 9. Configurar Rate Limiting

#### 9.1 Configurações de Rate Limiting
```sql
-- Configurar rate limiting para APIs críticas
-- Isso deve ser feito no nível da aplicação, mas pode ser monitorado no banco

CREATE TABLE api_rate_limits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  endpoint VARCHAR(100) NOT NULL,
  requests_count INTEGER DEFAULT 1,
  window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índice para performance
CREATE INDEX idx_api_rate_limits_user_endpoint_window 
ON api_rate_limits(user_id, endpoint, window_start);
```

### 10. Configurar Logs e Auditoria

#### 10.1 Configurar Logs Detalhados
```sql
-- Função para log de ações críticas
CREATE OR REPLACE FUNCTION log_critical_action()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO user_activity_logs (
    user_id,
    activity_type,
    description,
    metadata
  ) VALUES (
    COALESCE(NEW.user_id, OLD.user_id),
    TG_OP || '_' || TG_TABLE_NAME,
    'Action performed on ' || TG_TABLE_NAME,
    json_build_object(
      'old_data', to_jsonb(OLD),
      'new_data', to_jsonb(NEW),
      'timestamp', NOW()
    )
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Aplicar trigger em tabelas críticas
CREATE TRIGGER log_wallet_changes
  AFTER INSERT OR UPDATE OR DELETE ON wallets
  FOR EACH ROW
  EXECUTE FUNCTION log_critical_action();

CREATE TRIGGER log_transaction_changes
  AFTER INSERT OR UPDATE OR DELETE ON transactions
  FOR EACH ROW
  EXECUTE FUNCTION log_critical_action();
```

## 🔧 Comandos Úteis

### CLI do Supabase
```bash
# Instalar CLI
npm install -g supabase

# Login
supabase login

# Inicializar projeto local
supabase init

# Linkar com projeto remoto
supabase link --project-ref seu-project-ref

# Fazer push das migrations
supabase db push

# Fazer pull do schema remoto
supabase db pull

# Executar localmente
supabase start

# Parar ambiente local
supabase stop

# Ver logs
supabase functions logs analyze-match-result

# Deploy de edge functions
supabase functions deploy analyze-match-result
```

### Monitoramento
```sql
-- Ver conexões ativas
SELECT count(*) FROM pg_stat_activity;

-- Ver queries lentas
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Ver tamanho das tabelas
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 🚨 Checklist de Produção

- [ ] Configurar domínio personalizado
- [ ] Habilitar SSL/TLS
- [ ] Configurar backup automático
- [ ] Configurar monitoramento e alertas
- [ ] Testar disaster recovery
- [ ] Configurar rate limiting
- [ ] Revisar políticas de segurança
- [ ] Configurar logs de auditoria
- [ ] Testar edge functions
- [ ] Configurar CDN para assets
- [ ] Revisar configurações de CORS
- [ ] Configurar webhooks de pagamento
- [ ] Testar integração com OpenAI
- [ ] Configurar notificações por email
- [ ] Revisar limites de storage

import React from 'react';
import { Trophy, ArrowLeft, RotateCcw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Player {
  id: string;
  name: string;
  avatar: string;
  score: number;
  position: number;
}

interface GameEndScreenProps {
  score: number;
  gameId: string;
  position: number;
  topPlayers: Player[];
  onPlayAgain: () => void;
}

export default function GameEndScreen({
  score,
  gameId,
  position,
  topPlayers,
  onPlayAgain
}: GameEndScreenProps) {
  const navigate = useNavigate();

  // Determinar se o jogador está entre os 3 primeiros no ranking do torneio
  const isTopThree = position <= 3;

  // Prêmio baseado na posição (apenas para exibição se estiver no top 3)
  const getPrizeAmount = () => {
    switch (position) {
      case 1:
        return 100;
      case 2:
        return 60;
      case 3:
        return 40;
      default:
        return 0;
    }
  };

  // Encontrar o jogador atual nos top players
  const currentPlayer = topPlayers.find(player => player.name === 'Você');
  // Verificar se o jogador está realmente entre os 3 primeiros no ranking do torneio
  const isActuallyTopThree = currentPlayer && currentPlayer.position <= 3;

  return (
    <div className="fixed inset-0 bg-gray-900/95 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-xl max-w-md w-full p-6 space-y-6">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <Trophy className="w-16 h-16 text-yellow-400" />
          </div>
          <h2 className="text-2xl font-bold text-white mb-1">Fim de Jogo!</h2>
          <p className="text-gray-400">
            Você ficou em {position}º lugar
          </p>
        </div>

        {/* Player Rankings */}
        <div className="space-y-2">
          {topPlayers.map((player) => (
            <div
              key={player.id}
              className={`flex items-center justify-between p-3 rounded-lg ${
                player.name === 'Você'
                  ? 'bg-gray-700/70 border border-yellow-400/30'
                  : 'bg-gray-700/40'
              }`}
            >
              <div className="flex items-center gap-3">
                <div className="relative">
                  <img
                    src={player.avatar}
                    alt={player.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  {player.position <= 3 && (
                    <div className="absolute -top-1 -right-1 w-5 h-5 flex items-center justify-center">
                      {player.position === 1 && (
                        <span className="text-yellow-400 text-lg">👑</span>
                      )}
                    </div>
                  )}
                </div>
                <div>
                  <p className={`font-semibold ${player.name === 'Você' ? 'text-yellow-400' : 'text-white'}`}>
                    {player.name}
                  </p>
                  <p className="text-gray-400 text-sm">
                    {player.position}º lugar
                  </p>
                </div>
              </div>
              <p className="text-white font-bold">
                {player.score} pts
              </p>
            </div>
          ))}
        </div>

        {/* Prize (only shown if in top 3 in the tournament) */}
        {isActuallyTopThree && (
          <div className="bg-gray-700/50 rounded-lg p-4 border border-yellow-400/20">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Trophy className="w-5 h-5 text-yellow-400" />
                <p className="text-white font-semibold">Prêmio: R$ {getPrizeAmount().toFixed(2)}</p>
              </div>
            </div>
            <p className="text-gray-400 text-sm">
              Parabéns! Você está entre os 3 primeiros no ranking do torneio de hoje e ganhará o prêmio quando o torneio terminar à meia-noite!
            </p>

            {/* Botão de Resgatar Prêmio - Apenas para o primeiro colocado */}
            {position === 1 && (
              <button
                onClick={() => navigate(`/games/${gameId}/claim-prize`)}
                className="w-full mt-3 flex items-center justify-center gap-2 bg-yellow-400 text-black py-3 px-4 rounded-lg hover:bg-yellow-500 transition-colors font-bold"
              >
                <Trophy className="w-5 h-5" />
                Resgatar Prêmio
              </button>
            )}
          </div>
        )}

        {/* Action Buttons - Mostrar apenas quando não é o primeiro colocado ou não está no top 3 */}
        {(!isActuallyTopThree || position !== 1) && (
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => navigate(`/games/${gameId}`)}
              className="flex items-center justify-center gap-2 bg-gray-700 text-white py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              Sair
            </button>
            <button
              onClick={onPlayAgain}
              className="flex items-center justify-center gap-2 bg-gradient-to-r from-rose-400 to-pink-400 text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity"
            >
              <RotateCcw className="w-5 h-5" />
              Jogar Novamente
            </button>
          </div>
        )}

        {/* Botão de Jogar Novamente - Apenas para o primeiro colocado no top 3 */}
        {isActuallyTopThree && position === 1 && (
          <button
            onClick={onPlayAgain}
            className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-rose-400 to-pink-400 text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity"
          >
            <RotateCcw className="w-5 h-5" />
            Jogar Novamente
          </button>
        )}
      </div>
    </div>
  );
}

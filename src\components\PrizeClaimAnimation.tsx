import React, { useEffect, useState } from 'react';
import { Coins } from 'lucide-react';

interface PrizeClaimAnimationProps {
  amount: number;
  onComplete: () => void;
}

export default function PrizeClaimAnimation({ amount, onComplete }: PrizeClaimAnimationProps) {
  const [show, setShow] = useState(true);
  const [count, setCount] = useState(0);

  useEffect(() => {
    // Animação de contagem
    const duration = 1500; // 1.5 segundos
    const steps = 60; // 60 frames
    const increment = amount / steps;
    let currentStep = 0;

    const interval = setInterval(() => {
      if (currentStep < steps) {
        setCount(prev => Math.min(amount, prev + increment));
        currentStep++;
      } else {
        clearInterval(interval);
        setTimeout(() => {
          setShow(false);
          onComplete();
        }, 1000);
      }
    }, duration / steps);

    return () => clearInterval(interval);
  }, [amount, onComplete]);

  if (!show) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[100] pointer-events-none">
      <div className="animate-[slideUp_2.5s_ease-out] opacity-0">
        <div className="bg-yellow-400 text-black font-bold px-6 py-4 rounded-xl flex items-center gap-3 shadow-lg">
          <Coins className="w-6 h-6" />
          <span className="text-2xl">+R$ {count.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}
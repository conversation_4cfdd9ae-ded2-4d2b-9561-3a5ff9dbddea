import React from 'react';
import { Link } from 'react-router-dom';
import { LogIn, UserPlus, User, Gamepad2 } from 'lucide-react';
import { useUser } from '../contexts/UserContext';

interface WelcomeScreenProps {
  onGuestLogin: () => void;
}

export default function WelcomeScreen({ onGuestLogin }: WelcomeScreenProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          {/* Logo */}
          <div className="w-20 h-20 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
            <Gamepad2 className="w-10 h-10 text-white" />
          </div>
          
          {/* Título */}
          <h1 className="text-4xl font-bold text-white mb-2">
            Bem-vindo ao <span className="bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">Playstrike</span>
          </h1>
          <p className="text-gray-400 text-lg">
            A plataforma definitiva para gamers competitivos
          </p>
        </div>

        <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl shadow-xl p-8 space-y-4">
          {/* Login */}
          <Link
            to="/login"
            className="w-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-4 px-6 rounded-lg hover:opacity-90 transition-opacity shadow-lg flex items-center justify-center gap-3"
          >
            <LogIn className="w-5 h-5" />
            Entrar na minha conta
          </Link>

          {/* Registro */}
          <Link
            to="/register"
            className="w-full bg-gray-700/50 border border-gray-600 text-white font-medium py-4 px-6 rounded-lg hover:bg-gray-600/50 transition-colors flex items-center justify-center gap-3"
          >
            <UserPlus className="w-5 h-5" />
            Criar nova conta
          </Link>

          {/* Divisor */}
          <div className="flex items-center gap-4 my-6">
            <div className="flex-1 h-px bg-gray-600"></div>
            <span className="text-gray-400 text-sm">ou</span>
            <div className="flex-1 h-px bg-gray-600"></div>
          </div>

          {/* Entrar como convidado */}
          <button
            onClick={onGuestLogin}
            className="w-full bg-transparent border border-gray-600 text-gray-300 font-medium py-4 px-6 rounded-lg hover:bg-gray-700/30 transition-colors flex items-center justify-center gap-3"
          >
            <User className="w-5 h-5" />
            Continuar como convidado
          </button>

          {/* Informações sobre convidado */}
          <p className="text-gray-500 text-sm text-center mt-4">
            Como convidado, você pode explorar a plataforma, mas algumas funcionalidades serão limitadas.
          </p>
        </div>

        {/* Features */}
        <div className="mt-8 grid grid-cols-2 gap-4 text-center">
          <div className="bg-gray-800/30 backdrop-blur-sm border border-white/5 rounded-lg p-4">
            <div className="text-indigo-400 font-bold text-lg">1000+</div>
            <div className="text-gray-400 text-sm">Jogadores ativos</div>
          </div>
          <div className="bg-gray-800/30 backdrop-blur-sm border border-white/5 rounded-lg p-4">
            <div className="text-purple-400 font-bold text-lg">R$ 50k+</div>
            <div className="text-gray-400 text-sm">Em prêmios</div>
          </div>
        </div>
      </div>
    </div>
  );
}

import React, { useState } from 'react';
import { 
  X, 
  Trophy, 
  Users, 
  Calendar, 
  Clock, 
  Globe, 
  Lock, 
  Wallet,
  Info,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface CreateTournamentModalProps {
  onClose: () => void;
  onCreate: (tournamentData: {
    title: string;
    game: string;
    format: 'elimination' | 'points';
    startDate: string;
    endDate?: string;
    registrationDeadline: string;
    maxParticipants: number;
    entryFee: number;
    prizeDistribution: {
      first: number;
      second: number;
      third: number;
      other?: number;
    };
    description: string;
    rules: string;
    isPublic: boolean;
  }) => void;
  games: Array<{
    id: string;
    title: string;
  }>;
}

export default function CreateTournamentModal({ onClose, onCreate, games }: CreateTournamentModalProps) {
  const [step, setStep] = useState<'basic' | 'details' | 'prizes' | 'rules'>('basic');
  
  // Basic information
  const [title, setTitle] = useState('');
  const [selectedGame, setSelectedGame] = useState(games[0]?.id || '');
  const [format, setFormat] = useState<'elimination' | 'points'>('elimination');
  const [isPublic, setIsPublic] = useState(true);
  
  // Tournament details
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [registrationDeadline, setRegistrationDeadline] = useState('');
  const [maxParticipants, setMaxParticipants] = useState(32);
  const [entryFee, setEntryFee] = useState(25);
  
  // Prize distribution
  const [prizeDistribution, setPrizeDistribution] = useState({
    first: 60,
    second: 30,
    third: 10,
    other: 0
  });
  
  // Rules and description
  const [description, setDescription] = useState('');
  const [rules, setRules] = useState('');

  // Calculate total prize pool
  const calculatePrizePool = () => {
    return entryFee * maxParticipants;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form data
    if (step !== 'rules') {
      setStep('rules');
      return;
    }
    
    // Create tournament
    onCreate({
      title,
      game: selectedGame,
      format,
      startDate,
      endDate: format === 'points' ? endDate : undefined,
      registrationDeadline,
      maxParticipants,
      entryFee,
      prizeDistribution,
      description,
      rules,
      isPublic
    });
  };

  const renderStepIndicator = () => {
    const steps = ['basic', 'details', 'prizes', 'rules'];
    const currentIndex = steps.indexOf(step);
    
    return (
      <div className="flex items-center justify-center gap-2 mb-6">
        {steps.map((s, index) => (
          <React.Fragment key={s}>
            <div 
              className={`w-2.5 h-2.5 rounded-full ${
                index <= currentIndex 
                  ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500' 
                  : 'bg-gray-600'
              }`}
            />
            {index < steps.length - 1 && (
              <div className={`w-8 h-0.5 ${
                index < currentIndex ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500' : 'bg-gray-600'
              }`} />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl w-full max-w-2xl shadow-xl">
        <div className="p-4 border-b border-gray-700 flex items-center justify-between">
          <h3 className="text-white text-lg font-bold">Criar Novo Torneio</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {renderStepIndicator()}
          
          {step === 'basic' && (
            <div className="space-y-6">
              <h4 className="text-white font-bold text-lg">Informações Básicas</h4>
              
              {/* Tournament Name */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Nome do Torneio</label>
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                  placeholder="Digite o nome do torneio"
                  required
                />
              </div>
              
              {/* Game Selection */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Jogo</label>
                <select
                  value={selectedGame}
                  onChange={(e) => setSelectedGame(e.target.value)}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                  required
                >
                  {games.map(game => (
                    <option key={game.id} value={game.id}>{game.title}</option>
                  ))}
                </select>
              </div>
              
              {/* Tournament Format */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Formato do Torneio</label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    onClick={() => setFormat('elimination')}
                    className={`p-4 rounded-lg flex flex-col items-center gap-2 ${
                      format === 'elimination'
                        ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white'
                        : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/70'
                    }`}
                  >
                    <Trophy className="w-6 h-6" />
                    <span className="font-semibold">Eliminatória</span>
                    <p className="text-xs text-center">Chaveamento com eliminação direta</p>
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormat('points')}
                    className={`p-4 rounded-lg flex flex-col items-center gap-2 ${
                      format === 'points'
                        ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white'
                        : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/70'
                    }`}
                  >
                    <Users className="w-6 h-6" />
                    <span className="font-semibold">Pontuação</span>
                    <p className="text-xs text-center">Classificação por pontos acumulados</p>
                  </button>
                </div>
              </div>
              
              {/* Visibility */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Visibilidade</label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    onClick={() => setIsPublic(true)}
                    className={`p-4 rounded-lg flex items-center justify-center gap-2 ${
                      isPublic
                        ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white'
                        : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/70'
                    }`}
                  >
                    <Globe className="w-5 h-5" />
                    <span>Público</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsPublic(false)}
                    className={`p-4 rounded-lg flex items-center justify-center gap-2 ${
                      !isPublic
                        ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white'
                        : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/70'
                    }`}
                  >
                    <Lock className="w-5 h-5" />
                    <span>Privado</span>
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {step === 'details' && (
            <div className="space-y-6">
              <h4 className="text-white font-bold text-lg">Detalhes do Torneio</h4>
              
              {/* Dates */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-400 text-sm mb-2">Data de Início</label>
                  <input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                    required
                  />
                </div>
                
                {format === 'points' && (
                  <div>
                    <label className="block text-gray-400 text-sm mb-2">Data de Término</label>
                    <input
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                      required
                    />
                  </div>
                )}
              </div>
              
              <div>
                <label className="block text-gray-400 text-sm mb-2">Prazo de Inscrição</label>
                <input
                  type="date"
                  value={registrationDeadline}
                  onChange={(e) => setRegistrationDeadline(e.target.value)}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                  required
                />
              </div>
              
              {/* Participants */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Número Máximo de Participantes</label>
                <select
                  value={maxParticipants}
                  onChange={(e) => setMaxParticipants(Number(e.target.value))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                >
                  <option value={8}>8 participantes</option>
                  <option value={16}>16 participantes</option>
                  <option value={32}>32 participantes</option>
                  <option value={64}>64 participantes</option>
                  <option value={128}>128 participantes</option>
                </select>
              </div>
              
              {/* Entry Fee */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Taxa de Inscrição (R$)</label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">R$</span>
                  <input
                    type="number"
                    min="0"
                    step="5"
                    value={entryFee}
                    onChange={(e) => setEntryFee(Number(e.target.value))}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                  />
                </div>
                <p className="text-gray-400 text-xs mt-1">
                  Prêmio total estimado: R$ {calculatePrizePool().toLocaleString()}
                </p>
              </div>
            </div>
          )}
          
          {step === 'prizes' && (
            <div className="space-y-6">
              <h4 className="text-white font-bold text-lg">Distribuição de Prêmios</h4>
              
              <div className="bg-gray-700/30 backdrop-blur-sm border border-white/5 rounded-lg p-4 mb-4">
                <div className="flex items-start gap-2">
                  <Info className="w-5 h-5 text-indigo-400 flex-shrink-0 mt-0.5" />
                  <p className="text-gray-300 text-sm">
                    Defina como o prêmio total de <span className="text-white font-semibold">R$ {calculatePrizePool().toLocaleString()}</span> será distribuído entre os vencedores.
                  </p>
                </div>
              </div>
              
              {/* First Place */}
              <div>
                <label className="flex justify-between text-gray-400 text-sm mb-2">
                  <span>1º Lugar</span>
                  <span className="text-white">{prizeDistribution.first}%</span>
                </label>
                <input
                  type="range"
                  min="40"
                  max="100"
                  value={prizeDistribution.first}
                  onChange={(e) => {
                    const first = Number(e.target.value);
                    const remaining = 100 - first;
                    const ratio = remaining / (prizeDistribution.second + prizeDistribution.third);
                    
                    setPrizeDistribution({
                      first,
                      second: Math.round(prizeDistribution.second * ratio),
                      third: Math.round(prizeDistribution.third * ratio),
                      other: 0
                    });
                  }}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <p className="text-indigo-400 text-sm mt-1">
                  R$ {Math.round(calculatePrizePool() * prizeDistribution.first / 100).toLocaleString()}
                </p>
              </div>
              
              {/* Second Place */}
              <div>
                <label className="flex justify-between text-gray-400 text-sm mb-2">
                  <span>2º Lugar</span>
                  <span className="text-white">{prizeDistribution.second}%</span>
                </label>
                <input
                  type="range"
                  min="0"
                  max={100 - prizeDistribution.first - prizeDistribution.third}
                  value={prizeDistribution.second}
                  onChange={(e) => {
                    const second = Number(e.target.value);
                    setPrizeDistribution({
                      ...prizeDistribution,
                      second,
                      third: 100 - prizeDistribution.first - second
                    });
                  }}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <p className="text-indigo-400 text-sm mt-1">
                  R$ {Math.round(calculatePrizePool() * prizeDistribution.second / 100).toLocaleString()}
                </p>
              </div>
              
              {/* Third Place */}
              <div>
                <label className="flex justify-between text-gray-400 text-sm mb-2">
                  <span>3º Lugar</span>
                  <span className="text-white">{prizeDistribution.third}%</span>
                </label>
                <input
                  type="range"
                  min="0"
                  max={100 - prizeDistribution.first - prizeDistribution.second}
                  value={prizeDistribution.third}
                  onChange={(e) => {
                    const third = Number(e.target.value);
                    setPrizeDistribution({
                      ...prizeDistribution,
                      third,
                      second: 100 - prizeDistribution.first - third
                    });
                  }}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <p className="text-indigo-400 text-sm mt-1">
                  R$ {Math.round(calculatePrizePool() * prizeDistribution.third / 100).toLocaleString()}
                </p>
              </div>
            </div>
          )}
          
          {step === 'rules' && (
            <div className="space-y-6">
              <h4 className="text-white font-bold text-lg">Regras e Descrição</h4>
              
              {/* Description */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Descrição do Torneio</label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 min-h-[100px]"
                  placeholder="Descreva o torneio, seus objetivos e informações gerais..."
                  required
                />
              </div>
              
              {/* Rules */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Regras do Torneio</label>
                <textarea
                  value={rules}
                  onChange={(e) => setRules(e.target.value)}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 min-h-[150px]"
                  placeholder="Defina as regras específicas do torneio, formato de partidas, etc..."
                  required
                />
              </div>
            </div>
          )}
          
          {/* Navigation Buttons */}
          <div className="flex gap-3 mt-8">
            {step !== 'basic' && (
              <button
                type="button"
                onClick={() => {
                  if (step === 'details') setStep('basic');
                  if (step === 'prizes') setStep('details');
                  if (step === 'rules') setStep('prizes');
                }}
                className="flex-1 bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white font-bold py-3 rounded-lg hover:bg-gray-700/70 flex items-center justify-center gap-2"
              >
                <ChevronLeft className="w-5 h-5" />
                Voltar
              </button>
            )}
            
            {step !== 'rules' ? (
              <button
                type="button"
                onClick={() => {
                  if (step === 'basic') setStep('details');
                  if (step === 'details') setStep('prizes');
                  if (step === 'prizes') setStep('rules');
                }}
                className="flex-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-3 rounded-lg hover:opacity-90 flex items-center justify-center gap-2"
              >
                Próximo
                <ChevronRight className="w-5 h-5" />
              </button>
            ) : (
              <button
                type="submit"
                className="flex-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-3 rounded-lg hover:opacity-90"
              >
                Criar Torneio
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

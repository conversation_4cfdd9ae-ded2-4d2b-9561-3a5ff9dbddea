import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { ApiResponse } from '../types/api';
import config from '../config/environment';
import { logger } from '../utils/logger';

// Custom key generator that uses user ID if available, otherwise IP
const keyGenerator = (req: Request): string => {
  const user = (req as any).user;
  return user ? `user:${user.id}` : `ip:${req.ip}`;
};

// Custom handler for rate limit exceeded
const rateLimitHandler = (req: Request, res: Response): void => {
  const user = (req as any).user;
  const identifier = user ? `user ${user.username} (${user.id})` : `IP ${req.ip}`;

  logger.warn(`Rate limit exceeded for ${identifier} on ${req.method} ${req.path}`);

  const response: ApiResponse = {
    success: false,
    error: 'Too many requests. Please try again later.',
    meta: {
      retryAfter: Math.ceil(config.RATE_LIMIT_WINDOW_MS / 1000)
    }
  };

  res.status(429).json(response);
};

// Standard rate limiter for general API endpoints
export const standardRateLimit = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS, // 15 minutes
  max: config.NODE_ENV === 'development' ? config.RATE_LIMIT_MAX_REQUESTS * 10 : config.RATE_LIMIT_MAX_REQUESTS, // 1000 in dev, 100 in prod
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests from this IP, please try again later.'
});

// Strict rate limiter for authentication endpoints
export const authRateLimit = rateLimit({
  windowMs: config.AUTH_RATE_LIMIT_WINDOW_MS || 15 * 60 * 1000, // 15 minutes
  max: config.NODE_ENV === 'development' ? config.AUTH_RATE_LIMIT_MAX : 10, // Configurable in dev, 10 in production
  keyGenerator: (req: Request) => `auth:${req.ip}`,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
  message: 'Too many authentication attempts, please try again later.'
});

// Lenient rate limiter for public endpoints
export const publicRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 requests per window
  keyGenerator: (req: Request) => `public:${req.ip}`,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests, please try again later.'
});

// Strict rate limiter for file upload endpoints
export const uploadRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 uploads per hour
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many file uploads, please try again later.'
});

// Rate limiter for transaction endpoints
export const transactionRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 transactions per hour
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many transaction requests, please try again later.'
});

// Rate limiter for match creation
export const matchCreationRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // 10 matches per 5 minutes
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many match creation requests, please try again later.'
});

// Rate limiter for friend requests
export const friendRequestRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 friend requests per hour
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many friend requests, please try again later.'
});

// Rate limiter for chat messages
export const chatRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 messages per minute
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many messages, please slow down.'
});

// Rate limiter for search endpoints
export const searchRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // 60 searches per minute
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many search requests, please try again later.'
});

// Rate limiter for tournament creation
export const tournamentCreationRateLimit = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 5, // 5 tournaments per day
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many tournament creation requests, please try again tomorrow.'
});

// Rate limiter for club creation
export const clubCreationRateLimit = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 3, // 3 clubs per day
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many club creation requests, please try again tomorrow.'
});

// Rate limiter for reports
export const reportRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 reports per hour
  keyGenerator,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many reports, please try again later.'
});

// Rate limiter for password reset
export const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 password reset attempts per hour
  keyGenerator: (req: Request) => `password-reset:${req.ip}`,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many password reset attempts, please try again later.'
});

// Rate limiter for email verification
export const emailVerificationRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // 5 verification emails per hour
  keyGenerator: (req: Request) => `email-verification:${req.ip}`,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many verification email requests, please try again later.'
});

// Dynamic rate limiter based on user tier/role
export const dynamicRateLimit = (baseMax: number = 100) => {
  return rateLimit({
    windowMs: config.RATE_LIMIT_WINDOW_MS,
    max: (req: Request) => {
      const user = (req as any).user;

      if (!user) {
        return Math.floor(baseMax * 0.5); // 50% for unauthenticated users
      }

      // Increase limits based on user role
      switch (user.role) {
        case 'admin':
          return baseMax * 10; // 10x for admins
        case 'moderator':
          return baseMax * 5; // 5x for moderators
        case 'premium':
          return baseMax * 2; // 2x for premium users
        default:
          return baseMax; // Standard for regular users
      }
    },
    keyGenerator,
    handler: rateLimitHandler,
    standardHeaders: true,
    legacyHeaders: false
  });
};

// Rate limiter for webhook endpoints
export const webhookRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 1000, // 1000 webhook calls per minute
  keyGenerator: (req: Request) => `webhook:${req.ip}`,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Webhook rate limit exceeded.'
});

// Custom rate limiter for specific endpoints
export const createCustomRateLimit = (options: {
  windowMs?: number;
  max?: number;
  keyPrefix?: string;
  skipSuccessfulRequests?: boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs || config.RATE_LIMIT_WINDOW_MS,
    max: options.max || config.RATE_LIMIT_MAX_REQUESTS,
    keyGenerator: (req: Request) => {
      const user = (req as any).user;
      const prefix = options.keyPrefix || 'custom';
      return user ? `${prefix}:user:${user.id}` : `${prefix}:ip:${req.ip}`;
    },
    handler: rateLimitHandler,
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: options.skipSuccessfulRequests || false
  });
};

// Middleware to add rate limit info to response headers
export const addRateLimitInfo = (req: Request, res: Response, next: any) => {
  const originalSend = res.send;

  res.send = function(data: any) {
    // Add custom rate limit headers if they exist
    const rateLimitInfo = (req as any).rateLimit;
    if (rateLimitInfo) {
      res.set({
        'X-RateLimit-Limit': rateLimitInfo.limit?.toString(),
        'X-RateLimit-Remaining': rateLimitInfo.remaining?.toString(),
        'X-RateLimit-Reset': new Date(rateLimitInfo.resetTime).toISOString()
      });
    }

    return originalSend.call(this, data);
  };

  next();
};

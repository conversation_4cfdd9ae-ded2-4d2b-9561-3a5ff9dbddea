const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

// Create admin client that bypasses RLS
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  },
  db: {
    schema: 'public'
  }
});

async function bypassRlsUpdate() {
  try {
    const email = '<EMAIL>';
    const password = 'Milhao123#';
    const userId = 'dfd3cd04-6f0f-4345-862b-5c11188dd7e2';
    
    console.log(`🔧 Setting password for user: ${email} (bypassing RLS)`);
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);
    console.log('🔐 Password hashed successfully');
    
    // Use service role to bypass RLS
    console.log('\n📝 Updating with service role (bypassing RLS)...');
    
    // First, let's check current user data
    const { data: currentUser, error: currentError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (currentError) {
      console.error('❌ Cannot read current user:', currentError);
      return;
    }
    
    console.log('Current user data:');
    console.log('- Email:', currentUser.email);
    console.log('- Username:', currentUser.username);
    console.log('- Status:', currentUser.status);
    console.log('- Password Hash:', currentUser.password_hash ? 'EXISTS' : 'NULL');
    
    // Try the update with explicit service role
    const { data, error, count } = await supabase
      .from('users')
      .update({ 
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select('id, email, username');
    
    console.log('\nUpdate result:');
    console.log('- Data:', data);
    console.log('- Error:', error);
    console.log('- Count:', count);
    
    if (error) {
      console.error('❌ Failed to set password:', error);
      
      // Try using a raw SQL approach if available
      console.log('\n🔄 Trying alternative approach...');
      
      // Let's try to create a simple function to update the password
      const updateSql = `
        UPDATE users 
        SET password_hash = '${hashedPassword}', updated_at = NOW() 
        WHERE id = '${userId}';
      `;
      
      console.log('SQL to execute:', updateSql);
      
    } else if (data && data.length > 0) {
      console.log('✅ Update successful:', data[0]);
    } else {
      console.log('⚠️ Update returned no data - might be RLS issue');
    }
    
    // Verify the password was set
    console.log('\n🔍 Verifying password was set...');
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('id, email, username, password_hash')
      .eq('id', userId)
      .single();
    
    if (verifyError) {
      console.error('❌ Failed to verify:', verifyError);
      return;
    }
    
    console.log('User data after update:');
    console.log('- Password Hash exists:', !!verifyUser.password_hash);
    
    if (verifyUser.password_hash) {
      const isValid = await bcrypt.compare(password, verifyUser.password_hash);
      console.log('✅ Password verification:', isValid ? 'SUCCESS' : 'FAILED');
    } else {
      console.log('❌ Password hash still NULL - RLS might be blocking updates');
      console.log('\n💡 Suggestion: You may need to:');
      console.log('1. Disable RLS temporarily on users table');
      console.log('2. Or add a policy that allows service role to update');
      console.log('3. Or manually run SQL in Supabase dashboard');
    }
    
  } catch (error) {
    console.error('❌ Error setting password:', error);
  }
}

bypassRlsUpdate();

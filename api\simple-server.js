const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    services: {
      database: {
        status: 'healthy',
        responseTime: 50
      }
    }
  });
});

// API documentation endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'Playstrike API',
    version: '1.0.0',
    description: 'Gaming platform API with tournaments, matches, and social features',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      wallet: '/api/wallet',
      games: '/api/games',
      matches: '/api/matches',
      tournaments: '/api/tournaments',
      social: '/api/social',
      affiliate: '/api/affiliate',
      notifications: '/api/notifications',
      admin: '/api/admin',
      webhooks: '/api/webhooks'
    },
    documentation: 'https://docs.playstrike.com',
    support: '<EMAIL>'
  });
});

// Mock auth endpoints
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Mock validation
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      error: 'Email e senha são obrigatórios'
    });
  }

  // Mock successful login
  res.json({
    success: true,
    data: {
      user: {
        id: 'user_123',
        username: 'testuser',
        email: email,
        display_name: 'Usuário Teste',
        avatar_url: 'https://i.pravatar.cc/150?img=1',
        status: 'active',
        created_at: new Date().toISOString()
      },
      tokens: {
        access_token: 'mock_access_token_123',
        refresh_token: 'mock_refresh_token_123',
        expires_in: 3600
      }
    }
  });
});

app.post('/api/auth/register', (req, res) => {
  const { username, email, password, display_name } = req.body;
  
  // Mock validation
  if (!username || !email || !password) {
    return res.status(400).json({
      success: false,
      error: 'Username, email e senha são obrigatórios'
    });
  }

  // Mock successful registration
  res.json({
    success: true,
    data: {
      user: {
        id: 'user_new_123',
        username: username,
        email: email,
        display_name: display_name || username,
        avatar_url: 'https://i.pravatar.cc/150?img=2',
        status: 'active',
        created_at: new Date().toISOString()
      },
      tokens: {
        access_token: 'mock_access_token_new_123',
        refresh_token: 'mock_refresh_token_new_123',
        expires_in: 3600
      }
    }
  });
});

app.get('/api/auth/profile', (req, res) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: 'Token de acesso requerido'
    });
  }

  // Mock user profile
  res.json({
    success: true,
    data: {
      id: 'user_123',
      username: 'testuser',
      email: '<EMAIL>',
      display_name: 'Usuário Teste',
      avatar_url: 'https://i.pravatar.cc/150?img=1',
      status: 'active',
      country: 'BR',
      created_at: new Date().toISOString(),
      user_stats: {
        total_matches: 25,
        total_wins: 18,
        total_losses: 7,
        total_draws: 0,
        win_rate: 72,
        total_earnings: 1250.50,
        current_streak: 3,
        best_streak: 8,
        ranking_points: 1850,
        level: 12,
        experience_points: 2400,
        achievements_count: 15
      }
    }
  });
});

app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logout realizado com sucesso'
  });
});

// Mock tournaments endpoint
app.get('/api/tournaments', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 'tournament_1',
        title: 'CS2 Pro League - Season 1',
        game: 'Counter-Strike 2',
        status: 'in_progress',
        participants: 1024,
        maxParticipants: 1024,
        entryFee: 50,
        prizePool: 10000,
        startDate: '2024-04-01',
        endDate: '2024-05-20'
      }
    ]
  });
});

app.get('/api/tournaments/:id', (req, res) => {
  const { id } = req.params;
  
  res.json({
    success: true,
    data: {
      id: id,
      title: 'CS2 Pro League - Season 1',
      game: 'Counter-Strike 2',
      status: 'in_progress',
      participants: 1024,
      maxParticipants: 1024,
      entryFee: 50,
      prizePool: 10000,
      startDate: '2024-04-01',
      endDate: '2024-05-20',
      description: 'Torneio mock para testes'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Error:', error);
  res.status(error.status || 500).json({
    success: false,
    error: error.message || 'Internal server error'
  });
});

// Start server
app.listen(PORT, 'localhost', () => {
  console.log(`🚀 Mock API Server running on http://localhost:${PORT}`);
  console.log(`📊 Environment: development`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log(`📚 Health Check: http://localhost:${PORT}/health`);
});

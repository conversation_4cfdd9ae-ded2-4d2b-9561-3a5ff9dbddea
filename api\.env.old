# Server Configuration
NODE_ENV=development
PORT=3002
HOST=localhost

# Database Configuration (Supabase) - CONFIGURAÇÃO REAL
SUPABASE_URL=https://bnibkehmzkbvkitchrfq.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJuaWJrZWhtemtidmtpdGNocmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzMjYyMDYsImV4cCI6MjA2NDkwMjIwNn0.fyb9oOyTFZ5iT83cUBY6yRNXTaQA3UJ9ghXwMk2LWV4
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJuaWJrZWhtemtidmtpdGNocmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTMyNjIwNiwiZXhwIjoyMDY0OTAyMjA2fQ.IoeFZt2ytl5IirbddwcWLnYXXzX2Z835bvBLVQY6Hp0

# Authentication
JWT_SECRET=playstrike-super-secret-jwt-key-for-development-only
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_ORGANIZATION_ID=org-I7nn0ydPOGYLfmMLLgDPhDVp
OPENAI_MODEL=gpt-4-vision-preview

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Game Configuration
GAME_ROOM_TIMEOUT=1800
MATCHMAKING_TIMEOUT=300
MATCH_AUTO_CONFIRM_TIME=300

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX=1000
AUTH_RATE_LIMIT_WINDOW_MS=900000

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=playstrike-session-secret-for-development

# Feature Flags
ENABLE_TOURNAMENTS=true
ENABLE_AFFILIATE_PROGRAM=true
ENABLE_CLUBS=true
ENABLE_STREAMS=true
ENABLE_CHAT=true

const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api/openai',
    createProxyMiddleware({
      target: 'https://api.openai.com',
      changeOrigin: true,
      pathRewrite: {
        '^/api/openai': '/v1',
      },
      onProxyReq: (proxyReq, req, res) => {
        console.log('Proxying request to OpenAI API:', req.method, req.path);
      },
      onProxyRes: (proxyRes, req, res) => {
        console.log('Received response from OpenAI API:', proxyRes.statusCode);
      },
      onError: (err, req, res) => {
        console.error('Proxy error:', err);
      }
    })
  );
};

// Serviço para integração com a API da OpenAI
import { OPENAI_API_KEY, OPENAI_API_URL, OPENAI_VISION_MODEL, OPENAI_ORGANIZATION_ID } from '../config/api';

/**
 * Converte um arquivo para base64
 * @param file Arquivo a ser convertido
 * @returns String base64 do arquivo no formato correto para a API da OpenAI
 */
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      // O resultado já vem no formato "data:image/jpeg;base64,..."
      // que é exatamente o que a OpenAI espera
      const base64String = reader.result as string;
      console.log('Imagem convertida para base64. Tamanho:', Math.round(base64String.length / 1024), 'KB');
      resolve(base64String);
    };
    reader.onerror = error => reject(error);
  });
};

/**
 * Gera um resultado simulado para testes ou quando a API falha
 * @param player1Name Nome do jogador 1
 * @param player2Name Nome do jogador 2
 * @returns Resultado simulado com vencedor e placar
 */
const getMockResult = (player1Name: string, player2Name: string) => {
  // Gerar pontuações mais realistas para jogos
  const generateScore = () => {
    // Gerar pontuações típicas de jogos como FIFA, etc.
    const baseScores = [
      [1, 0], [2, 0], [2, 1], [3, 1], [3, 2], [4, 2], [4, 3], [5, 3]
    ];

    // Escolher um par de pontuações aleatoriamente
    const [score1, score2] = baseScores[Math.floor(Math.random() * baseScores.length)];

    return { score1, score2 };
  };

  // Determinar o vencedor aleatoriamente
  const player1Wins = Math.random() > 0.5;
  const { score1, score2 } = generateScore();

  const mockResult = {
    winner: player1Wins ? player1Name : player2Name,
    player1Score: player1Wins ? score1 : score2,
    player2Score: player1Wins ? score2 : score1,
    confidence: 0.95 + (Math.random() * 0.05), // Confiança alta (95% a 100%)
    awardPrize: true,
    originalWinner: player1Wins ? player1Name : player2Name,
    isMockResult: true // Indicador de que este é um resultado simulado
  };

  console.log('Resultado simulado gerado:', mockResult);
  return mockResult;
};

/**
 * Extrai informações de resultado de um texto não estruturado
 */
const extractResultFromText = (text: string, player1Name: string, player2Name: string) => {
  const result = {
    winner: player1Name,
    player1Score: 1,
    player2Score: 0,
    confidence: 0.7
  };

  const lowerText = text.toLowerCase();

  // Determinar o vencedor
  if (lowerText.includes('você venceu') ||
      lowerText.includes('vitória') ||
      lowerText.includes('vencedor') ||
      lowerText.includes('ganhou') ||
      lowerText.includes('prêmio') ||
      lowerText.includes('troféu')) {
    result.winner = player1Name;
    result.confidence = 0.95;
  } else if (lowerText.includes('você perdeu') ||
             lowerText.includes('derrota') ||
             lowerText.includes('perdedor') ||
             lowerText.includes('perdeu')) {
    result.winner = player2Name;
    result.confidence = 0.9;
  }

  return result;
};

/**
 * Analisa uma imagem de resultado de jogo e retorna o vencedor e placar
 * @param imageFile Arquivo de imagem do resultado
 * @param player1Name Nome do jogador 1
 * @param player2Name Nome do jogador 2
 * @returns Objeto com o vencedor e placar
 */
export const analyzeGameResult = async (
  imageFile: File,
  player1Name: string,
  player2Name: string
): Promise<{
  winner: string;
  player1Score: number;
  player2Score: number;
  confidence: number;
  awardPrize?: boolean;
  originalWinner?: string;
}> => {
  console.log('=== SERVIÇO OPENAI INICIADO ===');
  console.log('Arquivo recebido:', imageFile.name);
  console.log('Tamanho:', (imageFile.size / 1024).toFixed(2), 'KB');
  console.log('Tipo:', imageFile.type);
  console.log('Player 1:', player1Name);
  console.log('Player 2:', player2Name);

  try {
    // Converter a imagem para base64
    console.log('Convertendo imagem para base64...');
    const base64Image = await fileToBase64(imageFile);
    console.log('Imagem convertida com sucesso!');

    console.log('Enviando imagem para análise pela OpenAI...');

    // A chave da API está configurada no arquivo de configuração
    const apiKey = OPENAI_API_KEY;

    // Log da chave da API (apenas os primeiros 10 caracteres por segurança)
    console.log('Chave da API configurada:', apiKey.substring(0, 10) + '...');

    // Preparar o corpo da requisição
    const requestBody = {
      model: OPENAI_VISION_MODEL,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Analise esta imagem de resultado de jogo entre dois ou mais players.
                     A imagem mostra a tela final do jogo com o resultado da partida.

                     Identifique o vencedor da partida.
                     Se a imagem mostrar um placar, identifique os pontos de cada jogador, e o jogador com mais pontos é o vencedor.

                     Retorne apenas um JSON com os campos:
                     winner (nome do vencedor exatamente como aparece na imagem),
                     player1Score (pontuação do jogador 1, se visível),
                     player2Score (pontuação do jogador 2, se visível),
                     confidence (sua confiança de 0 a 1).`
            },
            {
              type: "image_url",
              image_url: {
                url: base64Image
              }
            }
          ]
        }
      ],
      max_tokens: 300,
      temperature: 0.5,
      response_format: { type: "json_object" }
    };

    console.log('Formato da imagem base64:', base64Image.substring(0, 50) + '...');
    console.log('Modelo utilizado:', OPENAI_VISION_MODEL);
    console.log('Organization ID:', OPENAI_ORGANIZATION_ID);
    console.log('Enviando requisição para OpenAI...');

    // Fazer a chamada real para a API da OpenAI
    const response = await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'Accept': 'application/json',
        'OpenAI-Organization': OPENAI_ORGANIZATION_ID
      },
      body: JSON.stringify(requestBody)
    });

    // Verificar se a resposta foi bem-sucedida
    console.log('Resposta recebida da OpenAI. Status:', response.status);

    if (!response.ok) {
      let errorMessage = `Erro na API da OpenAI: ${response.status} ${response.statusText}`;

      // Log de todos os headers da requisição para debug
      console.log('Headers enviados:', {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey.substring(0, 10)}...`,
        'Accept': 'application/json',
        'OpenAI-Organization': OPENAI_ORGANIZATION_ID
      });

      try {
        const errorData = await response.json();
        console.error('Detalhes do erro da OpenAI:', errorData);

        if (errorData.error) {
          errorMessage += ` - ${errorData.error.message || errorData.error.type || JSON.stringify(errorData.error)}`;

          // Log adicional para erros específicos
          if (errorData.error.type === 'insufficient_quota') {
            console.error('Erro de quota insuficiente. Verifique o plano e detalhes de faturamento da sua conta OpenAI.');
          } else if (errorData.error.type === 'invalid_request_error') {
            console.error('Erro de requisição inválida. Verifique os parâmetros enviados.');
          } else if (errorData.error.type === 'authentication_error') {
            console.error('Erro de autenticação. Verifique sua chave de API e ID da organização.');
          }
        }
      } catch (parseError) {
        console.error('Erro ao analisar resposta de erro:', parseError);
        const errorText = await response.text();
        console.error('Texto da resposta de erro:', errorText);
      }

      // Se for um erro 429 (Too Many Requests), adicionar informação específica
      if (response.status === 429) {
        console.error('Erro 429: Muitas requisições. Verifique os limites de taxa da sua conta OpenAI.');

        // Tentar obter o header Retry-After se disponível
        const retryAfter = response.headers.get('Retry-After');
        if (retryAfter) {
          console.log(`Tente novamente após ${retryAfter} segundos.`);
        }
      }

      throw new Error(errorMessage);
    }

    // Processar a resposta
    const data = await response.json();
    console.log('Resposta da OpenAI recebida com sucesso!');
    console.log('ID da resposta:', data.id);
    console.log('Modelo usado:', data.model);
    console.log('Uso de tokens:', data.usage);

    // Log da resposta completa (limitada para não sobrecarregar o console)
    const responseStr = JSON.stringify(data);
    console.log('Resposta completa:', responseStr.length > 1000 ? responseStr.substring(0, 1000) + '...' : responseStr);

    try {
      // Verificar se a resposta contém conteúdo ou se foi recusada
      const messageContent = data.choices[0].message.content;
      const refusal = data.choices[0].message.refusal;

      // Se a resposta foi recusada ou o conteúdo é nulo
      if (messageContent === null || refusal) {
        console.log('A API recusou a solicitação ou retornou conteúdo nulo');
        console.log('Mensagem de recusa:', refusal || 'Nenhuma');

        // Usar resposta simulada como fallback
        console.warn('Usando resposta simulada devido à recusa da API.');
        return getMockResult(player1Name, player2Name);
      }

      // Se chegou aqui, temos conteúdo válido
      const resultText = messageContent.trim();
      console.log('Resposta bruta da OpenAI:', resultText);

      // Tentar encontrar o JSON na resposta (pode estar envolvido em texto)
      let jsonMatch = resultText.match(/\{[\s\S]*\}/);
      let resultJson;

      if (jsonMatch) {
        try {
          resultJson = JSON.parse(jsonMatch[0]);
        } catch (parseError) {
          console.error('Erro ao fazer parse do JSON encontrado:', parseError);
          // Tentar limpar o texto e fazer parse novamente
          const cleanedText = jsonMatch[0].replace(/[\u0000-\u001F]+/g, ' ').trim();
          resultJson = JSON.parse(cleanedText);
        }
      } else {
        // Se não encontrar um JSON válido, tentar extrair as informações do texto
        resultJson = extractResultFromText(resultText, player1Name, player2Name);
      }

      console.log('JSON processado:', resultJson);

      // Validar o resultado
      if (!resultJson.winner) {
        console.error('Resposta da OpenAI não contém o vencedor:', resultJson);
        // Definir um vencedor padrão baseado no texto
        const lowerText = resultText.toLowerCase();

        // Verificações específicas para a tela "Você Venceu!" com troféu e prêmio
        if (lowerText.includes('troféu') && lowerText.includes('venceu')) {
          resultJson.winner = player1Name;
          resultJson.confidence = 0.99;
        } else if (lowerText.includes('r$') || lowerText.includes('prêmio')) {
          resultJson.winner = player1Name;
          resultJson.confidence = 0.98;
        } else if (lowerText.includes('venceu') || lowerText.includes('vitória')) {
          resultJson.winner = player1Name;
          resultJson.confidence = 0.9;
        } else {
          resultJson.winner = player2Name;
          resultJson.confidence = 0.7;
        }
      }

      // Verificar se o vencedor corresponde a um dos jogadores (comparação mais robusta)
      const winnerLower = resultJson.winner.toLowerCase();
      const player1Lower = player1Name.toLowerCase();
      const player2Lower = player2Name.toLowerCase();

      const isPlayer1Winner = winnerLower === player1Lower ||
                             winnerLower.includes(player1Lower) ||
                             player1Lower.includes(winnerLower);

      const isPlayer2Winner = winnerLower === player2Lower ||
                             winnerLower.includes(player2Lower) ||
                             player2Lower.includes(winnerLower);

      // O prêmio só deve ser concedido se o jogador atual (player1) for o vencedor
      resultJson.awardPrize = isPlayer1Winner;

      // Adicionar informação de debug
      console.log(`Vencedor identificado: "${resultJson.winner}"`);
      console.log(`Player1: "${player1Name}", Player2: "${player2Name}"`);
      console.log(`Player1 é vencedor: ${isPlayer1Winner}`);
      console.log(`Player2 é vencedor: ${isPlayer2Winner}`);
      console.log(`Prêmio será concedido: ${resultJson.awardPrize}`);

      // Adicionar informação sobre o vencedor para debug
      resultJson.originalWinner = resultJson.winner;

      // Garantir que os scores sejam números
      if (typeof resultJson.player1Score !== 'number') {
        resultJson.player1Score = resultJson.winner === player1Name ? 1 : 0;
      }

      if (typeof resultJson.player2Score !== 'number') {
        resultJson.player2Score = resultJson.winner === player2Name ? 1 : 0;
      }

      // Garantir que o nível de confiança esteja presente
      if (typeof resultJson.confidence !== 'number') {
        resultJson.confidence = 0.9; // Valor padrão se não for fornecido
      }

      // Garantir que o vencedor tenha uma pontuação maior
      if (resultJson.winner === player1Name && resultJson.player1Score <= resultJson.player2Score) {
        resultJson.player1Score = resultJson.player2Score + 1;
      } else if (resultJson.winner === player2Name && resultJson.player2Score <= resultJson.player1Score) {
        resultJson.player2Score = resultJson.player1Score + 1;
      }

      console.log('Resultado final processado:', resultJson);
      return resultJson;
    } catch (parseError) {
      console.error('Erro ao processar resposta da OpenAI:', parseError);
      throw new Error('Não foi possível interpretar o resultado da análise. Por favor, tente novamente.');
    }
  } catch (error) {
    console.error('Erro ao analisar imagem:', error);

    // Registrar informações adicionais para depuração
    if (error instanceof Error) {
      console.error('Detalhes do erro:', error.message);
      console.error('Stack trace:', error.stack);
    }

    // Se houver um erro na API, usar resposta simulada como fallback
    console.warn('Usando resposta simulada devido a erro na API.');
    return getMockResult(player1Name, player2Name);
  }
};

import React, { useState, useEffect } from 'react';
import { Trophy, Calendar, Filter, UserPlus, Loader2 } from 'lucide-react';
import RankingCard from '../components/RankingCard';
import { realStatsService, RankingEntry } from '../services/realStatsService';

export default function RankingPage() {
  const [selectedCategory, setSelectedCategory] = useState('geral');
  const [showInviteModal, setShowInviteModal] = useState(false);

  // Estados para dados da API
  const [rankings, setRankings] = useState<RankingEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const categories = [
    { id: 'geral', name: 'Geral' },
    { id: 'acao', name: 'Ação' },
    { id: 'estrategia', name: 'Estratégia' },
    { id: 'casual', name: 'Casual' }
  ];

  // Carrega dados do ranking
  useEffect(() => {
    const loadRankingData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando dados do ranking...');

        const data = await realStatsService.getGlobalRanking({
          category: selectedCategory === 'geral' ? 'points' : 'points',
          period: 'all_time'
        }, 1, 50);

        setRankings(data);
        console.log(`✅ ${data.length} entradas do ranking carregadas`);

      } catch (err) {
        console.error('❌ Erro ao carregar ranking:', err);
        setError('Erro ao carregar ranking. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadRankingData();
  }, [selectedCategory]);

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Season Banner */}
      <div className="bg-gradient-to-r from-rose-400 to-pink-400 rounded-xl p-6 shadow-lg shadow-rose-500/20">
        <div className="flex items-center gap-2 mb-2">
          <Calendar className="w-6 h-6 text-white" />
          <h2 className="text-white font-bold">Temporada 3</h2>
        </div>
        <p className="text-lg text-white/80 mb-4">Termina em 15 dias</p>
        <div className="flex items-center gap-3">
          <div className="bg-black/20 backdrop-blur-sm rounded-lg p-3">
            <Trophy className="w-6 h-6 text-yellow-400" />
          </div>
          <div>
            <p className="text-white text-sm">Sua Posição</p>
            <p className="text-2xl font-bold text-white">#42</p>
          </div>
        </div>
      </div>

      {/* Invite Friends Button */}
      <div className="bg-gradient-to-r from-fuchsia-500 to-magenta-500 rounded-xl p-6 text-center shadow-lg shadow-fuchsia-500/20">
        <div className="bg-white/20 backdrop-blur-sm p-4 rounded-xl inline-block mb-4">
          <UserPlus className="w-12 h-12 text-white" />
        </div>
        <h2 className="text-white text-2xl font-bold mb-2">Convide seus Amigos</h2>
        <p className="text-white/80 mb-4">Jogue com seus amigos e ganhe recompensas exclusivas!</p>
        <button
          onClick={() => setShowInviteModal(true)}
          className="bg-black/20 backdrop-blur-sm text-white font-bold px-8 py-3 rounded-xl inline-flex items-center gap-2 hover:bg-black/30 transition-colors border border-white/10"
        >
          <UserPlus className="w-5 h-5" />
          Convidar Amigos
        </button>
      </div>

      {/* Category Filter */}
      <div className="flex items-center gap-2 overflow-x-auto pb-2">
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 p-2 rounded-lg">
          <Filter className="w-5 h-5 text-rose-400" />
        </div>
        {categories.map(category => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-4 py-2 rounded-lg whitespace-nowrap ${
              selectedCategory === category.id
                ? 'bg-gradient-to-r from-rose-400 to-pink-400 text-white shadow-lg shadow-rose-500/20'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            {category.name}
          </button>
        ))}
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-center">
          <p className="text-red-400 mb-2">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Loader2 className="w-8 h-8 text-indigo-400 mx-auto mb-4 animate-spin" />
          <h3 className="text-white font-bold text-lg mb-2">Carregando ranking...</h3>
          <p className="text-gray-400">Aguarde enquanto buscamos os melhores jogadores.</p>
        </div>
      ) : (
        <>
      {/* Top Players */}
      <section>
        <h2 className="text-white text-lg font-bold mb-3">Top Jogadores</h2>
        <div className="space-y-3">
          {rankings.length > 0 ? (
            rankings.map((entry) => (
              <RankingCard
                key={entry.user_id}
                position={entry.position}
                name={entry.display_name || entry.username}
                avatar={entry.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'}
                points={entry.ranking_points}
                trend="up" // Pode ser calculado baseado em dados históricos
                isUser={entry.is_current_user}
              />
            ))
          ) : (
            <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
              <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <h3 className="text-white font-bold text-lg mb-2">Nenhum ranking encontrado</h3>
              <p className="text-gray-400">Não há dados de ranking disponíveis no momento.</p>
            </div>
          )}
        </div>
      </section>
        </>
      )}

      {/* Weekly Highlights */}
      <section>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-white text-lg font-bold">Destaques da Semana</h2>
          <button className="text-rose-400 text-sm hover:text-rose-300 transition-colors">Ver Tudo</button>
        </div>
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 hover:bg-gray-700/50 transition-colors">
            <p className="text-gray-400 text-sm mb-1">Maior Sequência</p>
            <p className="text-white font-bold">15 Vitórias</p>
            <p className="text-sm bg-gradient-to-r from-rose-400 to-pink-400 bg-clip-text text-transparent font-medium">Lucas Oliveira</p>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 hover:bg-gray-700/50 transition-colors">
            <p className="text-gray-400 text-sm mb-1">Mais Pontos</p>
            <p className="text-white font-bold">2.450 pts</p>
            <p className="text-sm bg-gradient-to-r from-rose-400 to-pink-400 bg-clip-text text-transparent font-medium">Ana Silva</p>
          </div>
        </div>
      </section>

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fadeIn">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl w-full max-w-md p-6">
            <h3 className="text-white text-xl font-bold mb-4">Convidar Amigos</h3>

            <div className="bg-gray-700/50 backdrop-blur-sm border border-white/10 p-4 rounded-lg mb-6">
              <p className="text-white font-medium mb-2">Seu Link de Convite</p>
              <div className="flex gap-2">
                <input
                  type="text"
                  value="https://fairplay.games/invite/abc123"
                  readOnly
                  className="flex-1 bg-gray-600/50 backdrop-blur-sm border border-white/10 text-white rounded-lg px-3 py-2"
                />
                <button className="bg-yellow-400 text-black px-4 rounded-lg font-medium hover:bg-yellow-500">
                  Copiar
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <button className="w-full bg-[#1DA1F2] text-white font-bold py-3 rounded-lg flex items-center justify-center gap-2">
                Compartilhar no Twitter
              </button>
              <button className="w-full bg-[#25D366] text-white font-bold py-3 rounded-lg flex items-center justify-center gap-2">
                Compartilhar no WhatsApp
              </button>
              <button className="w-full bg-[#7289DA] text-white font-bold py-3 rounded-lg flex items-center justify-center gap-2">
                Compartilhar no Discord
              </button>
            </div>

            <button
              onClick={() => setShowInviteModal(false)}
              className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white font-bold py-3 rounded-lg mt-4 hover:bg-gray-600/50 transition-colors"
            >
              Fechar
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
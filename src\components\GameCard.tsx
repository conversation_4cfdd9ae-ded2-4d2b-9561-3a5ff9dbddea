import React from 'react';
import { Users, Trophy } from 'lucide-react';

interface GameCardProps {
  title: string;
  rating: number;
  players?: number;
  type?: 'realtime' | 'continuous';
  isGameOfTheWeek?: boolean;
  prizePool?: number;
  entryFee?: number;
  isRecommended?: boolean;
  imageUrl: string;
  onClick?: () => void;
}

export default function GameCard({ 
  title, 
  rating, 
  players, 
  type,
  isGameOfTheWeek,
  prizePool,
  entryFee,
  isRecommended = false, 
  imageUrl,
  onClick 
}: GameCardProps) {
  return (
    <div 
      className="relative rounded-xl overflow-hidden cursor-pointer"
      onClick={onClick}
    >
      {isGameOfTheWeek && (
        <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/60 to-transparent p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            <span className="text-white font-bold"><PERSON><PERSON></span>
          </div>
        </div>
      )}
      <img 
        src={imageUrl} 
        alt={title}
        className={`w-full object-cover ${isGameOfTheWeek ? 'h-48 sm:h-64' : 'h-32'}`}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent p-4 flex flex-col justify-end">
        {isRecommended && (
          <span className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
            Recomendado
          </span>
        )}
        <div className="flex flex-wrap items-center gap-2 mb-1">
          <span className="flex items-center gap-1">
            <span className="text-yellow-400">⭐</span>
            <span className="text-white text-sm">{rating}</span>
          </span>
          {(type === 'continuous' || isGameOfTheWeek) && prizePool && entryFee && (
            <span className="flex items-center gap-3">
              <span className="flex items-center gap-1">
                <span className="text-gray-400 text-sm">Entrada:</span>
                <span className="text-yellow-400 font-bold text-sm">R$ {entryFee.toFixed(2)}</span>
              </span>
              <span className="flex items-center gap-1">
                <span className="text-gray-400 text-sm">Prêmio:</span>
                <span className="text-green-400 font-bold text-sm">R$ {prizePool.toFixed(2)}</span>
              </span>
            </span>
          )}
          {players && (
            <span className="text-white text-sm flex items-center gap-1">
              <Users className="w-3 h-3" />
              {players} online
            </span>
          )}
        </div>
        <h3 className="text-white font-bold">{title}</h3>
      </div>
    </div>
  );
}
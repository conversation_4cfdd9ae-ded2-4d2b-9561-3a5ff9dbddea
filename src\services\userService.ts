/**
 * Serviço para gerenciar informações do usuário usando localStorage
 */

// Chaves para armazenamento no localStorage
const USER_NAME_KEY = 'playstrike_user_name';
const USER_AVATAR_KEY = 'playstrike_user_avatar';
const USER_ID_KEY = 'playstrike_user_id';

/**
 * Salva o nome do usuário no localStorage
 * @param name Nome do usuário
 */
export const saveUserName = (name: string): void => {
  localStorage.setItem(USER_NAME_KEY, name);
};

/**
 * Obtém o nome do usuário do localStorage
 * @returns Nome do usuário ou null se não estiver definido
 */
export const getUserName = (): string | null => {
  return localStorage.getItem(USER_NAME_KEY);
};

/**
 * Salva o avatar do usuário no localStorage
 * @param avatarUrl URL do avatar do usuário
 */
export const saveUserAvatar = (avatarUrl: string): void => {
  localStorage.setItem(USER_AVATAR_KEY, avatarUrl);
};

/**
 * Obtém o avatar do usuário do localStorage
 * @returns URL do avatar do usuário ou null se não estiver definido
 */
export const getUserAvatar = (): string | null => {
  return localStorage.getItem(USER_AVATAR_KEY);
};

/**
 * Gera e salva um ID único para o usuário
 * @returns ID gerado
 */
export const generateAndSaveUserId = (): string => {
  const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  localStorage.setItem(USER_ID_KEY, userId);
  return userId;
};

/**
 * Obtém o ID do usuário do localStorage
 * @returns ID do usuário ou null se não estiver definido
 */
export const getUserId = (): string | null => {
  return localStorage.getItem(USER_ID_KEY);
};

/**
 * Verifica se o usuário está "logado" (tem nome definido)
 * @returns true se o usuário estiver logado, false caso contrário
 */
export const isUserLoggedIn = (): boolean => {
  return !!getUserName();
};

/**
 * Remove todas as informações do usuário do localStorage
 */
export const clearUserData = (): void => {
  localStorage.removeItem(USER_NAME_KEY);
  localStorage.removeItem(USER_AVATAR_KEY);
  localStorage.removeItem(USER_ID_KEY);
};

/**
 * Obtém todas as informações do usuário
 * @returns Objeto com as informações do usuário
 */
export const getUserData = () => {
  return {
    name: getUserName(),
    avatar: getUserAvatar() || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    id: getUserId() || generateAndSaveUserId()
  };
};

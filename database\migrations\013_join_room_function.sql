-- Migration 013: Join Room Function
-- Created: 2025-06-08
-- Description: Creates atomic function for joining game rooms

-- Function to atomically join a game room
CREATE OR REPLACE FUNCTION join_game_room(
    p_room_id UUID,
    p_user_id UUID,
    p_entry_fee DECIMAL(10,2),
    p_room_name VARCHAR(100)
)
RETURNS VOID AS $$
DECLARE
    v_current_players INTEGER;
    v_max_players INTEGER;
    v_wallet_balance DECIMAL(12,2);
    v_participant_exists BOOLEAN;
BEGIN
    -- Lock the room row to prevent race conditions
    SELECT current_players, max_players
    INTO v_current_players, v_max_players
    FROM game_rooms
    WHERE id = p_room_id
    FOR UPDATE;
    
    -- Check if room exists
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Room not found';
    END IF;
    
    -- Check if room is full
    IF v_current_players >= v_max_players THEN
        RAISE EXCEPTION 'Room is full';
    END IF;
    
    -- Check if user is already in the room
    SELECT EXISTS(
        SELECT 1 FROM room_participants 
        WHERE room_id = p_room_id AND user_id = p_user_id
    ) INTO v_participant_exists;
    
    IF v_participant_exists THEN
        RAISE EXCEPTION 'Already in room';
    END IF;
    
    -- Check wallet balance
    SELECT balance INTO v_wallet_balance
    FROM wallets
    WHERE user_id = p_user_id
    FOR UPDATE;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Wallet not found';
    END IF;
    
    IF v_wallet_balance < p_entry_fee THEN
        RAISE EXCEPTION 'Insufficient balance';
    END IF;
    
    -- Deduct entry fee from wallet
    UPDATE wallets
    SET balance = balance - p_entry_fee,
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    -- Add user to room participants (trigger will update current_players)
    INSERT INTO room_participants (room_id, user_id, is_ready)
    VALUES (p_room_id, p_user_id, false);
    
    -- Create transaction record
    INSERT INTO transactions (user_id, wallet_id, type, amount, status, description)
    SELECT p_user_id, w.id, 'bet', p_entry_fee, 'completed', 
           'Entry fee for room: ' || p_room_name
    FROM wallets w
    WHERE w.user_id = p_user_id;
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION join_game_room(UUID, UUID, DECIMAL, VARCHAR) TO authenticated;

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugRoomTeste2() {
  try {
    console.log('🔍 Investigando sala "Teste2"...');
    
    // Buscar a sala Teste2
    const { data: rooms, error: roomsError } = await supabase
      .from('game_rooms')
      .select('*')
      .eq('name', 'Teste2');
    
    if (roomsError) {
      console.error('❌ Erro ao buscar salas:', roomsError);
      return;
    }
    
    if (!rooms || rooms.length === 0) {
      console.log('❌ Sala "Teste2" não encontrada');
      return;
    }
    
    const room = rooms[0];
    console.log('📋 <PERSON>os da sala Teste2:');
    console.log(`  ID: ${room.id}`);
    console.log(`  Nome: ${room.name}`);
    console.log(`  Jogadores atuais: ${room.current_players}`);
    console.log(`  Jogadores máximos: ${room.max_players}`);
    console.log(`  Status: ${room.status}`);
    console.log(`  Host: ${room.host_user_id}`);
    console.log(`  Criada em: ${room.created_at}`);
    
    // Buscar participantes reais
    const { data: participants, error: participantsError } = await supabase
      .from('room_participants')
      .select(`
        user_id,
        is_ready,
        joined_at,
        users (username, email)
      `)
      .eq('room_id', room.id);
    
    if (participantsError) {
      console.error('❌ Erro ao buscar participantes:', participantsError);
      return;
    }
    
    console.log('\n👥 Participantes na sala:');
    if (participants && participants.length > 0) {
      participants.forEach((p, index) => {
        console.log(`  ${index + 1}. ${p.users?.username || 'Unknown'} (${p.user_id})`);
        console.log(`     Pronto: ${p.is_ready ? 'Sim' : 'Não'}`);
        console.log(`     Entrou em: ${p.joined_at}`);
      });
    } else {
      console.log('  Nenhum participante encontrado');
    }
    
    const actualCount = participants?.length || 0;
    console.log(`\n📊 Contagem real de participantes: ${actualCount}`);
    console.log(`📊 Contagem na tabela game_rooms: ${room.current_players}`);
    
    if (actualCount !== room.current_players) {
      console.log('⚠️ INCONSISTÊNCIA DETECTADA!');
      console.log('🔧 Corrigindo contagem...');
      
      const { error: updateError } = await supabase
        .from('game_rooms')
        .update({ 
          current_players: actualCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', room.id);
      
      if (updateError) {
        console.error('❌ Erro ao corrigir contagem:', updateError);
      } else {
        console.log('✅ Contagem corrigida!');
      }
    } else {
      console.log('✅ Contagem está correta');
    }
    
    // Verificar se a sala está realmente cheia
    const isRoomFull = actualCount >= room.max_players;
    console.log(`\n🚪 Sala está cheia? ${isRoomFull ? 'SIM' : 'NÃO'}`);
    console.log(`   Espaços disponíveis: ${room.max_players - actualCount}`);
    
    // Buscar informações dos usuários matt2 e matt3
    console.log('\n🔍 Verificando usuários matt2 e matt3...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email')
      .in('username', ['matt2', 'matt3']);
    
    if (usersError) {
      console.error('❌ Erro ao buscar usuários:', usersError);
    } else if (users) {
      users.forEach(user => {
        console.log(`  ${user.username}: ${user.id}`);
        const isInRoom = participants?.some(p => p.user_id === user.id);
        console.log(`    Na sala: ${isInRoom ? 'SIM' : 'NÃO'}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

debugRoomTeste2();

import React, { useState } from 'react';
import { X, Trophy, AlertTriangle } from 'lucide-react';
import MatchResultUploader from './MatchResultUploader';
import { analyzeGameResult } from '../services/openaiService';

interface MatchResultModalProps {
  onClose: () => void;
  onResultSubmit: (winner: string, player1Score: number, player2Score: number, awardPrize?: boolean) => void;
  onSubmitViaWebSocket?: (result: 'win' | 'loss' | 'draw', score?: number) => void;
  match: {
    id: string;
    player1: {
      id: string;
      name: string;
      avatar: string;
      username?: string;
      display_name?: string;
    };
    player2: {
      id: string;
      name: string;
      avatar: string;
    };
    scheduledTime: string;
  };
  selectedFile?: File | null;
  onFileSelect?: (file: File | null) => void;
  useWebSocket?: boolean;
}

export default function MatchResultModal({
  onClose,
  onResultSubmit,
  onSubmitViaWebSocket,
  match,
  selectedFile,
  onFileSelect,
  useWebSocket = false
}: MatchResultModalProps) {
  const [result, setResult] = useState<{
    winner: string;
    player1Score: number;
    player2Score: number;
    confidence?: number;
    awardPrize?: boolean;
    originalWinner?: string;
  } | null>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleResultProcessed = (
    winner: string,
    player1Score: number,
    player2Score: number,
    confidence?: number,
    awardPrize?: boolean,
    originalWinner?: string
  ) => {
    setResult({
      winner,
      player1Score,
      player2Score,
      confidence,
      awardPrize,
      originalWinner
    });
  };



  const handleSubmit = async () => {
    if (!result) {
      setError('Por favor, processe um resultado primeiro.');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (useWebSocket && onSubmitViaWebSocket) {
        // Usar WebSocket para submeter resultado
        // Melhorar comparação considerando username e display_name
        const winnerName = result.winner.toLowerCase();
        const originalWinnerName = result.originalWinner?.toLowerCase();
        const player1Name = match.player1.name.toLowerCase();
        const player1Username = match.player1.username?.toLowerCase();
        const player1DisplayName = match.player1.display_name?.toLowerCase();

        // Verificar se o vencedor corresponde ao jogador atual
        // Considerar múltiplas formas de identificação
        const isCurrentPlayerWinner = winnerName === player1Name ||
                                     originalWinnerName === player1Name ||
                                     (player1Username && (winnerName === player1Username || originalWinnerName === player1Username)) ||
                                     (player1DisplayName && (winnerName === player1DisplayName || originalWinnerName === player1DisplayName)) ||
                                     // Verificar se o nome contém o username/display_name do jogador
                                     (player1Username && winnerName.includes(player1Username)) ||
                                     (player1DisplayName && winnerName.includes(player1DisplayName)) ||
                                     winnerName.includes(player1Name) ||
                                     player1Name.includes(winnerName);

        const resultType: 'win' | 'loss' | 'draw' =
          isCurrentPlayerWinner ? 'win' :
          result.player1Score === result.player2Score ? 'draw' : 'loss';

        console.log(`🔍 Comparação detalhada de nomes:`);
        console.log(`  Vencedor: "${result.winner}" (original: "${result.originalWinner}")`);
        console.log(`  Jogador atual:`);
        console.log(`    - Nome: "${match.player1.name}"`);
        console.log(`    - Username: "${match.player1.username}"`);
        console.log(`    - Display Name: "${match.player1.display_name}"`);
        console.log(`  É vencedor? ${isCurrentPlayerWinner ? 'SIM' : 'NÃO'}`);
        console.log(`  Tipo de resultado: ${resultType}`);

        onSubmitViaWebSocket(resultType, result.player1Score);
        onClose();
      } else {
        // Usar método original
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simular envio

        // Verificar se o vencedor é o jogador atual (player1) - versão melhorada
        const winnerName = result.winner.toLowerCase();
        const originalWinnerName = result.originalWinner?.toLowerCase();
        const player1Name = match.player1.name.toLowerCase();
        const player1Username = match.player1.username?.toLowerCase();
        const player1DisplayName = match.player1.display_name?.toLowerCase();

        const isCurrentPlayerWinner = winnerName === player1Name ||
                                     originalWinnerName === player1Name ||
                                     (player1Username && (winnerName === player1Username || originalWinnerName === player1Username)) ||
                                     (player1DisplayName && (winnerName === player1DisplayName || originalWinnerName === player1DisplayName)) ||
                                     (player1Username && winnerName.includes(player1Username)) ||
                                     (player1DisplayName && winnerName.includes(player1DisplayName)) ||
                                     winnerName.includes(player1Name) ||
                                     player1Name.includes(winnerName);

        // Determinar se o prêmio deve ser concedido
        // Só conceder o prêmio se o vencedor for o jogador atual (player1)
        const awardPrize = isCurrentPlayerWinner;

        console.log(`Enviando resultado: Vencedor=${result.winner}, Jogador Atual=${match.player1.name}`);
        console.log(`O jogador atual venceu? ${isCurrentPlayerWinner ? 'SIM' : 'NÃO'}`);
        console.log(`Prêmio será concedido? ${awardPrize ? 'SIM' : 'NÃO'}`);

        // Usar o nome exato do jogador se ele for o vencedor
        const finalWinner = isCurrentPlayerWinner ? match.player1.name : result.winner;

        onResultSubmit(
          finalWinner,
          result.player1Score,
          result.player2Score,
          awardPrize
        );
        onClose();
      }
    } catch (err) {
      setError('Ocorreu um erro ao enviar o resultado. Por favor, tente novamente.');
      console.error('Error submitting result:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 md:p-6 animate-fadeIn overflow-hidden">
      <div className="bg-gray-800/90 backdrop-blur-sm border border-indigo-500/20 rounded-xl w-full max-w-2xl shadow-xl flex flex-col max-h-[90vh] md:max-h-[85vh]">
        {/* Header - Fixed at top */}
        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-4 flex items-center justify-between sticky top-0 z-10">
          <div className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-white" />
            <h3 className="text-white font-bold">Enviar Resultado da Partida</h3>
          </div>
          <button
            onClick={onClose}
            className="text-white/80 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content - Scrollable */}
        <div className="p-4 md:p-5 space-y-4 md:space-y-5 overflow-y-auto flex-grow">
          {/* Match Info */}
          <div className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4">
            <h4 className="text-white font-medium mb-3">Detalhes da Partida:</h4>

            <div className="flex items-center justify-between flex-wrap md:flex-nowrap gap-2">
              <div className="flex items-center gap-2">
                <img
                  src={match.player1.avatar}
                  alt={match.player1.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <div>
                  <span className="text-white font-medium">{match.player1.name}</span>
                  <p className="text-gray-400 text-xs">Jogador 1</p>
                </div>
              </div>

              <span className="text-white font-bold">VS</span>

              <div className="flex items-center gap-2">
                <div>
                  <span className="text-white font-medium">{match.player2.name}</span>
                  <p className="text-gray-400 text-xs text-right">Jogador 2</p>
                </div>
                <img
                  src={match.player2.avatar}
                  alt={match.player2.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
              </div>
            </div>

            <div className="mt-3 text-center text-gray-400 text-sm">
              {new Date(match.scheduledTime).toLocaleDateString('pt-BR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
              })} às {new Date(match.scheduledTime).toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>

          {/* Result Uploader */}
          <MatchResultUploader
            onResultProcessed={handleResultProcessed}
            player1Name={match.player1.username || match.player1.display_name || match.player1.name}
            player2Name={match.player2.name}
            selectedFile={selectedFile}
            setSelectedFile={onFileSelect}
          />

          {/* Error Message */}
          {error && (
            <div className="bg-red-500/20 text-red-400 p-3 rounded-lg flex items-start gap-2">
              <AlertTriangle className="w-5 h-5 flex-shrink-0 mt-0.5" />
              <p>{error}</p>
            </div>
          )}
        </div>

        {/* Footer - Fixed at bottom */}
        <div className="p-4 border-t border-gray-700 flex justify-end gap-3 sticky bottom-0 bg-gray-800/95 backdrop-blur-sm z-10">
          <button
            onClick={onClose}
            className="bg-gray-700 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-600"
          >
            Cancelar
          </button>
          <button
            onClick={handleSubmit}
            disabled={!result || isSubmitting}
            className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-2 px-4 rounded-lg hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Enviando...' : 'Confirmar Resultado'}
          </button>
        </div>
      </div>
    </div>
  );
}

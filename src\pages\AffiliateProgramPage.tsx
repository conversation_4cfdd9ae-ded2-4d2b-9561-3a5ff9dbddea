import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Users,
  ArrowLeft,
  Copy,
  TrendingUp,
  DollarSign,
  Share2,
  HelpCircle,
  ChevronDown,
  ChevronUp,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { realAffiliateService, AffiliateStats, AffiliateReferral } from '../services/realAffiliateService';

interface ReferralUser {
  id: string;
  name: string;
  avatar: string;
  joinDate: string;
  totalSpent: number;
  commission: number;
}

interface EarningHistory {
  id: string;
  date: string;
  amount: number;
  source: string;
  status: 'pending' | 'completed';
}

export default function AffiliateProgramPage() {
  const navigate = useNavigate();
  const [copied, setCopied] = useState(false);
  const [showFaq, setShowFaq] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [affiliateStats, setAffiliateStats] = useState<AffiliateStats | null>(null);
  const [referrals, setReferrals] = useState<AffiliateReferral[]>([]);

  const affiliateLink = affiliateStats?.affiliate_code
    ? `fplaygames.com/ref/${affiliateStats.affiliate_code}`
    : "fplaygames.com/ref/loading...";

  // Carregar dados da API
  useEffect(() => {
    const loadAffiliateData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando dados de afiliados...');

        // Carregar estatísticas e indicações em paralelo
        const [statsData, referralsData] = await Promise.all([
          realAffiliateService.getAffiliateStats(),
          realAffiliateService.getReferrals(1, 5)
        ]);

        if (statsData) {
          setAffiliateStats(statsData);
          console.log('✅ Estatísticas de afiliados carregadas');
        }

        setReferrals(referralsData);
        console.log(`✅ ${referralsData.length} indicações carregadas`);

      } catch (err) {
        console.error('❌ Erro ao carregar dados de afiliados:', err);
        setError('Erro ao carregar dados. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadAffiliateData();
  }, []);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(affiliateLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Remover dados mock - agora usando dados da API

  const earningHistory: EarningHistory[] = [
    {
      id: '1',
      date: '10/08/2023',
      amount: 15.00,
      source: 'Maria Santos',
      status: 'completed'
    },
    {
      id: '2',
      date: '05/08/2023',
      amount: 12.50,
      source: 'Carlos Oliveira',
      status: 'completed'
    },
    {
      id: '3',
      date: '01/08/2023',
      amount: 8.75,
      source: 'Ana Pereira',
      status: 'completed'
    },
    {
      id: '4',
      date: '28/07/2023',
      amount: 10.25,
      source: 'Pedro Almeida',
      status: 'completed'
    },
    {
      id: '5',
      date: 'Hoje',
      amount: 7.50,
      source: 'Juliana Costa',
      status: 'pending'
    }
  ];

  const faqItems = [
    {
      question: 'Como funciona o programa de afiliados?',
      answer: 'Você ganha uma comissão de 15% sobre as taxas pagas pelos amigos que você convidar para a plataforma. A comissão é creditada automaticamente na sua conta quando seu amigo participa de jogos com taxas.'
    },
    {
      question: 'Quando recebo minhas comissões?',
      answer: 'As comissões são calculadas diariamente e creditadas na sua conta em até 24 horas após a confirmação da transação do seu indicado.'
    },
    {
      question: 'Existe um limite de pessoas que posso convidar?',
      answer: 'Não há limite para o número de pessoas que você pode convidar. Quanto mais amigos você trouxer, maiores serão seus ganhos!'
    },
    {
      question: 'Posso sacar minhas comissões?',
      answer: 'Sim! As comissões são adicionadas ao seu saldo na plataforma e podem ser sacadas seguindo o mesmo processo de saque normal, através de PIX ou transferência bancária.'
    },
    {
      question: 'Por quanto tempo ganho comissão dos meus indicados?',
      answer: 'Você ganha comissão por todas as taxas pagas pelo seu indicado durante todo o tempo em que ele permanecer ativo na plataforma. Não há prazo de expiração.'
    }
  ];

  // Calcular valores a partir dos dados da API
  const totalEarnings = affiliateStats?.total_earnings || 0;
  const pendingEarnings = affiliateStats?.pending_earnings || 0;
  const totalReferrals = affiliateStats?.total_referrals || 0;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-indigo-500 animate-spin mx-auto mb-4" />
          <p className="text-gray-400">Carregando dados de afiliados...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="flex items-center gap-2 sm:gap-4">
        <button
          onClick={() => navigate('/profile')}
          className="flex items-center gap-1 sm:gap-2 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5" />
          <span className="text-sm sm:text-base">Voltar</span>
        </button>
        <h1 className="text-xl sm:text-2xl font-bold text-white">Programa de Afiliados</h1>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-xl p-4 sm:p-6 relative overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mt-10 -mr-10 blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -mb-8 -ml-8 blur-xl"></div>

        <div className="relative z-10">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            <h2 className="text-white font-bold text-lg sm:text-xl">Convide amigos e ganhe dinheiro!</h2>
          </div>
          <p className="text-white/80 mb-4 sm:mb-6 max-w-2xl text-sm sm:text-base">
            Ganhe 15% de comissão sobre todas as taxas pagas pelos amigos que você convidar para a plataforma.
            Quanto mais amigos você trouxer, mais você ganha!
          </p>

          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 sm:p-4 mb-4 sm:mb-6 flex items-center justify-between max-w-lg">
            <span className="text-white text-xs sm:text-base truncate mr-2">{affiliateLink}</span>
            <button
              onClick={handleCopyLink}
              className="bg-white/30 hover:bg-white/40 transition-colors px-2 sm:px-3 py-1.5 sm:py-2 rounded-md flex items-center gap-1 sm:gap-2"
            >
              {copied ? (
                <>
                  <CheckCircle className="w-4 h-4 text-white" />
                  <span className="text-white text-xs sm:text-sm font-medium">Copiado!</span>
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4 text-white" />
                  <span className="text-white text-xs sm:text-sm font-medium hidden xs:inline">Copiar Link</span>
                  <span className="text-white text-xs sm:text-sm font-medium xs:hidden">Copiar</span>
                </>
              )}
            </button>
          </div>

          <div className="grid grid-cols-1 xs:grid-cols-3 gap-3 sm:gap-4 max-w-2xl">
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center gap-1 sm:gap-2 mb-1 sm:mb-2">
                <Users className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                <h3 className="text-white font-medium text-xs sm:text-sm">Amigos Convidados</h3>
              </div>
              <p className="text-white font-bold text-xl sm:text-2xl">{totalReferrals}</p>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center gap-1 sm:gap-2 mb-1 sm:mb-2">
                <DollarSign className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                <h3 className="text-white font-medium text-xs sm:text-sm">Ganhos Totais</h3>
              </div>
              <p className="text-white font-bold text-xl sm:text-2xl">R$ {totalEarnings.toFixed(2)}</p>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center gap-1 sm:gap-2 mb-1 sm:mb-2">
                <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                <h3 className="text-white font-medium text-xs sm:text-sm">Pendente</h3>
              </div>
              <p className="text-white font-bold text-xl sm:text-2xl">R$ {pendingEarnings.toFixed(2)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* How It Works */}
      <section>
        <h2 className="text-white text-base sm:text-lg font-bold mb-3 sm:mb-4">Como Funciona</h2>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center">
            <div className="bg-indigo-500/20 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3">
              <Share2 className="w-5 h-5 sm:w-6 sm:h-6 text-indigo-400" />
            </div>
            <h3 className="text-white font-bold mb-1 sm:mb-2 text-sm sm:text-base">1. Compartilhe seu link</h3>
            <p className="text-gray-400 text-xs sm:text-sm">Envie seu link de convite para amigos através de redes sociais ou mensagens.</p>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center">
            <div className="bg-purple-500/20 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3">
              <Users className="w-5 h-5 sm:w-6 sm:h-6 text-purple-400" />
            </div>
            <h3 className="text-white font-bold mb-1 sm:mb-2 text-sm sm:text-base">2. Amigos se cadastram</h3>
            <p className="text-gray-400 text-xs sm:text-sm">Seus amigos criam uma conta através do seu link de convite.</p>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 text-center">
            <div className="bg-pink-500/20 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3">
              <DollarSign className="w-5 h-5 sm:w-6 sm:h-6 text-pink-400" />
            </div>
            <h3 className="text-white font-bold mb-1 sm:mb-2 text-sm sm:text-base">3. Ganhe comissões</h3>
            <p className="text-gray-400 text-xs sm:text-sm">Receba 15% de todas as taxas pagas pelos seus indicados.</p>
          </div>
        </div>
      </section>

      {/* Referrals */}
      <section>
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <h2 className="text-white text-base sm:text-lg font-bold">Seus Indicados</h2>
          <button
            onClick={() => navigate('/affiliate-program/referrals')}
            className="text-indigo-400 hover:text-indigo-300 text-xs sm:text-sm font-medium"
          >
            Ver todos
          </button>
        </div>
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden">
          <div className="hidden sm:grid grid-cols-5 gap-4 p-4 border-b border-gray-700 text-gray-400 text-sm">
            <div>Usuário</div>
            <div>Data de Cadastro</div>
            <div>Total Gasto</div>
            <div>Sua Comissão</div>
            <div></div>
          </div>
          {referrals.slice(0, 5).map(referral => (
            <div key={referral.id} className="grid grid-cols-1 sm:grid-cols-5 gap-2 sm:gap-4 p-4 border-b border-gray-700 items-center">
              <div className="flex items-center justify-between sm:justify-start gap-3">
                <div className="flex items-center gap-2 sm:gap-3">
                  <img
                    src={referral.referred_user.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'}
                    alt={referral.referred_user.display_name}
                    className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover"
                  />
                  <span className="text-white text-sm sm:text-base">{referral.referred_user.display_name}</span>
                </div>
                <button className="text-indigo-400 text-xs sm:hidden hover:text-indigo-300">Detalhes</button>
              </div>

              <div className="sm:hidden grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-400">Data:</span>
                  <span className="text-white ml-1">{new Date(referral.registration_date).toLocaleDateString('pt-BR')}</span>
                </div>
                <div>
                  <span className="text-gray-400">Gasto:</span>
                  <span className="text-white ml-1">R$ {referral.total_spent.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-400">Comissão:</span>
                  <span className="text-green-400 ml-1">R$ {referral.commission_earned.toFixed(2)}</span>
                </div>
              </div>

              <div className="hidden sm:block text-gray-400">{new Date(referral.registration_date).toLocaleDateString('pt-BR')}</div>
              <div className="hidden sm:block text-white">R$ {referral.total_spent.toFixed(2)}</div>
              <div className="hidden sm:block text-green-400">R$ {referral.commission_earned.toFixed(2)}</div>
              <div className="hidden sm:block text-right">
                <button className="text-indigo-400 text-sm hover:text-indigo-300">Detalhes</button>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Earnings History */}
      <section>
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <h2 className="text-white text-base sm:text-lg font-bold">Histórico de Ganhos</h2>
          <button
            onClick={() => navigate('/affiliate-program/earnings')}
            className="text-indigo-400 hover:text-indigo-300 text-xs sm:text-sm font-medium"
          >
            Ver todos
          </button>
        </div>
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden">
          <div className="hidden sm:grid grid-cols-4 gap-4 p-4 border-b border-gray-700 text-gray-400 text-sm">
            <div>Data</div>
            <div>Origem</div>
            <div>Valor</div>
            <div>Status</div>
          </div>
          {earningHistory.slice(0, 5).map(earning => (
            <div key={earning.id} className="grid grid-cols-1 sm:grid-cols-4 gap-2 sm:gap-4 p-4 border-b border-gray-700">
              <div className="flex items-center justify-between sm:hidden">
                <div className="flex items-center gap-2">
                  <div className="text-white text-sm">{earning.source}</div>
                  <div className="text-gray-400 text-xs">{earning.date}</div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-green-400 text-sm">R$ {earning.amount.toFixed(2)}</div>
                  {earning.status === 'completed' ? (
                    <span className="bg-green-500/20 text-green-400 text-xs px-2 py-0.5 rounded-full">
                      Concluído
                    </span>
                  ) : (
                    <span className="bg-yellow-500/20 text-yellow-400 text-xs px-2 py-0.5 rounded-full">
                      Pendente
                    </span>
                  )}
                </div>
              </div>

              <div className="hidden sm:block text-gray-400">{earning.date}</div>
              <div className="hidden sm:block text-white">{earning.source}</div>
              <div className="hidden sm:block text-green-400">R$ {earning.amount.toFixed(2)}</div>
              <div className="hidden sm:block">
                {earning.status === 'completed' ? (
                  <span className="bg-green-500/20 text-green-400 text-xs px-2 py-1 rounded-full">
                    Concluído
                  </span>
                ) : (
                  <span className="bg-yellow-500/20 text-yellow-400 text-xs px-2 py-1 rounded-full">
                    Pendente
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* FAQ */}
      <section>
        <h2 className="text-white text-base sm:text-lg font-bold mb-3 sm:mb-4">Perguntas Frequentes</h2>
        <div className="space-y-3">
          {faqItems.map((item, index) => (
            <div
              key={index}
              className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden"
            >
              <button
                className="w-full p-4 flex items-center justify-between text-left"
                onClick={() => setShowFaq(showFaq === index ? null : index)}
              >
                <span className="text-white font-medium text-sm sm:text-base">{item.question}</span>
                {showFaq === index ? (
                  <ChevronUp className="w-5 h-5 text-gray-400" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-400" />
                )}
              </button>
              {showFaq === index && (
                <div className="p-4 pt-0 text-gray-400 text-xs sm:text-sm border-t border-gray-700">
                  {item.answer}
                </div>
              )}
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}

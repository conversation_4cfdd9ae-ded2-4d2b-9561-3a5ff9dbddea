const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function cleanupStuckRooms() {
  console.log('🧹 Limpando rooms presas no status "playing"...\n');

  try {
    // Buscar rooms presas
    console.log('1. Identificando rooms problemáticas...');
    const { data: stuckRooms, error: roomError } = await supabase
      .from('game_rooms')
      .select(`
        id,
        status,
        current_players,
        max_players,
        created_at,
        room_participants!inner(
          user_id,
          users!inner(username)
        ),
        matches!inner(
          id,
          status,
          created_at,
          match_participants!inner(
            user_id,
            stats,
            users!inner(username)
          )
        )
      `)
      .eq('status', 'playing')
      .order('created_at', { ascending: true });

    if (roomError) {
      console.error('❌ Erro ao buscar rooms:', roomError);
      return;
    }

    console.log(`📊 Encontradas ${stuckRooms.length} rooms com status "playing":\n`);

    if (stuckRooms.length === 0) {
      console.log('✅ Nenhuma room presa encontrada!');
      return;
    }

    // Analisar cada room
    const roomsToCleanup = [];

    stuckRooms.forEach((room, index) => {
      console.log(`${index + 1}. Room ID: ${room.id}`);
      console.log(`   Status: ${room.status}`);
      console.log(`   Criada: ${room.created_at}`);
      console.log(`   Jogadores: ${room.current_players}/${room.max_players}`);
      
      room.room_participants.forEach(p => {
        console.log(`     - ${p.users.username}`);
      });

      // Verificar matches associadas
      if (room.matches && room.matches.length > 0) {
        const match = room.matches[0];
        console.log(`   Match ID: ${match.id}`);
        console.log(`   Match Status: ${match.status}`);
        console.log(`   Match Criada: ${match.created_at}`);
        
        // Verificar participantes da match
        const participantsWithResults = match.match_participants.filter(p => 
          p.stats && p.stats.result && p.stats.submitted_at
        );
        
        console.log(`   Resultados submetidos: ${participantsWithResults.length}/${match.match_participants.length}`);
        
        match.match_participants.forEach(p => {
          const hasResult = p.stats && p.stats.result;
          console.log(`     ${hasResult ? '✅' : '❌'} ${p.users.username}: ${hasResult ? p.stats.result : 'sem resultado'}`);
        });

        // Determinar se deve ser limpa
        const roomAge = new Date() - new Date(room.created_at);
        const roomAgeMinutes = Math.floor(roomAge / (1000 * 60));
        
        console.log(`   Idade da room: ${roomAgeMinutes} minutos`);
        
        if (roomAgeMinutes > 5 || participantsWithResults.length > 0) {
          console.log(`   🧹 MARCADA PARA LIMPEZA`);
          roomsToCleanup.push({
            roomId: room.id,
            matchId: match.id,
            participantsWithResults,
            totalParticipants: match.match_participants.length,
            ageMinutes: roomAgeMinutes
          });
        } else {
          console.log(`   ⏳ Muito recente, aguardando...`);
        }
      } else {
        console.log(`   ❌ Sem match associada - MARCADA PARA LIMPEZA`);
        roomsToCleanup.push({
          roomId: room.id,
          matchId: null,
          participantsWithResults: [],
          totalParticipants: 0,
          ageMinutes: Math.floor((new Date() - new Date(room.created_at)) / (1000 * 60))
        });
      }
      
      console.log('');
    });

    if (roomsToCleanup.length === 0) {
      console.log('✅ Nenhuma room precisa ser limpa no momento.');
      return;
    }

    console.log(`🧹 Limpando ${roomsToCleanup.length} rooms...\n`);

    let cleanedCount = 0;
    let errorCount = 0;

    for (const roomData of roomsToCleanup) {
      try {
        console.log(`Limpando room ${roomData.roomId}...`);
        
        if (roomData.matchId) {
          // Finalizar match se existir
          let winnerId = null;
          
          if (roomData.participantsWithResults.length > 0) {
            // Determinar vencedor baseado em quem submeteu resultado
            const winnerParticipant = roomData.participantsWithResults.find(p => 
              p.stats && p.stats.result === 'win'
            ) || roomData.participantsWithResults[0];
            
            winnerId = winnerParticipant.user_id;
            console.log(`  🏆 Vencedor determinado: ${winnerId}`);
          }

          // Atualizar match
          const { error: matchError } = await supabase
            .from('matches')
            .update({
              status: 'completed',
              winner_user_id: winnerId,
              finished_at: new Date().toISOString()
            })
            .eq('id', roomData.matchId);

          if (matchError) {
            console.error(`  ❌ Erro ao finalizar match: ${matchError.message}`);
          } else {
            console.log(`  ✅ Match finalizada`);
          }

          // Atualizar colocação do vencedor se houver
          if (winnerId) {
            await supabase
              .from('match_participants')
              .update({ placement: 1 })
              .eq('match_id', roomData.matchId)
              .eq('user_id', winnerId);
            
            console.log(`  ✅ Colocação do vencedor atualizada`);
          }
        }

        // Finalizar room
        const { error: roomError } = await supabase
          .from('game_rooms')
          .update({
            status: 'finished',
            finished_at: new Date().toISOString()
          })
          .eq('id', roomData.roomId);

        if (roomError) {
          console.error(`  ❌ Erro ao finalizar room: ${roomError.message}`);
          errorCount++;
        } else {
          console.log(`  ✅ Room finalizada`);
          cleanedCount++;
        }

        // Pequena pausa entre limpezas
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        console.error(`❌ Erro ao limpar room ${roomData.roomId}:`, error);
        errorCount++;
      }
    }

    console.log(`\n📊 Resultado da limpeza:`);
    console.log(`✅ Rooms limpas: ${cleanedCount}`);
    console.log(`❌ Erros: ${errorCount}`);
    console.log(`📈 Total processado: ${cleanedCount + errorCount}`);

    if (cleanedCount > 0) {
      console.log('\n🎉 Limpeza concluída com sucesso!');
      console.log('💡 Jogadores que estavam presos agora podem criar novas partidas');
    }

  } catch (error) {
    console.error('❌ Erro durante limpeza:', error);
  }
}

// Executar limpeza
cleanupStuckRooms()
  .then(() => {
    console.log('\n✅ Processo de limpeza concluído!');
    console.log('🔄 A nova funcionalidade de auto-finalização evitará esse problema no futuro');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

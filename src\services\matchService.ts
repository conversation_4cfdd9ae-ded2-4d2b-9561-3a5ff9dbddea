// Tipos de dados
export interface Match {
  id: string;
  tournamentId: string;
  tournamentName: string;
  round: number;
  player1: {
    id: string;
    name: string;
    avatar: string;
    score?: number;
  };
  player2: {
    id: string;
    name: string;
    avatar: string;
    score?: number;
  };
  scheduledTime: string;
  status: 'scheduled' | 'in_progress' | 'completed';
  isCurrentPlayerMatch: boolean;
}

// Dados mockados para próximas partidas
const mockMatches: Match[] = [
  {
    id: 'm1',
    tournamentId: '1',
    tournamentName: 'CS2 Pro League - Season 1',
    round: 1,
    player1: {
      id: 'current_player',
      name: '<PERSON><PERSON>uá<PERSON>',
      avatar: 'https://i.pravatar.cc/150?img=1',
    },
    player2: {
      id: 'opponent',
      name: 'Jogador 2',
      avatar: 'https://i.pravatar.cc/150?img=2',
    },
    scheduledTime: new Date(Date.now() + 1000 * 60 * 60 * 2).toISOString(), // 2 horas no futuro
    status: 'scheduled',
    isCurrentPlayerMatch: true
  },
  {
    id: 'm2',
    tournamentId: '1',
    tournamentName: 'CS2 Pro League - Season 1',
    round: 2,
    player1: {
      id: 'current_player',
      name: 'Seu Usuário',
      avatar: 'https://i.pravatar.cc/150?img=1',
    },
    player2: {
      id: 'p3',
      name: 'Jogador 3',
      avatar: 'https://i.pravatar.cc/150?img=3',
    },
    scheduledTime: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2).toISOString(), // 2 dias no futuro
    status: 'scheduled',
    isCurrentPlayerMatch: true
  },
  {
    id: 'm3',
    tournamentId: '2',
    tournamentName: 'FIFA 24 Championship',
    round: 1,
    player1: {
      id: 'p4',
      name: 'Jogador 4',
      avatar: 'https://i.pravatar.cc/150?img=4',
    },
    player2: {
      id: 'current_player',
      name: 'Seu Usuário',
      avatar: 'https://i.pravatar.cc/150?img=1',
    },
    scheduledTime: new Date(Date.now() + 1000 * 60 * 60 * 24).toISOString(), // 1 dia no futuro
    status: 'scheduled',
    isCurrentPlayerMatch: true
  }
];

/**
 * Obtém a próxima partida do jogador atual
 * @returns A próxima partida agendada ou null se não houver
 */
export const getNextMatch = (): Match | null => {
  // Verificar se o usuário optou por não mostrar lembretes
  if (localStorage.getItem('dontShowMatchReminder') === 'true') {
    return null;
  }
  
  // Obter partidas ocultas
  const hiddenMatches = JSON.parse(localStorage.getItem('hiddenMatchReminders') || '[]');
  
  // Filtrar partidas agendadas que não estão ocultas
  const upcomingMatches = mockMatches
    .filter(match => 
      match.status === 'scheduled' && 
      match.isCurrentPlayerMatch &&
      !hiddenMatches.includes(match.id)
    )
    .sort((a, b) => new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime());
  
  // Retornar a próxima partida ou null se não houver
  return upcomingMatches.length > 0 ? upcomingMatches[0] : null;
};

/**
 * Obtém todas as partidas do jogador atual
 * @returns Lista de todas as partidas do jogador
 */
export const getAllPlayerMatches = (): Match[] => {
  return mockMatches.filter(match => match.isCurrentPlayerMatch);
};

/**
 * Marca uma partida como vista para não mostrar o lembrete novamente
 * @param matchId ID da partida
 */
export const markMatchAsSeen = (matchId: string): void => {
  const hiddenMatches = JSON.parse(localStorage.getItem('hiddenMatchReminders') || '[]');
  if (!hiddenMatches.includes(matchId)) {
    hiddenMatches.push(matchId);
    localStorage.setItem('hiddenMatchReminders', JSON.stringify(hiddenMatches));
  }
};

// Importando o módulo express
const express = require('express');

// Importando o módulo http
const http = require('http');

// Criando a aplicação Express
const app = express();

// Criando o servidor HTTP utilizando a aplicação Express
const server = http.createServer(app);

// Definindo uma rota para a raiz do servidor
app.get('/', (req, res) => {
  res.send('Hello World'); // Envia "Hello World" como resposta
});

// Função para iniciar o servidor
const startServer = async () => {
  try {
    // Iniciando o servidor
    server.listen(3002, 'localhost', () => {
      console.log(`Server running on localhost:3002`);
    });
  } catch (error) {
    console.log('Failed to start server:', error);
    process.exit(1);
  }
};

// Iniciando o servidor
startServer();
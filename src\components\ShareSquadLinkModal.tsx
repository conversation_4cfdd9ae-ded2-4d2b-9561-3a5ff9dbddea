import React, { useState, useEffect } from 'react';
import { X, Copy, CheckCircle, Share2, Users, Mail, MessageCircle } from 'lucide-react';
import XLogo from './icons/XLogo';
import InstagramLogo from './icons/InstagramLogo';

interface ShareSquadLinkModalProps {
  onClose: () => void;
  squadId: string;
  squadName: string;
  gameId: string;
  gameName: string;
}

export default function ShareSquadLinkModal({
  onClose,
  squadId,
  squadName,
  gameId,
  gameName
}: ShareSquadLinkModalProps) {
  const [copied, setCopied] = useState(false);
  const [instagramCopied, setInstagramCopied] = useState(false);
  const [inviteLink, setInviteLink] = useState('');

  useEffect(() => {
    // Gerar link de convite
    const baseUrl = window.location.origin;
    const link = `${baseUrl}/squad/${squadId}/join?ref=invite`;
    setInviteLink(link);
  }, [squadId]);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(inviteLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 3000);
  };

  const handleShare = (platform: string) => {
    let shareUrl = '';
    const text = `Junte-se ao meu squad "${squadName}" no jogo ${gameName}!`;

    switch (platform) {
      case 'instagram':
        // Instagram não tem API direta de compartilhamento, então copiamos para a área de transferência
        navigator.clipboard.writeText(`${text} ${inviteLink}`);
        setInstagramCopied(true);
        setTimeout(() => setInstagramCopied(false), 3000);
        return;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(inviteLink)}&text=${encodeURIComponent(text)}`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodeURIComponent(`${text} ${inviteLink}`)}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=${encodeURIComponent(`Convite para squad de ${gameName}`)}&body=${encodeURIComponent(`${text}\n\n${inviteLink}`)}`;
        break;
      default:
        return;
    }

    window.open(shareUrl, '_blank');
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-xl w-full max-w-md relative overflow-hidden">
        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-white font-bold text-lg">Convidar para o Squad</h2>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          <div className="text-center">
            <div className="bg-indigo-500/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-indigo-400" />
            </div>
            <h3 className="text-white font-bold text-lg mb-1">{squadName}</h3>
            <p className="text-gray-400">{gameName}</p>
          </div>

          <div>
            <label className="block text-gray-400 text-sm mb-2">
              Link de convite
            </label>
            <div className="bg-gray-700/50 backdrop-blur-sm border border-white/10 rounded-lg p-3 flex items-center justify-between">
              <span className="text-white text-sm truncate mr-2">{inviteLink}</span>
              <button
                onClick={handleCopyLink}
                className="bg-white/20 hover:bg-white/30 transition-colors px-3 py-1.5 rounded-md flex items-center gap-2"
              >
                {copied ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white text-sm font-medium">Copiado!</span>
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4 text-white" />
                    <span className="text-white text-sm font-medium">Copiar</span>
                  </>
                )}
              </button>
            </div>
          </div>

          <div>
            <label className="block text-gray-400 text-sm mb-2">
              Compartilhar via
            </label>
            <div className="grid grid-cols-4 gap-3">
              <button
                onClick={() => handleShare('instagram')}
                className="bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 hover:opacity-90 transition-opacity p-3 rounded-lg flex flex-col items-center gap-1 relative"
              >
                {instagramCopied && (
                  <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs py-1 px-2 rounded whitespace-nowrap">
                    Copiado! Cole no Instagram
                  </div>
                )}
                <InstagramLogo className="w-6 h-6 text-white" />
                <span className="text-white text-xs">Instagram</span>
              </button>
              <button
                onClick={() => handleShare('twitter')}
                className="bg-black hover:bg-gray-800 transition-colors p-3 rounded-lg flex flex-col items-center gap-1"
              >
                <XLogo className="w-6 h-6 text-white" />
                <span className="text-white text-xs">X</span>
              </button>
              <button
                onClick={() => handleShare('whatsapp')}
                className="bg-green-500 hover:bg-green-600 transition-colors p-3 rounded-lg flex flex-col items-center gap-1"
              >
                <MessageCircle className="w-6 h-6 text-white" />
                <span className="text-white text-xs">WhatsApp</span>
              </button>
              <button
                onClick={() => handleShare('email')}
                className="bg-gray-600 hover:bg-gray-700 transition-colors p-3 rounded-lg flex flex-col items-center gap-1"
              >
                <Mail className="w-6 h-6 text-white" />
                <span className="text-white text-xs">Email</span>
              </button>
            </div>
          </div>

          <div className="bg-indigo-500/10 border border-indigo-500/20 rounded-lg p-4">
            <p className="text-gray-300 text-sm">
              Compartilhe este link com seus amigos para que eles possam se juntar ao seu squad. O link expira em 24 horas.
            </p>
          </div>

          <button
            onClick={onClose}
            className="w-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-semibold py-2 rounded-lg hover:opacity-90 transition-opacity flex items-center justify-center gap-2"
          >
            <Share2 className="w-4 h-4" />
            Fechar
          </button>
        </div>
      </div>
    </div>
  );
}

import { apiService } from './apiService';

// Tipos para conquistas
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'matches' | 'wins' | 'streak' | 'earnings' | 'social' | 'special';
  difficulty: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  points: number;
  
  // Critérios para desbloquear
  criteria: {
    type: string; // 'total_wins', 'win_streak', 'total_earnings', etc.
    target_value: number;
    current_value?: number;
  };
  
  // Status do usuário
  is_unlocked: boolean;
  unlocked_at?: string;
  progress: number; // 0-100
}

export interface AchievementCategory {
  category: string;
  name: string;
  description: string;
  achievements: Achievement[];
  total_achievements: number;
  unlocked_achievements: number;
  completion_percentage: number;
}

class RealAchievementsService {
  
  /**
   * Busca todas as conquistas do usuário
   */
  async getUserAchievements(): Promise<{
    achievements: Achievement[];
    categories: AchievementCategory[];
    summary: {
      total_achievements: number;
      unlocked_achievements: number;
      total_points: number;
      earned_points: number;
      completion_percentage: number;
      recent_unlocks: Achievement[];
    };
  }> {
    console.log('🏆 Buscando conquistas do usuário...');
    
    const response = await apiService.get<any>('/achievements');
    
    if (response.success && response.data) {
      console.log(`✅ ${response.data.achievements.length} conquistas carregadas`);
      return response.data;
    }
    
    throw new Error(response.error || 'Erro ao buscar conquistas');
  }

  /**
   * Busca conquistas por categoria
   */
  async getAchievementsByCategory(category: string): Promise<Achievement[]> {
    console.log(`🎯 Buscando conquistas da categoria: ${category}`);
    
    const response = await apiService.get<Achievement[]>(`/achievements/category/${category}`);
    
    if (response.success && response.data) {
      console.log(`✅ ${response.data.length} conquistas da categoria carregadas`);
      return response.data;
    }
    
    throw new Error(response.error || 'Erro ao buscar conquistas da categoria');
  }

  /**
   * Busca conquistas recentes desbloqueadas
   */
  async getRecentAchievements(limit: number = 5): Promise<Achievement[]> {
    console.log('🆕 Buscando conquistas recentes...');
    
    const response = await apiService.get<Achievement[]>('/achievements/recent', {
      params: { limit }
    });
    
    if (response.success && response.data) {
      console.log(`✅ ${response.data.length} conquistas recentes carregadas`);
      return response.data;
    }
    
    throw new Error(response.error || 'Erro ao buscar conquistas recentes');
  }

  /**
   * Busca progresso de uma conquista específica
   */
  async getAchievementProgress(achievementId: string): Promise<{
    achievement: Achievement;
    progress_history: Array<{
      date: string;
      value: number;
      increment: number;
    }>;
    estimated_completion?: string;
  }> {
    console.log(`📈 Buscando progresso da conquista: ${achievementId}`);
    
    const response = await apiService.get<any>(`/achievements/${achievementId}/progress`);
    
    if (response.success && response.data) {
      console.log('✅ Progresso da conquista carregado');
      return response.data;
    }
    
    throw new Error(response.error || 'Erro ao buscar progresso da conquista');
  }

  /**
   * Gera dados mock para conquistas (temporário até implementar a API)
   */
  getMockAchievements(): Achievement[] {
    return [
      {
        id: '1',
        name: 'Guerreiro Implacável',
        description: 'Vença 50 partidas seguidas',
        icon: 'swords',
        category: 'wins',
        difficulty: 'gold',
        points: 500,
        criteria: {
          type: 'win_streak',
          target_value: 50,
          current_value: 40
        },
        is_unlocked: false,
        progress: 80
      },
      {
        id: '2',
        name: 'Mestre Estrategista',
        description: 'Vença 100 partidas de jogos de estratégia',
        icon: 'brain',
        category: 'matches',
        difficulty: 'platinum',
        points: 1000,
        criteria: {
          type: 'strategy_wins',
          target_value: 100,
          current_value: 100
        },
        is_unlocked: true,
        unlocked_at: '2024-01-15T10:30:00Z',
        progress: 100
      },
      {
        id: '3',
        name: 'Em Chamas!',
        description: 'Mantenha uma sequência de 7 dias jogando',
        icon: 'flame',
        category: 'streak',
        difficulty: 'silver',
        points: 250,
        criteria: {
          type: 'daily_streak',
          target_value: 7,
          current_value: 4
        },
        is_unlocked: false,
        progress: 57
      }
    ];
  }
}

export const realAchievementsService = new RealAchievementsService();

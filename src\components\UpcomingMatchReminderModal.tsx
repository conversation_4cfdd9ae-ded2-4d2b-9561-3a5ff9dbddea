import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Calendar, Clock, Trophy, X, Bell, ChevronRight } from 'lucide-react';

interface Match {
  id: string;
  tournamentId: string;
  tournamentName: string;
  round: number;
  player1: {
    id: string;
    name: string;
    avatar: string;
  };
  player2: {
    id: string;
    name: string;
    avatar: string;
  };
  scheduledTime: string;
}

interface UpcomingMatchReminderModalProps {
  onClose: () => void;
  match: Match;
}

export default function UpcomingMatchReminderModal({ onClose, match }: UpcomingMatchReminderModalProps) {
  const navigate = useNavigate();
  const [dontShowAgain, setDontShowAgain] = useState(false);

  // Formatar data e hora
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Verificar se a partida é hoje
  const isToday = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  // Verificar se a partida é amanhã
  const isTomorrow = (dateString: string) => {
    const date = new Date(dateString);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return (
      date.getDate() === tomorrow.getDate() &&
      date.getMonth() === tomorrow.getMonth() &&
      date.getFullYear() === tomorrow.getFullYear()
    );
  };

  // Obter label da data (Hoje, Amanhã ou data formatada)
  const getDateLabel = (dateString: string) => {
    if (isToday(dateString)) return 'Hoje';
    if (isTomorrow(dateString)) return 'Amanhã';
    return formatDate(dateString);
  };

  // Calcular tempo restante até a partida
  const getTimeRemaining = (dateString: string) => {
    const now = new Date();
    const matchTime = new Date(dateString);
    const diffMs = matchTime.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Agora';
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 24) {
      const diffDays = Math.floor(diffHours / 24);
      return `em ${diffDays} ${diffDays === 1 ? 'dia' : 'dias'}`;
    }
    
    if (diffHours > 0) {
      return `em ${diffHours}h ${diffMinutes}min`;
    }
    
    return `em ${diffMinutes} ${diffMinutes === 1 ? 'minuto' : 'minutos'}`;
  };

  const handleClose = () => {
    if (dontShowAgain) {
      // Salvar preferência do usuário para não mostrar novamente
      localStorage.setItem('dontShowMatchReminder', 'true');
      
      // Salvar o ID da partida atual para não mostrar este lembrete específico novamente
      const hiddenMatches = JSON.parse(localStorage.getItem('hiddenMatchReminders') || '[]');
      if (!hiddenMatches.includes(match.id)) {
        hiddenMatches.push(match.id);
        localStorage.setItem('hiddenMatchReminders', JSON.stringify(hiddenMatches));
      }
    }
    
    onClose();
  };

  const handleGoToTournament = () => {
    // Salvar a aba 'overview' como ativa antes de navegar
    localStorage.setItem(`tournament_${match.tournamentId}_tab`, 'overview');
    navigate(`/tournaments/${match.tournamentId}`);
    onClose();
  };

  const handleGoToMatches = () => {
    navigate('/player-matches');
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fadeIn">
      <div className="bg-gray-800/90 backdrop-blur-sm border border-indigo-500/20 rounded-xl w-full max-w-md shadow-xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-white" />
            <h3 className="text-white font-bold">Lembrete de Partida</h3>
          </div>
          <button 
            onClick={handleClose}
            className="text-white/80 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-5 space-y-4">
          <div className="text-center">
            <p className="text-gray-300 mb-1">Você está participando do torneio</p>
            <h4 className="text-white text-lg font-bold">{match.tournamentName}</h4>
            <div className="mt-2 inline-block bg-indigo-500/20 text-indigo-400 px-3 py-1 rounded-lg text-sm">
              Round {match.round}
            </div>
          </div>
          
          <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 mt-4">
            <div className="text-center mb-3">
              <h5 className="text-white font-bold">Sua próxima partida {getTimeRemaining(match.scheduledTime)}</h5>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <img
                  src={match.player1.avatar}
                  alt={match.player1.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <div>
                  <span className="text-indigo-400 font-medium">{match.player1.name}</span>
                  <p className="text-gray-400 text-xs">Você</p>
                </div>
              </div>
              
              <span className="text-white font-bold">VS</span>
              
              <div className="flex items-center gap-2">
                <div>
                  <span className="text-white font-medium">{match.player2.name}</span>
                  <p className="text-gray-400 text-xs text-right">Oponente</p>
                </div>
                <img
                  src={match.player2.avatar}
                  alt={match.player2.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
              </div>
            </div>
            
            <div className="flex justify-center items-center gap-4 mt-4">
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4 text-indigo-400" />
                <span className="text-white text-sm">{getDateLabel(match.scheduledTime)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4 text-indigo-400" />
                <span className="text-white text-sm">{formatTime(match.scheduledTime)}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center mt-4">
            <input
              type="checkbox"
              id="dontShowAgain"
              checked={dontShowAgain}
              onChange={(e) => setDontShowAgain(e.target.checked)}
              className="w-4 h-4 rounded border-gray-600 bg-gray-700 text-indigo-500 focus:ring-indigo-500"
            />
            <label htmlFor="dontShowAgain" className="ml-2 text-sm text-gray-300">
              Não mostrar novamente
            </label>
          </div>
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t border-gray-700 flex flex-col sm:flex-row gap-2">
          <button
            onClick={handleGoToTournament}
            className="flex-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-2 rounded-lg hover:opacity-90 flex items-center justify-center gap-1"
          >
            <Trophy className="w-4 h-4" />
            Ver Torneio
          </button>
          <button
            onClick={handleGoToMatches}
            className="flex-1 bg-gray-700 text-white font-bold py-2 rounded-lg hover:bg-gray-600 flex items-center justify-center gap-1"
          >
            <Calendar className="w-4 h-4" />
            Todas as Partidas
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

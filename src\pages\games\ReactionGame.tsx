import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Timer, Trophy, RotateCcw, ArrowLeft } from 'lucide-react';

type GameState = 'waiting' | 'ready' | 'click' | 'result' | 'finished';

interface Result {
  reactionTime: number;
  timestamp: number;
}

export default function ReactionGame() {
  const navigate = useNavigate();
  const [gameState, setGameState] = useState<GameState>('waiting');
  const [startTime, setStartTime] = useState(0);
  const [results, setResults] = useState<Result[]>([]);
  const [bestTime, setBestTime] = useState<number | null>(null);
  const [countdown, setCountdown] = useState(3);

  // Obter nome do jogador do cache
  const playerName = localStorage.getItem('playerName') || 'Jogador';

  const ROUNDS = 5;
  const MIN_DELAY = 1000; // 1 segundo
  const MAX_DELAY = 4000; // 4 segundos

  const startRound = useCallback(() => {
    setGameState('ready');
    // Tempo aleatório entre MIN_DELAY e MAX_DELAY
    const delay = MIN_DELAY + Math.random() * (MAX_DELAY - MIN_DELAY);
    setTimeout(() => {
      setStartTime(Date.now());
      setGameState('click');
    }, delay);
  }, []);

  const handleClick = () => {
    if (gameState === 'ready') {
      // Clicou muito cedo
      setResults(prev => [...prev, { reactionTime: -1, timestamp: Date.now() }]);
      if (results.length + 1 >= ROUNDS) {
        setGameState('finished');
      } else {
        setGameState('waiting');
        setTimeout(startRound, 1000);
      }
    } else if (gameState === 'click') {
      const endTime = Date.now();
      const reactionTime = endTime - startTime;
      const newResults = [...results, { reactionTime, timestamp: endTime }];
      setResults(newResults);

      // Atualiza o melhor tempo
      const validTimes = newResults.filter(r => r.reactionTime > 0).map(r => r.reactionTime);
      if (validTimes.length > 0) {
        const newBestTime = Math.min(...validTimes);
        setBestTime(prev => prev === null ? newBestTime : Math.min(prev, newBestTime));
      }

      if (newResults.length >= ROUNDS) {
        setGameState('finished');

        // Salvar resultado no localStorage
        const gameResult = {
          playerName,
          game: 'reaction',
          score: bestTime || Math.min(...validTimes),
          timestamp: Date.now(),
          details: {
            averageTime: validTimes.length > 0 ? validTimes.reduce((acc, curr) => acc + curr, 0) / validTimes.length : 0,
            bestTime: bestTime || Math.min(...validTimes),
            attempts: newResults.length,
            validAttempts: validTimes.length
          }
        };

        const savedResults = JSON.parse(localStorage.getItem('gameResults') || '[]');
        savedResults.push(gameResult);
        localStorage.setItem('gameResults', JSON.stringify(savedResults));

        console.log('🎮 Resultado do jogo salvo:', gameResult);
      } else {
        setGameState('waiting');
        setTimeout(startRound, 1000);
      }
    }
  };

  // Contagem regressiva inicial
  useEffect(() => {
    if (gameState === 'waiting' && results.length === 0) {
      const interval = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            startRound();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [gameState, results.length, startRound]);

  const getBackgroundColor = () => {
    switch (gameState) {
      case 'waiting':
        return 'bg-gray-800';
      case 'ready':
        return 'bg-yellow-400';
      case 'click':
        return 'bg-green-500';
      default:
        return 'bg-gray-800';
    }
  };

  const getMessage = () => {
    switch (gameState) {
      case 'waiting':
        return results.length === 0
          ? `Começando em ${countdown}...`
          : 'Prepare-se...';
      case 'ready':
        return 'Aguarde o verde...';
      case 'click':
        return 'CLIQUE AGORA!';
      case 'finished':
        const validTimes = results.filter(r => r.reactionTime > 0);
        const avgTime = validTimes.length > 0
          ? validTimes.reduce((acc, curr) => acc + curr.reactionTime, 0) / validTimes.length
          : 0;
        return `Média: ${avgTime.toFixed(0)}ms`;
      default:
        return '';
    }
  };

  if (gameState === 'finished') {
    return (
      <div className="min-h-screen bg-gray-900 p-6 flex flex-col items-center justify-center">
        <div className="w-full max-w-md space-y-6">
          <div className="text-center space-y-2">
            <Trophy className="w-16 h-16 text-yellow-400 mx-auto" />
            <h1 className="text-2xl font-bold text-white">Resultados</h1>
            <p className="text-gray-400">Parabéns, {playerName}!</p>
            <p className="text-gray-500 text-sm">Seus tempos de reação</p>
          </div>

          <div className="bg-gray-800 rounded-xl p-4 space-y-4">
            {results.map((result, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-700 rounded-lg"
              >
                <div>
                  <p className="text-white font-semibold">Tentativa {index + 1}</p>
                  <p className={`text-sm ${result.reactionTime < 0 ? 'text-red-400' : 'text-gray-400'}`}>
                    {result.reactionTime < 0 ? 'Muito cedo!' : `${result.reactionTime}ms`}
                  </p>
                </div>
                {bestTime === result.reactionTime && result.reactionTime > 0 && (
                  <div className="bg-yellow-400/20 text-yellow-400 text-sm px-2 py-1 rounded">
                    Melhor tempo!
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex gap-3">
            <button
              onClick={() => navigate('/games/reaction')}
              className="flex-1 bg-gray-800 text-white font-semibold rounded-xl py-3 flex items-center justify-center gap-2"
            >
              <ArrowLeft className="w-5 h-5" />
              Voltar
            </button>
            <button
              onClick={() => {
                setGameState('waiting');
                setResults([]);
                setBestTime(null);
                setCountdown(3);
              }}
              className="flex-1 bg-yellow-400 text-black font-semibold rounded-xl py-3 flex items-center justify-center gap-2"
            >
              <RotateCcw className="w-5 h-5" />
              Jogar Novamente
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Timer className="w-6 h-6 text-yellow-400" />
          <h1 className="text-xl font-bold text-white">Teste de Reação</h1>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-400">Melhor Tempo</p>
          <p className="text-lg font-bold text-yellow-400">
            {bestTime ? `${bestTime}ms` : '--'}
          </p>
        </div>
      </div>

      {/* Game Area */}
      <button
        onClick={handleClick}
        disabled={gameState === 'waiting'}
        className={`w-full h-96 rounded-xl transition-colors duration-200 flex items-center justify-center ${getBackgroundColor()}`}
      >
        <div className="text-center">
          <p className="text-2xl font-bold text-white mb-2">{getMessage()}</p>
          <p className="text-gray-300">
            Tentativa {results.length + 1} de {ROUNDS}
          </p>
        </div>
      </button>

      {/* Instructions */}
      <div className="mt-6 bg-gray-800 rounded-xl p-4">
        <h2 className="text-white font-semibold mb-2">Como Jogar</h2>
        <p className="text-gray-400 text-sm">
          1. Aguarde o quadrado ficar amarelo<br />
          2. Quando ficar verde, clique o mais rápido possível<br />
          3. Não clique antes do verde aparecer!
        </p>
      </div>
    </div>
  );
}
import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, MatchCreationData, MatchResultData } from '../types/api';
import { logger, logBusinessEvent, logSecurityEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';

/**
 * Get matches with filters
 */
export const getMatches = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { page, limit } = extractPagination(req.query);
    const { status, game_id, tournament_id, user_id } = req.query;

    let query = supabaseAdmin
      .from('matches')
      .select(`
        id, status, match_type, entry_fee, total_prize, scheduled_time,
        started_at, finished_at, created_at,
        games (id, name, slug, icon_url),
        tournaments (id, title),
        match_participants (
          user_id, score, placement, earnings,
          users (username, display_name, avatar_url)
        )
      `, { count: 'exact' });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (game_id) {
      query = query.eq('game_id', game_id);
    }
    if (tournament_id) {
      query = query.eq('tournament_id', tournament_id);
    }
    if (user_id) {
      query = query.eq('match_participants.user_id', user_id);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: matches, error, count } = await query;

    if (error) {
      logger.error('Failed to get matches:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve matches'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        matches: matches || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get matches error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Create a new match
 */
export const createMatch = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const matchData: MatchCreationData = req.body;

    // Verify game exists
    const { data: game, error: gameError } = await supabaseAdmin
      .from('games')
      .select('id, name, is_active, min_players, max_players')
      .eq('id', matchData.game_id)
      .eq('is_active', true)
      .single();

    if (gameError || !game) {
      const response: ApiResponse = {
        success: false,
        error: 'Game not found or inactive'
      };
      res.status(404).json(response);
      return;
    }

    // Validate participants count
    if (matchData.participants.length < game.min_players || matchData.participants.length > game.max_players) {
      const response: ApiResponse = {
        success: false,
        error: `Match must have between ${game.min_players} and ${game.max_players} players`
      };
      res.status(400).json(response);
      return;
    }

    // Check if user has sufficient balance for entry fee
    if (matchData.entry_fee > 0) {
      const { data: wallet } = await supabaseAdmin
        .from('wallets')
        .select('balance, frozen_balance')
        .eq('user_id', user.id)
        .single();

      if (!wallet || (wallet.balance - wallet.frozen_balance) < matchData.entry_fee) {
        const response: ApiResponse = {
          success: false,
          error: 'Insufficient balance'
        };
        res.status(400).json(response);
        return;
      }
    }

    // Create match
    const { data: match, error: matchError } = await supabaseAdmin
      .from('matches')
      .insert({
        game_id: matchData.game_id,
        room_id: matchData.room_id,
        tournament_id: matchData.tournament_id,
        match_type: matchData.match_type,
        entry_fee: matchData.entry_fee,
        total_prize: matchData.entry_fee * matchData.participants.length,
        game_settings: matchData.game_settings || {},
        scheduled_time: matchData.scheduled_time
      })
      .select('*')
      .single();

    if (matchError || !match) {
      logger.error('Failed to create match:', matchError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create match'
      };
      res.status(500).json(response);
      return;
    }

    // Add participants
    const participants = matchData.participants.map(userId => ({
      match_id: match.id,
      user_id: userId
    }));

    const { error: participantsError } = await supabaseAdmin
      .from('match_participants')
      .insert(participants);

    if (participantsError) {
      // Rollback match creation
      await supabaseAdmin.from('matches').delete().eq('id', match.id);

      logger.error('Failed to add match participants:', participantsError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to add match participants'
      };
      res.status(500).json(response);
      return;
    }

    // Freeze entry fees for all participants
    if (matchData.entry_fee > 0) {
      for (const userId of matchData.participants) {
        await supabaseAdmin
          .from('wallets')
          .update({
            frozen_balance: matchData.entry_fee // Will be handled by trigger or manual calculation
          })
          .eq('user_id', userId);
      }
    }

    logBusinessEvent('match_created', user.id, {
      match_id: match.id,
      game_id: matchData.game_id,
      entry_fee: matchData.entry_fee,
      participants_count: matchData.participants.length
    });

    const response: ApiResponse = {
      success: true,
      message: 'Match created successfully',
      data: {
        match: {
          id: match.id,
          status: match.status,
          match_type: match.match_type,
          entry_fee: match.entry_fee,
          total_prize: match.total_prize,
          scheduled_time: match.scheduled_time,
          created_at: match.created_at
        }
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Create match error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get match by ID
 */
export const getMatchById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    logger.info(`Getting match by ID: ${id}, User: ${currentUser?.username || 'anonymous'}`);

    // First, try to get the basic match data
    const { data: match, error } = await supabaseAdmin
      .from('matches')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      logger.error(`Database error when fetching match ${id}:`, error);
      const response: ApiResponse = {
        success: false,
        error: 'Match not found'
      };
      res.status(404).json(response);
      return;
    }

    if (!match) {
      logger.warn(`Match ${id} not found in database`);
      const response: ApiResponse = {
        success: false,
        error: 'Match not found'
      };
      res.status(404).json(response);
      return;
    }

    // Now get the related data
    const { data: participants } = await supabaseAdmin
      .from('match_participants')
      .select(`
        user_id, score, placement, earnings,
        users (username, display_name, avatar_url)
      `)
      .eq('match_id', id);

    const { data: game } = await supabaseAdmin
      .from('games')
      .select('id, name, slug, icon_url, type')
      .eq('id', match.game_id)
      .single();

    // Fix winner_user_id if it's the string "undefined"
    if (match.winner_user_id === "undefined") {
      logger.warn(`⚠️ Found winner_user_id as string "undefined" for match ${id}, correcting...`);

      // Try to determine the correct winner from participants
      const winner = participants?.find(p => p.placement === 1);
      if (winner && winner.user_id) {
        match.winner_user_id = winner.user_id;
        logger.info(`✅ Corrected winner_user_id to: ${winner.user_id}`);

        // Update the database with the correct winner
        await supabaseAdmin
          .from('matches')
          .update({ winner_user_id: winner.user_id })
          .eq('id', id);
      } else {
        match.winner_user_id = null;
        logger.warn(`⚠️ Could not determine winner, setting to null`);

        // Update the database to null
        await supabaseAdmin
          .from('matches')
          .update({ winner_user_id: null })
          .eq('id', id);
      }
    }

    // Attach related data to match
    match.match_participants = participants || [];
    match.games = game;
    match.tournaments = null; // Will be populated if needed
    match.match_disputes = []; // Will be populated if needed

    logger.info(`Match ${id} found. Status: ${match.status}, Participants: ${match.match_participants?.length || 0}`);

    // Debug winner_user_id
    console.log(`🔍 MATCH API DEBUG - Match ${id}:`);
    console.log(`  winner_user_id: ${JSON.stringify(match.winner_user_id)} (type: ${typeof match.winner_user_id})`);
    console.log(`  status: ${match.status}`);

    // Check if user can view this match
    const isParticipant = currentUser && match.match_participants?.some(
      (p: any) => p.user_id === currentUser.id
    );
    const isPublicTournament = match.tournaments && match.tournaments.is_public;

    logger.info(`Access check for match ${id}: isParticipant=${isParticipant}, isPublicTournament=${isPublicTournament}, status=${match.status}`);

    // Allow access if:
    // 1. User is a participant
    // 2. Match is from a public tournament
    // 3. Match is completed (public results)
    // 4. User is not authenticated (public access for completed matches)
    // 5. Match is in progress and user is authenticated (for ongoing matches)
    const allowAccess = !currentUser || // No user (public access)
                       isParticipant || // User is participant
                       isPublicTournament || // Public tournament
                       match.status === 'completed' || // Completed match
                       match.status === 'in_progress'; // In progress match

    if (!allowAccess) {
      logger.warn(`Access denied to match ${id} for user ${currentUser.username}`);
      const response: ApiResponse = {
        success: false,
        error: 'Access denied to this match'
      };
      res.status(403).json(response);
      return;
    }

    logger.info(`Access granted to match ${id}`);

    const response: ApiResponse = {
      success: true,
      data: {
        match
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get match by ID error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Join a match
 */
export const joinMatch = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;

    // Get match details
    const { data: match, error: matchError } = await supabaseAdmin
      .from('matches')
      .select(`
        *,
        games (min_players, max_players),
        match_participants (user_id)
      `)
      .eq('id', id)
      .single();

    if (matchError || !match) {
      const response: ApiResponse = {
        success: false,
        error: 'Match not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check match status
    if (match.status !== 'scheduled') {
      const response: ApiResponse = {
        success: false,
        error: 'Cannot join match that is not scheduled'
      };
      res.status(400).json(response);
      return;
    }

    // Check if user is already a participant
    const isAlreadyParticipant = match.match_participants?.some(
      (p: any) => p.user_id === user.id
    );

    if (isAlreadyParticipant) {
      const response: ApiResponse = {
        success: false,
        error: 'Already a participant in this match'
      };
      res.status(400).json(response);
      return;
    }

    // Check if match is full
    const currentParticipants = match.match_participants?.length || 0;
    if (currentParticipants >= match.games.max_players) {
      const response: ApiResponse = {
        success: false,
        error: 'Match is full'
      };
      res.status(400).json(response);
      return;
    }

    // Check user balance for entry fee
    if (match.entry_fee > 0) {
      const { data: wallet } = await supabaseAdmin
        .from('wallets')
        .select('balance, frozen_balance')
        .eq('user_id', user.id)
        .single();

      if (!wallet || (wallet.balance - wallet.frozen_balance) < match.entry_fee) {
        const response: ApiResponse = {
          success: false,
          error: 'Insufficient balance'
        };
        res.status(400).json(response);
        return;
      }
    }

    // Add user as participant
    const { error: participantError } = await supabaseAdmin
      .from('match_participants')
      .insert({
        match_id: id,
        user_id: user.id
      });

    if (participantError) {
      logger.error('Failed to join match:', participantError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to join match'
      };
      res.status(500).json(response);
      return;
    }

    // Freeze entry fee
    if (match.entry_fee > 0) {
      await supabaseAdmin
        .from('wallets')
        .update({
          frozen_balance: match.entry_fee // Simplified for now
        })
        .eq('user_id', user.id);
    }

    // Update match total prize
    await supabaseAdmin
      .from('matches')
      .update({
        total_prize: match.entry_fee // Will be handled by SQL function
      })
      .eq('id', id);

    logBusinessEvent('match_joined', user.id, {
      match_id: id,
      entry_fee: match.entry_fee
    });

    const response: ApiResponse = {
      success: true,
      message: 'Successfully joined match'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Join match error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Leave a match
 */
export const leaveMatch = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;

    // Get match details
    const { data: match, error: matchError } = await supabaseAdmin
      .from('matches')
      .select('*')
      .eq('id', id)
      .single();

    if (matchError || !match) {
      const response: ApiResponse = {
        success: false,
        error: 'Match not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if match can be left
    if (match.status !== 'scheduled') {
      const response: ApiResponse = {
        success: false,
        error: 'Cannot leave match that has already started'
      };
      res.status(400).json(response);
      return;
    }

    // Remove participant
    const { error: removeError } = await supabaseAdmin
      .from('match_participants')
      .delete()
      .eq('match_id', id)
      .eq('user_id', user.id);

    if (removeError) {
      logger.error('Failed to leave match:', removeError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to leave match'
      };
      res.status(500).json(response);
      return;
    }

    // Unfreeze entry fee
    if (match.entry_fee > 0) {
      await supabaseAdmin
        .from('wallets')
        .update({
          frozen_balance: 0 // Will be handled by SQL function
        })
        .eq('user_id', user.id);
    }

    // Update match total prize
    await supabaseAdmin
      .from('matches')
      .update({
        total_prize: 0 // Will be handled by SQL function
      })
      .eq('id', id);

    logBusinessEvent('match_left', user.id, {
      match_id: id,
      entry_fee: match.entry_fee
    });

    const response: ApiResponse = {
      success: true,
      message: 'Successfully left match'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Leave match error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Submit match result
 */
export const submitMatchResult = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const resultData: MatchResultData = req.body;

    // Get match details
    const { data: match, error: matchError } = await supabaseAdmin
      .from('matches')
      .select(`
        *,
        match_participants (user_id)
      `)
      .eq('id', resultData.match_id)
      .single();

    if (matchError || !match) {
      const response: ApiResponse = {
        success: false,
        error: 'Match not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if user is a participant
    const isParticipant = match.match_participants?.some(
      (p: any) => p.user_id === user.id
    );

    if (!isParticipant) {
      const response: ApiResponse = {
        success: false,
        error: 'Only match participants can submit results'
      };
      res.status(403).json(response);
      return;
    }

    // Check match status
    if (match.status !== 'in_progress') {
      const response: ApiResponse = {
        success: false,
        error: 'Can only submit results for matches in progress'
      };
      res.status(400).json(response);
      return;
    }

    // Update match with result
    const { error: updateError } = await supabaseAdmin
      .from('matches')
      .update({
        status: 'completed',
        winner_user_id: resultData.winner_user_id,
        result_data: resultData.result_data,
        result_image_url: resultData.result_image_url,
        finished_at: new Date().toISOString()
      })
      .eq('id', resultData.match_id);

    if (updateError) {
      logger.error('Failed to update match result:', updateError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to submit match result'
      };
      res.status(500).json(response);
      return;
    }

    // Update participant scores and placements
    for (const participantScore of resultData.participants_scores) {
      await supabaseAdmin
        .from('match_participants')
        .update({
          score: participantScore.score,
          placement: participantScore.placement,
          stats: participantScore.stats
        })
        .eq('match_id', resultData.match_id)
        .eq('user_id', participantScore.user_id);
    }

    // Calculate and distribute earnings
    await distributeMatchEarnings(resultData.match_id, match.prize_pool, resultData.participants_scores);

    logBusinessEvent('match_result_submitted', user.id, {
      match_id: resultData.match_id,
      winner_user_id: resultData.winner_user_id
    });

    const response: ApiResponse = {
      success: true,
      message: 'Match result submitted successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Submit match result error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Dispute match result
 */
export const disputeMatchResult = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;
    const { reason, description, evidence_urls } = req.body;

    // Get match details
    const { data: match, error: matchError } = await supabaseAdmin
      .from('matches')
      .select(`
        *,
        match_participants (user_id)
      `)
      .eq('id', id)
      .single();

    if (matchError || !match) {
      const response: ApiResponse = {
        success: false,
        error: 'Match not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if user is a participant
    const isParticipant = match.match_participants?.some(
      (p: any) => p.user_id === user.id
    );

    if (!isParticipant) {
      const response: ApiResponse = {
        success: false,
        error: 'Only match participants can dispute results'
      };
      res.status(403).json(response);
      return;
    }

    // Check if match is completed
    if (match.status !== 'completed') {
      const response: ApiResponse = {
        success: false,
        error: 'Can only dispute completed matches'
      };
      res.status(400).json(response);
      return;
    }

    // Check if dispute already exists
    const { data: existingDispute } = await supabaseAdmin
      .from('match_disputes')
      .select('id')
      .eq('match_id', id)
      .eq('disputer_user_id', user.id)
      .single();

    if (existingDispute) {
      const response: ApiResponse = {
        success: false,
        error: 'You have already disputed this match'
      };
      res.status(400).json(response);
      return;
    }

    // Create dispute
    const { data: dispute, error: disputeError } = await supabaseAdmin
      .from('match_disputes')
      .insert({
        match_id: id,
        disputer_user_id: user.id,
        reason,
        description,
        evidence_urls: evidence_urls || []
      })
      .select('*')
      .single();

    if (disputeError || !dispute) {
      logger.error('Failed to create match dispute:', disputeError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create dispute'
      };
      res.status(500).json(response);
      return;
    }

    // Update match status to disputed
    await supabaseAdmin
      .from('matches')
      .update({ status: 'disputed' })
      .eq('id', id);

    logBusinessEvent('match_disputed', user.id, {
      match_id: id,
      dispute_id: dispute.id,
      reason
    });

    const response: ApiResponse = {
      success: true,
      message: 'Match dispute submitted successfully',
      data: {
        dispute: {
          id: dispute.id,
          reason: dispute.reason,
          status: dispute.status,
          created_at: dispute.created_at
        }
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Dispute match result error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};



/**
 * Get user's upcoming matches
 */
export const getUpcomingMatches = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);

    const offset = (page - 1) * limit;

    const { data: matches, error, count } = await supabaseAdmin
      .from('matches')
      .select(`
        id, status, match_type, entry_fee, total_prize, scheduled_time,
        created_at,
        games (id, name, slug, icon_url),
        tournaments (id, title),
        match_participants!inner (
          user_id, is_ready
        )
      `, { count: 'exact' })
      .eq('match_participants.user_id', user.id)
      .in('status', ['scheduled', 'in_progress'])
      .order('scheduled_time', { ascending: true })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get upcoming matches:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve upcoming matches'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        matches: matches || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get upcoming matches error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

// Helper function to distribute match earnings
const distributeMatchEarnings = async (matchId: string, prizePool: number, participantScores: any[]) => {
  try {
    // Sort participants by placement
    const sortedParticipants = participantScores.sort((a, b) => a.placement - b.placement);

    // Simple distribution: winner takes 70%, second place 20%, third place 10%
    const distributions = [0.7, 0.2, 0.1];

    for (let i = 0; i < sortedParticipants.length && i < distributions.length; i++) {
      const participant = sortedParticipants[i];
      const earnings = prizePool * distributions[i];

      if (earnings > 0) {
        // Update participant earnings
        await supabaseAdmin
          .from('match_participants')
          .update({ earnings })
          .eq('match_id', matchId)
          .eq('user_id', participant.user_id);

        // Get wallet ID
        const { data: walletData } = await supabaseAdmin
          .from('wallets')
          .select('id')
          .eq('user_id', participant.user_id)
          .single();

        if (walletData) {
          // Create win transaction with pending status first, then update to completed
          // This ensures the trigger works correctly
          const { data: transaction, error: txError } = await supabaseAdmin
            .from('transactions')
            .insert({
              user_id: participant.user_id,
              wallet_id: walletData.id,
              type: 'win',
              amount: earnings,
              description: `Match winnings - Placement ${participant.placement}`,
              status: 'pending'
            })
            .select('id')
            .single();

          if (txError || !transaction) {
            logger.error(`Error creating win transaction for user ${participant.user_id}:`, txError);
          } else {
            // Update to completed to trigger wallet balance update
            const { error: updateError } = await supabaseAdmin
              .from('transactions')
              .update({ status: 'completed' })
              .eq('id', transaction.id);

            if (updateError) {
              logger.error(`Error completing win transaction ${transaction.id}:`, updateError);
            }
          }
        }
      }
    }

    // Unfreeze entry fees for all participants
    const { data: match } = await supabaseAdmin
      .from('matches')
      .select('entry_fee')
      .eq('id', matchId)
      .single();

    if (match?.entry_fee > 0) {
      for (const participant of participantScores) {
        await supabaseAdmin
          .from('wallets')
          .update({
            frozen_balance: 0 // Will be handled by SQL function
          })
          .eq('user_id', participant.user_id);
      }
    }
  } catch (error) {
    logger.error('Error distributing match earnings:', error);
  }
};

/**
 * Get user's match history
 */
export const getMatchHistory = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);
    const { game_id, match_type, status } = req.query;

    // Build query for matches where user participated
    let query = supabaseAdmin
      .from('matches')
      .select(`
        id, game_id, match_type, status, entry_fee, total_prize,
        scheduled_time, started_at, finished_at, winner_user_id, result_data, created_at,
        games (name, slug),
        match_participants!inner (
          score, placement, earnings, stats
        )
      `, { count: 'exact' })
      .eq('match_participants.user_id', user.id);

    // Apply filters
    if (game_id) {
      query = query.eq('game_id', game_id);
    }
    if (match_type) {
      query = query.eq('match_type', match_type);
    }
    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('finished_at', { ascending: false, nullsFirst: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: matches, error, count } = await query;

    if (error) {
      logger.error('Failed to fetch match history:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch match history'
      };
      res.status(500).json(response);
      return;
    }

    // Process matches to add result and points for the user
    const processedMatches = (matches || []).map(match => {
      const participant = match.match_participants[0];

      // Determine result for the user
      let result: 'victory' | 'defeat' | 'draw' = 'defeat';
      if (match.winner_user_id === user.id) {
        result = 'victory';
      } else if (participant.placement === 1) {
        result = 'victory';
      } else if (match.status === 'completed' && participant.placement && participant.placement <= (match.result_data?.total_participants || 2) / 2) {
        result = 'victory';
      }

      // Calculate points based on result and placement
      let points = 0;
      if (result === 'victory') {
        points = Math.max(50, 100 - (participant.placement || 1) * 10);
      } else {
        points = Math.max(10, 30 - (participant.placement || 5) * 5);
      }

      return {
        id: match.id,
        game_name: (match.games as any)?.name || 'Jogo Desconhecido',
        game_slug: (match.games as any)?.slug || 'unknown',
        match_type: match.match_type,
        status: match.status,
        entry_fee: match.entry_fee,
        total_prize: match.total_prize,
        scheduled_time: match.scheduled_time,
        started_at: match.started_at,
        finished_at: match.finished_at,
        winner_user_id: match.winner_user_id,
        result_data: match.result_data,
        created_at: match.created_at,
        participant,
        result,
        points
      };
    });

    const totalPages = Math.ceil((count || 0) / limit);

    const response: ApiResponse = {
      success: true,
      data: {
        matches: processedMatches,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    };

    res.status(200).json(response);
  } catch (error: any) {
    logger.error('Match history error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get user's match statistics
 */
export const getMatchStats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    // Get match statistics
    const { data: stats, error } = await supabaseAdmin
      .from('match_participants')
      .select(`
        matches (status, winner_user_id, finished_at, games (name)),
        placement, earnings
      `)
      .eq('user_id', user.id)
      .eq('matches.status', 'completed');

    if (error) {
      logger.error('Failed to fetch match stats:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to fetch match statistics'
      };
      res.status(500).json(response);
      return;
    }

    // Calculate statistics
    const totalMatches = stats?.length || 0;
    let totalWins = 0;
    let totalLosses = 0;
    let totalDraws = 0;
    let totalEarnings = 0;
    let currentStreak = 0;
    let bestStreak = 0;
    let tempStreak = 0;
    const gameStats: { [key: string]: number } = {};
    const recentPerformance: Array<{ date: string; result: 'win' | 'loss' | 'draw'; game: string }> = [];

    stats?.forEach((stat: any) => {
      const match = stat.matches;
      if (!match) return;

      totalEarnings += stat.earnings || 0;

      // Count game plays
      const gameName = match.games?.name || 'Jogo Desconhecido';
      gameStats[gameName] = (gameStats[gameName] || 0) + 1;

      // Determine result
      let result: 'win' | 'loss' | 'draw' = 'loss';
      if (match.winner_user_id === user.id || stat.placement === 1) {
        result = 'win';
        totalWins++;
        tempStreak++;
        bestStreak = Math.max(bestStreak, tempStreak);
      } else {
        totalLosses++;
        tempStreak = 0;
      }

      // Add to recent performance (last 10)
      if (recentPerformance.length < 10) {
        recentPerformance.push({
          date: match.finished_at,
          result,
          game: gameName
        });
      }
    });

    currentStreak = tempStreak;
    const winRate = totalMatches > 0 ? (totalWins / totalMatches) * 100 : 0;
    const favoriteGame = Object.keys(gameStats).reduce((a, b) => gameStats[a] > gameStats[b] ? a : b, '');

    const response: ApiResponse = {
      success: true,
      data: {
        total_matches: totalMatches,
        total_wins: totalWins,
        total_losses: totalLosses,
        total_draws: totalDraws,
        win_rate: Math.round(winRate * 100) / 100,
        total_earnings: totalEarnings,
        current_streak: currentStreak,
        best_streak: bestStreak,
        favorite_game: favoriteGame || null,
        recent_performance: recentPerformance.reverse() // Most recent first
      }
    };

    res.status(200).json(response);
  } catch (error: any) {
    logger.error('Match stats error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Force finalize a match (for debugging)
 */
export const forceFinalize = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Get match participants
    const { data: participants, error: participantsError } = await supabaseAdmin
      .from('match_participants')
      .select('user_id, placement, stats')
      .eq('match_id', id);

    if (participantsError || !participants) {
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get match participants'
      };
      res.status(400).json(response);
      return;
    }

    // Find winner (placement = 1)
    const winner = participants.find(p => p.placement === 1);
    const winnerId = winner?.user_id || null;

    logger.info(`🔧 Force finalizing match ${id} with winner: ${winnerId}`);
    logger.info(`Participants:`, JSON.stringify(participants, null, 2));

    // Update match
    const updateData = {
      status: 'completed',
      winner_user_id: winnerId,
      finished_at: new Date().toISOString()
    };

    logger.info(`Updating match ${id} with data:`, updateData);

    // Update only the essential fields to avoid trigger issues
    const { error: updateError, data: updateResult } = await supabaseAdmin
      .from('matches')
      .update({
        status: 'completed',
        winner_user_id: winnerId,
        finished_at: new Date().toISOString()
      })
      .eq('id', id)
      .select();

    logger.info(`Update result:`, updateResult);

    if (updateError) {
      logger.error(`Failed to update match ${id}:`, updateError);
      const response: ApiResponse = {
        success: false,
        error: `Failed to update match: ${updateError.message}`
      };
      res.status(500).json(response);
      return;
    }

    logger.info(`✅ Match ${id} force finalized successfully with winner: ${winnerId}`);

    const response: ApiResponse = {
      success: true,
      data: {
        matchId: id,
        winnerId,
        participants
      }
    };
    res.json(response);

  } catch (error: any) {
    logger.error('Error force finalizing match:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};
import { Request, Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse } from '../types/api';
import { logger, logBusinessEvent, logSecurityEvent } from '../utils/logger';
import crypto from 'crypto';
import config from '../config/environment';

/**
 * Handle payment webhooks
 */
export const handlePaymentWebhook = async (req: Request, res: Response): Promise<void> => {
  try {
    const signature = req.headers['x-webhook-signature'] as string;
    const payload = JSON.stringify(req.body);

    // Verify webhook signature
    if (!verifyWebhookSignature(payload, signature, config.PAYMENT_WEBHOOK_SECRET)) {
      logSecurityEvent('invalid_webhook_signature', 'high', {
        source: 'payment_webhook',
        signature,
        ip: req.ip
      });

      const response: ApiResponse = {
        success: false,
        error: 'Invalid webhook signature'
      };
      res.status(401).json(response);
      return;
    }

    const { event_type, data } = req.body;

    switch (event_type) {
      case 'payment.approved':
        await handlePaymentApproved(data);
        break;
      case 'payment.rejected':
        await handlePaymentRejected(data);
        break;
      case 'payment.cancelled':
        await handlePaymentCancelled(data);
        break;
      case 'withdrawal.completed':
        await handleWithdrawalCompleted(data);
        break;
      case 'withdrawal.failed':
        await handleWithdrawalFailed(data);
        break;
      default:
        logger.warn('Unknown payment webhook event:', event_type);
    }

    logBusinessEvent('payment_webhook_processed', 'system', {
      event_type,
      transaction_id: data.transaction_id
    });

    const response: ApiResponse = {
      success: true,
      message: 'Webhook processed successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Payment webhook error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Handle tournament webhooks
 */
export const handleTournamentWebhook = async (req: Request, res: Response): Promise<void> => {
  try {
    const signature = req.headers['x-webhook-signature'] as string;
    const payload = JSON.stringify(req.body);

    // Verify webhook signature
    if (!verifyWebhookSignature(payload, signature, config.TOURNAMENT_WEBHOOK_SECRET)) {
      logSecurityEvent('invalid_webhook_signature', 'high', {
        source: 'tournament_webhook',
        signature,
        ip: req.ip
      });

      const response: ApiResponse = {
        success: false,
        error: 'Invalid webhook signature'
      };
      res.status(401).json(response);
      return;
    }

    const { event_type, data } = req.body;

    switch (event_type) {
      case 'tournament.started':
        await handleTournamentStarted(data);
        break;
      case 'tournament.completed':
        await handleTournamentCompleted(data);
        break;
      case 'tournament.cancelled':
        await handleTournamentCancelled(data);
        break;
      case 'match.completed':
        await handleMatchCompleted(data);
        break;
      default:
        logger.warn('Unknown tournament webhook event:', event_type);
    }

    logBusinessEvent('tournament_webhook_processed', 'system', {
      event_type,
      tournament_id: data.tournament_id
    });

    const response: ApiResponse = {
      success: true,
      message: 'Webhook processed successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Tournament webhook error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

// Helper functions for payment webhooks

const handlePaymentApproved = async (data: any): Promise<void> => {
  try {
    const { transaction_id, amount, user_id } = data;

    // Update transaction status
    const { error: transactionError } = await supabaseAdmin
      .from('transactions')
      .update({
        status: 'completed',
        external_transaction_id: transaction_id,
        completed_at: new Date().toISOString()
      })
      .eq('external_transaction_id', transaction_id);

    if (transactionError) {
      logger.error('Failed to update transaction:', transactionError);
      return;
    }

    // Update wallet balance
    const { error: walletError } = await supabaseAdmin
      .from('wallets')
      .update({
        balance: amount // Will be handled by SQL function
      })
      .eq('user_id', user_id);

    if (walletError) {
      logger.error('Failed to update wallet balance:', walletError);
      return;
    }

    // Create notification
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id,
        type: 'payment',
        title: 'Depósito aprovado',
        message: `Seu depósito de R$ ${amount.toFixed(2)} foi aprovado e creditado em sua conta.`,
        data: {
          transaction_id,
          amount
        }
      });

    logger.info('Payment approved and processed:', { transaction_id, amount, user_id });
  } catch (error) {
    logger.error('Handle payment approved error:', error);
  }
};

const handlePaymentRejected = async (data: any): Promise<void> => {
  try {
    const { transaction_id, reason, user_id } = data;

    // Update transaction status
    const { error: transactionError } = await supabaseAdmin
      .from('transactions')
      .update({
        status: 'failed',
        failure_reason: reason,
        completed_at: new Date().toISOString()
      })
      .eq('external_transaction_id', transaction_id);

    if (transactionError) {
      logger.error('Failed to update transaction:', transactionError);
      return;
    }

    // Create notification
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id,
        type: 'payment',
        title: 'Depósito rejeitado',
        message: `Seu depósito foi rejeitado. Motivo: ${reason}`,
        data: {
          transaction_id,
          reason
        }
      });

    logger.info('Payment rejected:', { transaction_id, reason, user_id });
  } catch (error) {
    logger.error('Handle payment rejected error:', error);
  }
};

const handlePaymentCancelled = async (data: any): Promise<void> => {
  try {
    const { transaction_id, user_id } = data;

    // Update transaction status
    const { error: transactionError } = await supabaseAdmin
      .from('transactions')
      .update({
        status: 'cancelled',
        completed_at: new Date().toISOString()
      })
      .eq('external_transaction_id', transaction_id);

    if (transactionError) {
      logger.error('Failed to update transaction:', transactionError);
      return;
    }

    // Create notification
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id,
        type: 'payment',
        title: 'Depósito cancelado',
        message: 'Seu depósito foi cancelado.',
        data: {
          transaction_id
        }
      });

    logger.info('Payment cancelled:', { transaction_id, user_id });
  } catch (error) {
    logger.error('Handle payment cancelled error:', error);
  }
};

const handleWithdrawalCompleted = async (data: any): Promise<void> => {
  try {
    const { transaction_id, amount, user_id } = data;

    // Update transaction status
    const { error: transactionError } = await supabaseAdmin
      .from('transactions')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('external_transaction_id', transaction_id);

    if (transactionError) {
      logger.error('Failed to update transaction:', transactionError);
      return;
    }

    // Create notification
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id,
        type: 'payment',
        title: 'Saque processado',
        message: `Seu saque de R$ ${amount.toFixed(2)} foi processado com sucesso.`,
        data: {
          transaction_id,
          amount
        }
      });

    logger.info('Withdrawal completed:', { transaction_id, amount, user_id });
  } catch (error) {
    logger.error('Handle withdrawal completed error:', error);
  }
};

const handleWithdrawalFailed = async (data: any): Promise<void> => {
  try {
    const { transaction_id, reason, amount, user_id } = data;

    // Update transaction status
    const { error: transactionError } = await supabaseAdmin
      .from('transactions')
      .update({
        status: 'failed',
        failure_reason: reason,
        completed_at: new Date().toISOString()
      })
      .eq('external_transaction_id', transaction_id);

    if (transactionError) {
      logger.error('Failed to update transaction:', transactionError);
      return;
    }

    // Refund amount to wallet
    const { error: walletError } = await supabaseAdmin
      .from('wallets')
      .update({
        balance: amount // Will be handled by SQL function
      })
      .eq('user_id', user_id);

    if (walletError) {
      logger.error('Failed to refund wallet balance:', walletError);
      return;
    }

    // Create notification
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id,
        type: 'payment',
        title: 'Saque falhou',
        message: `Seu saque falhou e o valor foi devolvido à sua conta. Motivo: ${reason}`,
        data: {
          transaction_id,
          amount,
          reason
        }
      });

    logger.info('Withdrawal failed and refunded:', { transaction_id, amount, reason, user_id });
  } catch (error) {
    logger.error('Handle withdrawal failed error:', error);
  }
};

// Helper functions for tournament webhooks

const handleTournamentStarted = async (data: any): Promise<void> => {
  try {
    const { tournament_id } = data;

    // Update tournament status
    await supabaseAdmin
      .from('tournaments')
      .update({
        status: 'in_progress',
        started_at: new Date().toISOString()
      })
      .eq('id', tournament_id);

    // Notify participants
    const { data: participants } = await supabaseAdmin
      .from('tournament_participants')
      .select('user_id')
      .eq('tournament_id', tournament_id);

    if (participants) {
      const notifications = participants.map((p: any) => ({
        user_id: p.user_id,
        type: 'tournament',
        title: 'Torneio iniciado',
        message: 'O torneio que você está participando foi iniciado!',
        data: { tournament_id }
      }));

      await supabaseAdmin
        .from('notifications')
        .insert(notifications);
    }

    logger.info('Tournament started:', { tournament_id });
  } catch (error) {
    logger.error('Handle tournament started error:', error);
  }
};

const handleTournamentCompleted = async (data: any): Promise<void> => {
  try {
    const { tournament_id, results } = data;

    // Update tournament status
    await supabaseAdmin
      .from('tournaments')
      .update({
        status: 'completed',
        ended_at: new Date().toISOString()
      })
      .eq('id', tournament_id);

    // Update participant results and distribute prizes
    for (const result of results) {
      await supabaseAdmin
        .from('tournament_participants')
        .update({
          final_placement: result.placement,
          prize_won: result.prize_amount
        })
        .eq('tournament_id', tournament_id)
        .eq('user_id', result.user_id);

      // Add prize to wallet if any
      if (result.prize_amount > 0) {
        await supabaseAdmin
          .from('wallets')
          .update({
            balance: result.prize_amount // Will be handled by SQL function
          })
          .eq('user_id', result.user_id);

        // Create prize transaction
        await supabaseAdmin
          .from('transactions')
          .insert({
            user_id: result.user_id,
            type: 'tournament_prize',
            amount: result.prize_amount,
            description: `Prêmio do torneio - ${result.placement}º lugar`,
            status: 'completed'
          });
      }
    }

    logger.info('Tournament completed:', { tournament_id, results });
  } catch (error) {
    logger.error('Handle tournament completed error:', error);
  }
};

const handleTournamentCancelled = async (data: any): Promise<void> => {
  try {
    const { tournament_id, reason } = data;

    // Update tournament status
    await supabaseAdmin
      .from('tournaments')
      .update({
        status: 'cancelled',
        cancelled_reason: reason,
        ended_at: new Date().toISOString()
      })
      .eq('id', tournament_id);

    // Refund entry fees to participants
    const { data: participants } = await supabaseAdmin
      .from('tournament_participants')
      .select('user_id')
      .eq('tournament_id', tournament_id);

    const { data: tournament } = await supabaseAdmin
      .from('tournaments')
      .select('entry_fee')
      .eq('id', tournament_id)
      .single();

    if (participants && tournament?.entry_fee > 0) {
      for (const participant of participants) {
        // Refund to wallet
        await supabaseAdmin
          .from('wallets')
          .update({
            balance: tournament?.entry_fee || 0 // Will be handled by SQL function
          })
          .eq('user_id', participant.user_id);

        // Create refund transaction
        await supabaseAdmin
          .from('transactions')
          .insert({
            user_id: participant.user_id,
            type: 'refund',
            amount: tournament?.entry_fee || 0,
            description: 'Reembolso de torneio cancelado',
            status: 'completed'
          });
      }
    }

    logger.info('Tournament cancelled and refunded:', { tournament_id, reason });
  } catch (error) {
    logger.error('Handle tournament cancelled error:', error);
  }
};

const handleMatchCompleted = async (data: any): Promise<void> => {
  try {
    const { match_id, results } = data;

    // This would be handled by the match completion logic
    // Just log for now
    logger.info('Match completed via webhook:', { match_id, results });
  } catch (error) {
    logger.error('Handle match completed error:', error);
  }
};

// Helper function to verify webhook signatures
const verifyWebhookSignature = (payload: string, signature: string, secret?: string): boolean => {
  if (!secret || !signature) {
    return false;
  }

  try {
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    logger.error('Webhook signature verification error:', error);
    return false;
  }
};

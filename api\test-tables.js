// Script para testar se as tabelas foram criadas
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testTables() {
  console.log('🔍 Verificando tabelas criadas no Supabase...\n');
  
  try {
    // Listar todas as tabelas
    console.log('📋 Listando tabelas existentes...');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .order('table_name');
    
    if (tablesError) {
      console.error('❌ Erro ao listar tabelas:', tablesError.message);
      return false;
    }
    
    console.log('📊 Tabelas encontradas:');
    tables.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });
    
    // Verificar tabelas específicas
    const expectedTables = ['users', 'wallets', 'games'];
    console.log('\n🔍 Verificando tabelas específicas...');
    
    for (const tableName of expectedTables) {
      const tableExists = tables.some(t => t.table_name === tableName);
      if (tableExists) {
        console.log(`✅ Tabela '${tableName}' existe`);
        
        // Testar consulta na tabela
        try {
          const { data, error } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);
          
          if (error) {
            console.log(`   ⚠️ Erro ao consultar '${tableName}': ${error.message}`);
          } else {
            console.log(`   ✅ Consulta em '${tableName}' funcionando (${data.length} registros)`);
          }
        } catch (err) {
          console.log(`   ❌ Erro ao testar '${tableName}': ${err.message}`);
        }
      } else {
        console.log(`❌ Tabela '${tableName}' NÃO existe`);
      }
    }
    
    // Testar inserção de dados
    console.log('\n🧪 Testando inserção de dados...');
    
    // Testar inserção na tabela games
    try {
      const { data, error } = await supabase
        .from('games')
        .select('*')
        .limit(5);
      
      if (error) {
        console.log('❌ Erro ao consultar jogos:', error.message);
      } else {
        console.log(`✅ Jogos encontrados: ${data.length}`);
        data.forEach(game => {
          console.log(`   - ${game.name} (${game.type})`);
        });
      }
    } catch (err) {
      console.log('❌ Erro ao testar jogos:', err.message);
    }
    
    console.log('\n🎉 TESTE DE TABELAS CONCLUÍDO!');
    return true;
    
  } catch (err) {
    console.error('❌ Erro geral:', err.message);
    return false;
  }
}

// Executar teste
testTables()
  .then(success => {
    if (success) {
      console.log('\n✅ Banco de dados configurado corretamente!');
      console.log('🚀 Pronto para usar na API');
    } else {
      console.log('\n❌ Problemas encontrados no banco de dados');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(err => {
    console.error('\n❌ Erro fatal:', err.message);
    process.exit(1);
  });

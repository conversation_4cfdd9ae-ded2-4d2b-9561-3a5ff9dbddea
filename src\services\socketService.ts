import { io, Socket } from 'socket.io-client';
import { authService } from './authService';
import { WS_URL } from '../config/api';

interface SocketMessage {
  id: string;
  message: string;
  message_type: 'text' | 'system' | 'image';
  created_at: string;
  users: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

interface SocketUser {
  userId: string;
  username: string;
}

class SocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private currentRoomId: string | null = null;



  // Conecta ao servidor WebSocket
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Sempre desconecta primeiro para evitar conexões antigas
        if (this.socket) {
          this.socket.disconnect();
          this.socket = null;
          this.isConnected = false;
        }

        const token = authService.getToken();
        if (!token) {
          reject(new Error('Token de autenticação não encontrado'));
          return;
        }

        console.log('🔌 Conectando WebSocket com novo token...');

        // Conectar ao servidor WebSocket
        this.socket = io(WS_URL, {
          auth: {
            token: token
          },
          transports: ['websocket', 'polling'],
          forceNew: true, // Força nova conexão
          withCredentials: true,               // para enviar cookies, caso use sessão
        });

        this.socket.on('connect', () => {
          console.log('✅ Conectado ao WebSocket');
          this.isConnected = true;
          resolve();
        });

        this.socket.on('disconnect', () => {
          console.log('❌ Desconectado do WebSocket');
          this.isConnected = false;
        });

        this.socket.on('connect_error', (error) => {
          console.error('❌ Erro de conexão WebSocket:', error);
          reject(error);
        });

        this.socket.on('error', (error) => {
          console.error('❌ Erro WebSocket:', error);
        });

      } catch (error) {
        console.error('❌ Erro ao conectar WebSocket:', error);
        reject(error);
      }
    });
  }

  // Desconecta do servidor WebSocket
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.currentRoomId = null;
      console.log('❌ Desconectado do WebSocket');
    }
  }

  // Força reconexão (útil quando usuário muda)
  async reconnect(): Promise<void> {
    console.log('🔄 Forçando reconexão do WebSocket...');
    this.disconnect();
    await this.connect();
  }

  // Entra em uma sala
  joinRoom(roomId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket || !this.isConnected) {
        reject(new Error('WebSocket não conectado'));
        return;
      }

      this.socket.emit('join_room', { roomId });

      this.socket.once('room_joined', () => {
        console.log(`✅ Entrou na sala ${roomId}`);
        this.currentRoomId = roomId;
        resolve();
      });

      this.socket.once('error', (error) => {
        console.error('❌ Erro ao entrar na sala:', error);
        reject(error);
      });
    });
  }

  // Sai de uma sala
  leaveRoom(): void {
    if (this.socket && this.currentRoomId) {
      this.socket.emit('leave_room', { roomId: this.currentRoomId });
      this.currentRoomId = null;
    }
  }

  // Envia uma mensagem
  sendMessage(message: string): void {
    if (!this.socket || !this.currentRoomId) {
      console.error('❌ WebSocket não conectado ou não está em uma sala');
      return;
    }

    this.socket.emit('send_message', {
      roomId: this.currentRoomId,
      message,
      type: 'text'
    });
  }

  // Escuta novas mensagens
  onNewMessage(callback: (message: SocketMessage) => void): void {
    if (!this.socket) return;

    this.socket.on('new_message', callback);
  }

  // Escuta usuários entrando na sala
  onUserJoinedRoom(callback: (user: SocketUser) => void): void {
    if (!this.socket) return;

    this.socket.on('user_joined_room', callback);
  }

  // Escuta usuários saindo da sala
  onUserLeftRoom(callback: (user: SocketUser) => void): void {
    if (!this.socket) return;

    this.socket.on('user_left_room', callback);
  }

  // Atualiza status de pronto
  setPlayerReady(isReady: boolean): void {
    if (!this.socket || !this.currentRoomId) {
      console.error('❌ WebSocket não conectado ou não está em uma sala');
      return;
    }

    this.socket.emit('player_ready', {
      roomId: this.currentRoomId,
      isReady
    });
  }

  // Escuta mudanças de status de pronto
  onPlayerReadyChanged(callback: (data: { userId: string; isReady: boolean }) => void): void {
    if (!this.socket) return;

    this.socket.on('player_ready_update', callback);
  }

  // Escuta estado da sala
  onRoomStateUpdate(callback: (data: any) => void): void {
    if (!this.socket) return;

    this.socket.on('room_state_update', callback);
  }

  // Solicita sincronização do estado da sala
  syncRoomState(roomId: string): void {
    if (!this.socket || !this.isConnected) {
      console.error('❌ WebSocket não conectado');
      return;
    }

    this.socket.emit('sync_room_state', { roomId });
  }

  // Escuta sincronização completa do estado da sala
  onRoomStateSynced(callback: (data: any) => void): void {
    if (!this.socket) return;

    this.socket.on('room_state_synced', callback);
  }

  // Submete resultado da partida
  submitMatchResult(matchId: string, result: 'win' | 'loss' | 'draw', score?: number, evidence?: string): void {
    if (!this.socket || !this.isConnected) {
      console.error('❌ WebSocket não conectado');
      return;
    }

    this.socket.emit('submit_match_result', {
      matchId,
      result,
      score,
      evidence
    });
  }

  // Escuta submissão de resultado
  onMatchResultSubmitted(callback: (data: any) => void): void {
    if (!this.socket) return;

    this.socket.on('match_result_submitted', callback);
  }

  // Escuta confirmação de submissão de resultado
  onMatchResultSubmittedSuccess(callback: (data: any) => void): void {
    if (!this.socket) return;

    this.socket.on('match_result_submitted_success', callback);
  }

  // Escuta estado da partida
  onMatchStateUpdate(callback: (data: any) => void): void {
    if (!this.socket) return;

    this.socket.on('match_state_update', callback);
  }

  // Escuta finalização da partida
  onMatchEnding(callback: (data: any) => void): void {
    if (!this.socket) return;

    this.socket.on('match_ending', callback);
  }

  // Escuta notificação de espera por mais resultados
  onWaitingForResults(callback: (data: any) => void): void {
    if (!this.socket) return;

    this.socket.on('waiting_for_results', callback);
  }

  // Escuta conclusão completa da partida (todos os resultados submetidos)
  onMatchCompleted(callback: (data: any) => void): void {
    if (!this.socket) return;
    this.socket.on('match_completed', callback);
  }



  // Escuta início do jogo
  onGameStarting(callback: (data: { countdown: number; message: string }) => void): void {
    if (!this.socket) return;

    this.socket.on('game_starting', callback);
  }

  // Escuta atualizações do countdown
  onCountdownUpdate(callback: (data: { countdown: number }) => void): void {
    if (!this.socket) return;

    this.socket.on('countdown_update', callback);
  }

  // Escuta início da partida
  onMatchStarted(callback: (data: { matchId: string; message: string }) => void): void {
    if (!this.socket) return;

    this.socket.on('match_started', callback);
  }

  // Escuta partida em progresso
  onMatchInProgress(callback: (data: { matchId: string; startedAt: string }) => void): void {
    if (!this.socket) return;

    this.socket.on('match_in_progress', callback);
  }

  // Remove todos os listeners
  removeAllListeners(): void {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }

  // Verifica se está conectado
  get connected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  // Obtém o ID da sala atual
  get roomId(): string | null {
    return this.currentRoomId;
  }
}

export const socketService = new SocketService();
export default socketService;

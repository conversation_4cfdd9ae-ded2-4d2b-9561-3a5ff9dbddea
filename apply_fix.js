const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyFix() {
  console.log('🔧 Aplicando correção do trigger de wallet...\n');

  try {
    // Ler o arquivo SQL
    const sqlContent = fs.readFileSync('../fix_wallet_trigger.sql', 'utf8');
    
    // Dividir em comandos individuais
    const commands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));

    console.log(`📝 Executando ${commands.length} comandos SQL...\n`);

    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      console.log(`${i + 1}. Executando: ${command.substring(0, 100)}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: command });
        
        if (error) {
          console.error(`❌ Erro no comando ${i + 1}:`, error);
        } else {
          console.log(`✅ Comando ${i + 1} executado com sucesso`);
        }
      } catch (cmdError) {
        console.error(`❌ Erro ao executar comando ${i + 1}:`, cmdError);
      }
      
      // Pequena pausa entre comandos
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('\n🧪 Testando a correção...');
    
    // Testar a correção
    const { data: testUser, error: userError } = await supabase
      .from('users')
      .select('id, username')
      .limit(1)
      .single();

    if (userError || !testUser) {
      console.error('❌ Erro ao buscar usuário para teste:', userError);
      return;
    }

    console.log(`👤 Testando com usuário: ${testUser.username}`);
    
    // Buscar wallet
    const { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .select('id, balance')
      .eq('user_id', testUser.id)
      .single();

    if (walletError || !wallet) {
      console.error('❌ Erro ao buscar wallet:', walletError);
      return;
    }

    console.log(`💰 Saldo inicial: R$ ${wallet.balance}`);

    // Teste 1: Criar transação diretamente com status completed
    const testAmount1 = 7.50;
    const { data: directTransaction, error: directError } = await supabase
      .from('transactions')
      .insert({
        user_id: testUser.id,
        wallet_id: wallet.id,
        type: 'win',
        amount: testAmount1,
        description: 'Teste correção - transação direta completed',
        status: 'completed'
      })
      .select()
      .single();

    if (directError) {
      console.error('❌ Erro ao criar transação direta:', directError);
    } else {
      console.log(`📝 Transação direta criada: ${directTransaction.id}`);
      
      // Aguardar trigger processar
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Verificar saldo
      const { data: walletAfterDirect, error: walletDirectError } = await supabase
        .from('wallets')
        .select('balance')
        .eq('user_id', testUser.id)
        .single();

      if (walletDirectError) {
        console.error('❌ Erro ao verificar saldo após transação direta:', walletDirectError);
      } else {
        const expectedBalance1 = wallet.balance + testAmount1;
        console.log(`💰 Saldo após transação direta: R$ ${walletAfterDirect.balance}`);
        console.log(`💰 Saldo esperado: R$ ${expectedBalance1}`);
        
        if (Math.abs(walletAfterDirect.balance - expectedBalance1) < 0.01) {
          console.log('✅ TESTE 1 PASSOU: Transação direta com status completed funcionou!');
        } else {
          console.log('❌ TESTE 1 FALHOU: Transação direta não atualizou o saldo');
        }
      }
    }

    // Teste 2: Criar transação pending e depois atualizar para completed
    const testAmount2 = 2.50;
    const { data: pendingTransaction, error: pendingError } = await supabase
      .from('transactions')
      .insert({
        user_id: testUser.id,
        wallet_id: wallet.id,
        type: 'win',
        amount: testAmount2,
        description: 'Teste correção - transação pending->completed',
        status: 'pending'
      })
      .select()
      .single();

    if (pendingError) {
      console.error('❌ Erro ao criar transação pending:', pendingError);
    } else {
      console.log(`📝 Transação pending criada: ${pendingTransaction.id}`);
      
      // Atualizar para completed
      const { error: updateError } = await supabase
        .from('transactions')
        .update({ status: 'completed' })
        .eq('id', pendingTransaction.id);

      if (updateError) {
        console.error('❌ Erro ao atualizar transação:', updateError);
      } else {
        console.log('✅ Transação atualizada para completed');
        
        // Aguardar trigger processar
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Verificar saldo final
        const { data: finalWallet, error: finalError } = await supabase
          .from('wallets')
          .select('balance')
          .eq('user_id', testUser.id)
          .single();

        if (finalError) {
          console.error('❌ Erro ao verificar saldo final:', finalError);
        } else {
          const expectedFinalBalance = wallet.balance + testAmount1 + testAmount2;
          console.log(`💰 Saldo final: R$ ${finalWallet.balance}`);
          console.log(`💰 Saldo esperado final: R$ ${expectedFinalBalance}`);
          
          if (Math.abs(finalWallet.balance - expectedFinalBalance) < 0.01) {
            console.log('✅ TESTE 2 PASSOU: Transação pending->completed funcionou!');
            console.log('🎉 CORREÇÃO APLICADA COM SUCESSO!');
          } else {
            console.log('❌ TESTE 2 FALHOU: Transação pending->completed não funcionou');
          }
        }
      }
    }

    // Limpar transações de teste
    if (directTransaction) {
      await supabase.from('transactions').delete().eq('id', directTransaction.id);
    }
    if (pendingTransaction) {
      await supabase.from('transactions').delete().eq('id', pendingTransaction.id);
    }
    
    // Reverter saldo
    await supabase
      .from('wallets')
      .update({ balance: wallet.balance })
      .eq('user_id', testUser.id);
    
    console.log('🔄 Transações de teste removidas e saldo revertido');

  } catch (error) {
    console.error('❌ Erro durante aplicação da correção:', error);
  }
}

// Executar correção
applyFix()
  .then(() => {
    console.log('\n✅ Processo de correção concluído!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

-- Fix the ambiguous column reference in the trigger function
CREATE OR REPLACE FUNCTION update_user_stats_after_match()
RETURNS TRIGGER AS $$
DECLARE
    participant RECORD;
    total_matches INTEGER;
    total_wins INTEGER;
    total_losses INTEGER;
    total_draws INTEGER;
    new_win_rate DECIMAL(5,2);
BEGIN
    -- Only process when match status changes to completed
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        -- Update stats for each participant
        FOR participant IN 
            SELECT user_id, placement, earnings 
            FROM match_participants 
            WHERE match_id = NEW.id
        LOOP
            -- Update match counts
            UPDATE user_stats 
            SET total_matches = total_matches + 1,
                total_earnings = total_earnings + participant.earnings,
                updated_at = NOW()
            WHERE user_id = participant.user_id;
            
            -- Update wins/losses based on placement
            IF participant.placement = 1 THEN
                UPDATE user_stats 
                SET total_wins = total_wins + 1,
                    current_streak = current_streak + 1,
                    best_streak = GREATEST(best_streak, current_streak + 1)
                WHERE user_id = participant.user_id;
            ELSIF participant.placement > 1 THEN
                UPDATE user_stats 
                SET total_losses = total_losses + 1,
                    current_streak = 0
                WHERE user_id = participant.user_id;
            END IF;
            
            -- Recalculate win rate
            SELECT us.total_matches, us.total_wins, us.total_losses, us.total_draws
            INTO total_matches, total_wins, total_losses, total_draws
            FROM user_stats us
            WHERE us.user_id = participant.user_id;
            
            IF total_matches > 0 THEN
                new_win_rate := (total_wins::DECIMAL / total_matches::DECIMAL) * 100;
                UPDATE user_stats 
                SET win_rate = new_win_rate
                WHERE user_id = participant.user_id;
            END IF;
        END LOOP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixRoomCount() {
  try {
    const roomId = '9dffa9ee-a39e-4b11-a078-20f507e8eaed';
    
    console.log('🔧 Fixing room participant count...');
    
    // Get actual participant count
    const { data: participants, error: participantError } = await supabase
      .from('room_participants')
      .select('user_id')
      .eq('room_id', roomId);
    
    if (participantError) {
      console.error('❌ Error getting participants:', participantError);
      return;
    }
    
    const actualCount = participants?.length || 0;
    console.log(`📊 Actual participants: ${actualCount}`);
    
    // Update the room's current_players count
    const { error: updateError } = await supabase
      .from('game_rooms')
      .update({ 
        current_players: actualCount,
        updated_at: new Date().toISOString()
      })
      .eq('id', roomId);
    
    if (updateError) {
      console.error('❌ Error updating room:', updateError);
      return;
    }
    
    console.log('✅ Room participant count fixed');
    
    // Verify the fix
    const { data: room, error: roomError } = await supabase
      .from('game_rooms')
      .select('current_players, max_players')
      .eq('id', roomId)
      .single();
    
    if (roomError) {
      console.error('❌ Error verifying fix:', roomError);
      return;
    }
    
    console.log(`✅ Room now shows: ${room.current_players}/${room.max_players} players`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixRoomCount();

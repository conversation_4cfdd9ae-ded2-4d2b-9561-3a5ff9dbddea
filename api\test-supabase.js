// Script simples para testar conexão com Supabase
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

console.log('🔍 Testando conexão com Supabase...\n');

// Configurações do Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('📋 Configurações:');
console.log('URL:', supabaseUrl);
console.log('Anon Key:', supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'NÃO DEFINIDA');
console.log('Service Key:', supabaseServiceKey ? `${supabaseServiceKey.substring(0, 20)}...` : 'NÃO DEFINIDA');
console.log('');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Erro: Variáveis de ambiente do Supabase não estão configuradas!');
  process.exit(1);
}

// Criar cliente Supabase
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  try {
    console.log('🔗 Testando conexão básica...');
    
    // Teste 1: Verificar se consegue conectar
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('⚠️ Erro na consulta (esperado se tabela não existir):', error.message);
      
      // Teste 2: Tentar uma operação mais básica
      console.log('🔄 Tentando operação mais básica...');
      const { data: authData, error: authError } = await supabase.auth.getSession();
      
      if (authError) {
        console.error('❌ Erro de autenticação:', authError.message);
        return false;
      } else {
        console.log('✅ Conexão com Supabase estabelecida com sucesso!');
        console.log('📊 Dados da sessão:', authData ? 'Sessão ativa' : 'Nenhuma sessão ativa');
        return true;
      }
    } else {
      console.log('✅ Conexão com Supabase estabelecida com sucesso!');
      console.log('📊 Dados retornados:', data);
      return true;
    }
    
  } catch (err) {
    console.error('❌ Erro ao conectar com Supabase:', err.message);
    return false;
  }
}

async function testServiceRoleConnection() {
  if (!supabaseServiceKey) {
    console.log('⚠️ Service Role Key não configurada, pulando teste admin...');
    return true;
  }
  
  try {
    console.log('🔐 Testando conexão com Service Role...');
    
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Teste básico com privilégios admin
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('⚠️ Erro na consulta admin (esperado se tabela não existir):', error.message);
      return true; // Não é um erro crítico se a tabela não existir
    } else {
      console.log('✅ Conexão admin com Supabase estabelecida com sucesso!');
      return true;
    }
    
  } catch (err) {
    console.error('❌ Erro ao conectar com Service Role:', err.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Iniciando testes de conexão...\n');
  
  const basicTest = await testConnection();
  console.log('');
  
  const adminTest = await testServiceRoleConnection();
  console.log('');
  
  if (basicTest && adminTest) {
    console.log('🎉 TODOS OS TESTES PASSARAM!');
    console.log('✅ Supabase está configurado corretamente');
    console.log('✅ Conexão estabelecida com sucesso');
    console.log('✅ Pronto para usar na API');
    process.exit(0);
  } else {
    console.log('❌ ALGUNS TESTES FALHARAM');
    console.log('🔧 Verifique as configurações do Supabase');
    process.exit(1);
  }
}

main();

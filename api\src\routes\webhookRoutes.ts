import { Router } from 'express';
import { handlePaymentWebhook, handleTournamentWebhook } from '../controllers/webhookController';
import { webhookRateLimit } from '../middleware/rateLimiting';

const router = Router();

/**
 * @route   POST /api/webhooks/payment
 * @desc    Handle payment gateway webhooks
 * @access  Public (with signature verification)
 */
router.post('/payment',
  webhookRateLimit,
  handlePaymentWebhook
);

/**
 * @route   POST /api/webhooks/tournament
 * @desc    Handle tournament webhooks
 * @access  Public (with signature verification)
 */
router.post('/tournament',
  webhookRateLimit,
  handleTournamentWebhook
);

export default router;

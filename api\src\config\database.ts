import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database';

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseServiceKey || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Service role client (for admin operations)
export const supabaseAdmin: SupabaseClient<Database> = createClient(
  supabaseUrl,
  supabaseServiceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Anonymous client (for public operations)
export const supabaseClient: SupabaseClient<Database> = createClient(
  supabaseUrl,
  supabaseAnonKey
);

// Create authenticated client for specific user
export const createUserClient = (accessToken: string): SupabaseClient<Database> => {
  return createClient(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    }
  });
};

// Database connection test
export const testConnection = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
    
    console.log('Database connection successful');
    return true;
  } catch (error) {
    console.error('Database connection test error:', error);
    return false;
  }
};

// Database health check
export const getDatabaseHealth = async () => {
  try {
    const startTime = Date.now();
    
    const { data, error } = await supabaseAdmin
      .from('system_settings')
      .select('key, value')
      .eq('key', 'maintenance_mode')
      .single();
    
    const responseTime = Date.now() - startTime;
    
    if (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        responseTime
      };
    }
    
    return {
      status: 'healthy',
      maintenanceMode: data?.value === 'true',
      responseTime
    };
  } catch (error) {
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime: -1
    };
  }
};

// Execute raw SQL query (admin only)
export const executeQuery = async (query: string, params: any[] = []) => {
  try {
    const { data, error } = await supabaseAdmin.rpc('execute_sql', {
      query,
      params
    });
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Query execution error:', error);
    throw error;
  }
};

// Get database statistics
export const getDatabaseStats = async () => {
  try {
    const queries = [
      'SELECT COUNT(*) as total_users FROM users WHERE status = \'active\'',
      'SELECT COUNT(*) as active_matches FROM matches WHERE status = \'in_progress\'',
      'SELECT COUNT(*) as waiting_rooms FROM game_rooms WHERE status = \'waiting\'',
      'SELECT COALESCE(SUM(balance), 0) as total_balance FROM wallets',
      'SELECT COUNT(*) as transactions_24h FROM transactions WHERE created_at > NOW() - INTERVAL \'24 hours\'',
      'SELECT COUNT(*) as unread_notifications FROM notifications WHERE created_at > NOW() - INTERVAL \'1 hour\' AND is_read = false'
    ];
    
    const results = await Promise.all(
      queries.map(query => supabaseAdmin.rpc('execute_sql', { query, params: [] }))
    );
    
    return {
      totalUsers: results[0].data?.[0]?.total_users || 0,
      activeMatches: results[1].data?.[0]?.active_matches || 0,
      waitingRooms: results[2].data?.[0]?.waiting_rooms || 0,
      totalBalance: results[3].data?.[0]?.total_balance || 0,
      transactions24h: results[4].data?.[0]?.transactions_24h || 0,
      unreadNotifications: results[5].data?.[0]?.unread_notifications || 0
    };
  } catch (error) {
    console.error('Error getting database stats:', error);
    throw error;
  }
};

export default supabaseAdmin;

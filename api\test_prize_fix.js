const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testPrizeFix() {
  console.log('🧪 Testando correção de distribuição de prêmios...\n');

  try {
    // Buscar usuário para teste
    const { data: testUser, error: userError } = await supabase
      .from('users')
      .select('id, username')
      .limit(1)
      .single();

    if (userError || !testUser) {
      console.error('❌ Erro ao buscar usuário:', userError);
      return;
    }

    console.log(`👤 Usuário de teste: ${testUser.username}`);

    // Buscar wallet
    const { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .select('id, balance')
      .eq('user_id', testUser.id)
      .single();

    if (walletError || !wallet) {
      console.error('❌ Erro ao buscar wallet:', walletError);
      return;
    }

    console.log(`💰 Saldo inicial: R$ ${wallet.balance}`);

    // Teste da nova lógica: criar transação pending e depois atualizar para completed
    const testAmount = 15.00;
    
    console.log('\n🔄 Testando nova lógica (pending -> completed)...');
    
    // Passo 1: Criar transação com status pending
    const { data: transaction, error: txError } = await supabase
      .from('transactions')
      .insert({
        user_id: testUser.id,
        wallet_id: wallet.id,
        type: 'win',
        amount: testAmount,
        description: 'Teste correção - prêmio de partida',
        status: 'pending'
      })
      .select('id')
      .single();

    if (txError || !transaction) {
      console.error('❌ Erro ao criar transação pending:', txError);
      return;
    }

    console.log(`📝 Transação pending criada: ${transaction.id}`);

    // Verificar que o saldo não mudou ainda
    const { data: walletAfterPending, error: walletPendingError } = await supabase
      .from('wallets')
      .select('balance')
      .eq('user_id', testUser.id)
      .single();

    if (walletPendingError) {
      console.error('❌ Erro ao verificar saldo após pending:', walletPendingError);
    } else {
      console.log(`💰 Saldo após pending: R$ ${walletAfterPending.balance} (deve ser igual ao inicial)`);
    }

    // Passo 2: Atualizar para completed
    console.log('🔄 Atualizando transação para completed...');
    
    const { error: updateError } = await supabase
      .from('transactions')
      .update({ status: 'completed' })
      .eq('id', transaction.id);

    if (updateError) {
      console.error('❌ Erro ao atualizar transação:', updateError);
      return;
    }

    console.log('✅ Transação atualizada para completed');

    // Aguardar trigger processar
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Verificar saldo final
    const { data: finalWallet, error: finalError } = await supabase
      .from('wallets')
      .select('balance, updated_at')
      .eq('user_id', testUser.id)
      .single();

    if (finalError) {
      console.error('❌ Erro ao verificar saldo final:', finalError);
    } else {
      const expectedBalance = wallet.balance + testAmount;
      console.log(`💰 Saldo final: R$ ${finalWallet.balance}`);
      console.log(`💰 Saldo esperado: R$ ${expectedBalance}`);
      console.log(`🕐 Wallet atualizada: ${finalWallet.updated_at}`);
      
      if (Math.abs(finalWallet.balance - expectedBalance) < 0.01) {
        console.log('🎉 CORREÇÃO FUNCIONANDO!');
        console.log('✅ A nova lógica de pending->completed está atualizando o saldo corretamente!');
      } else {
        console.log('❌ CORREÇÃO NÃO FUNCIONANDO!');
        console.log('⚠️ O trigger ainda não está processando as atualizações de status');
      }
    }

    // Limpar transação de teste
    await supabase.from('transactions').delete().eq('id', transaction.id);
    
    // Reverter saldo se necessário
    if (finalWallet && Math.abs(finalWallet.balance - (wallet.balance + testAmount)) < 0.01) {
      await supabase
        .from('wallets')
        .update({ balance: wallet.balance })
        .eq('user_id', testUser.id);
      console.log('🔄 Saldo revertido para o valor original');
    }

    // Teste adicional: verificar se há transações de prêmio recentes que não foram processadas
    console.log('\n🔍 Verificando transações de prêmio não processadas...');
    
    const { data: unprocessedTransactions, error: unprocessedError } = await supabase
      .from('transactions')
      .select(`
        id,
        user_id,
        amount,
        status,
        created_at,
        users!inner(username)
      `)
      .eq('type', 'win')
      .eq('status', 'completed')
      .gte('created_at', new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()) // Últimas 2 horas
      .order('created_at', { ascending: false });

    if (unprocessedError) {
      console.error('❌ Erro ao buscar transações não processadas:', unprocessedError);
    } else {
      console.log(`📊 Transações de prêmio completadas nas últimas 2 horas: ${unprocessedTransactions.length}`);
      
      if (unprocessedTransactions.length > 0) {
        console.log('💡 Sugestão: Reiniciar o servidor da API pode ajudar a aplicar as correções no código');
        
        unprocessedTransactions.forEach(tx => {
          console.log(`  - ${tx.users.username}: R$ ${tx.amount} (${tx.created_at})`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Erro durante teste:', error);
  }
}

// Executar teste
testPrizeFix()
  .then(() => {
    console.log('\n✅ Teste concluído!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

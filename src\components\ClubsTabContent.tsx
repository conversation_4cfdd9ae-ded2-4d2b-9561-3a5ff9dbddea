import React from 'react';
import { Clock, Sparkles, Construction } from 'lucide-react';

export default function ClubsTabContent() {

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 max-w-lg w-full text-center relative overflow-hidden">
        {/* Em Breve Tag */}
        <div className="absolute top-0 right-0 bg-gradient-to-l from-indigo-500 via-purple-500 to-pink-500 text-white font-bold px-6 py-1 rounded-bl-lg">
          EM BREVE
        </div>

        <div className="mb-6 relative">
          <div className="absolute -top-1 -left-1 w-full h-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-full opacity-20 animate-pulse"></div>
          <Construction className="w-16 h-16 text-indigo-400 mx-auto" />
        </div>

        <h1 className="text-2xl font-bold text-white mb-4">Clubes</h1>

        <p className="text-gray-300 mb-6">
          Estamos trabalhando para trazer a funcionalidade de Clubes para a plataforma.
          Em breve você poderá criar e participar de clubes com outros jogadores!
        </p>

        <div className="flex items-center justify-center gap-4 mb-6">
          <div className="flex items-center gap-2 bg-gray-700/50 px-4 py-2 rounded-lg">
            <Clock className="w-5 h-5 text-indigo-400" />
            <span className="text-gray-300">Lançamento em breve</span>
          </div>

          <div className="flex items-center gap-2 bg-gray-700/50 px-4 py-2 rounded-lg">
            <Sparkles className="w-5 h-5 text-indigo-400" />
            <span className="text-gray-300">Novos recursos</span>
          </div>
        </div>

        <div className="text-sm text-gray-400">
          Fique atento para novidades sobre o lançamento dos Clubes!
        </div>
      </div>
    </div>
  );
}

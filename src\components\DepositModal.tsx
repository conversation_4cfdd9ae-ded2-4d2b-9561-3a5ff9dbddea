import React, { useState } from 'react';
import { X, CreditCard, QrCode, Copy, Check } from 'lucide-react';

interface DepositModalProps {
  onClose: () => void;
  onDeposit: (amount: number, method: 'pix' | 'credit_card') => void;
}

export default function DepositModal({ onClose, onDeposit }: DepositModalProps) {
  const [amount, setAmount] = useState('');
  const [method, setMethod] = useState<'pix' | 'credit_card'>('pix');
  const [step, setStep] = useState<'amount' | 'payment'>('amount');
  const [copied, setCopied] = useState(false);
  const [cardInfo, setCardInfo] = useState({
    number: '',
    name: '',
    expiry: '',
    cvv: ''
  });

  const handleCopyPix = () => {
    navigator.clipboard.writeText('00020126580014BR.GOV.BCB.PIX0136f5c32bd5-877d-4fcd-982b-2cd9f3bd96bb5204000053039865802BR5925FairPlay Games LTDA      6009SAO PAULO62070503***6304E2CA');
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (step === 'amount') {
      setStep('payment');
    } else {
      onDeposit(Number(amount), method);
      onClose();
    }
  };

  const presetAmounts = [10, 25, 50, 100, 250, 500];

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fadeIn">
      <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl w-full max-w-md shadow-xl">
        {/* Header */}
        <div className="p-4 border-b border-gray-700 flex items-center justify-between">
          <h3 className="text-white text-lg font-bold">Depositar</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {step === 'amount' ? (
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Amount Input */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Valor do Depósito</label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">R$</span>
                <input
                  type="number"
                  min="10"
                  step="0.01"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 pl-10 pr-3 focus:outline-none focus:ring-2 focus:ring-rose-400"
                  placeholder="0,00"
                  required
                />
              </div>
            </div>

            {/* Preset Amounts */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Valores Sugeridos</label>
              <div className="grid grid-cols-3 gap-2">
                {presetAmounts.map((value) => (
                  <button
                    key={value}
                    type="button"
                    onClick={() => setAmount(value.toString())}
                    className="bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-2 hover:bg-gray-600/50 transition-colors"
                  >
                    R$ {value}
                  </button>
                ))}
              </div>
            </div>

            {/* Payment Method */}
            <div>
              <label className="block text-gray-400 text-sm mb-2">Método de Pagamento</label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  onClick={() => setMethod('pix')}
                  className={`p-4 rounded-lg flex flex-col items-center gap-2 ${
                    method === 'pix'
                      ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white border-2 border-white/20'
                      : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-600/50'
                  }`}
                >
                  <QrCode className="w-6 h-6" />
                  <span className="font-semibold">PIX</span>
                </button>
                <button
                  type="button"
                  onClick={() => setMethod('credit_card')}
                  className={`p-4 rounded-lg flex flex-col items-center gap-2 ${
                    method === 'credit_card'
                      ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white border-2 border-white/20'
                      : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-600/50'
                  }`}
                >
                  <CreditCard className="w-6 h-6" />
                  <span className="font-semibold">Cartão</span>
                </button>
              </div>
            </div>

            <button
              type="submit"
              className="w-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-3 rounded-lg hover:opacity-90 transition-opacity shadow-lg"
            >
              Continuar
            </button>
          </form>
        ) : (
          <div className="p-6 space-y-6">
            {method === 'pix' ? (
              <>
                <div className="text-center">
                  <div className="bg-white/90 backdrop-blur-sm p-4 rounded-lg inline-block mb-4 shadow-xl">
                    <img
                      src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=00020126580014BR.GOV.BCB.PIX0136f5c32bd5-877d-4fcd-982b-2cd9f3bd96bb5204000053039865802BR5925FairPlay Games LTDA      6009SAO PAULO62070503***6304E2CA"
                      alt="QR Code PIX"
                      className="w-48 h-48"
                    />
                  </div>
                  <p className="text-gray-400 mb-4">
                    Escaneie o QR Code ou copie o código PIX abaixo
                  </p>
                  <button
                    onClick={handleCopyPix}
                    className="bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-4 w-full flex items-center justify-center gap-2 hover:bg-gray-600/50 transition-colors"
                  >
                    {copied ? (
                      <>
                        <Check className="w-5 h-5" />
                        Copiado!
                      </>
                    ) : (
                      <>
                        <Copy className="w-5 h-5" />
                        Copiar Código PIX
                      </>
                    )}
                  </button>
                </div>
              </>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Número do Cartão</label>
                  <input
                    type="text"
                    maxLength={19}
                    value={cardInfo.number}
                    onChange={(e) => setCardInfo({ ...cardInfo, number: e.target.value })}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-3 focus:outline-none focus:ring-2 focus:ring-rose-400"
                    placeholder="1234 5678 9012 3456"
                    required
                  />
                </div>
                <div>
                  <label className="block text-gray-400 text-sm mb-1">Nome no Cartão</label>
                  <input
                    type="text"
                    value={cardInfo.name}
                    onChange={(e) => setCardInfo({ ...cardInfo, name: e.target.value })}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-3 focus:outline-none focus:ring-2 focus:ring-rose-400"
                    placeholder="NOME COMO ESTÁ NO CARTÃO"
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-gray-400 text-sm mb-1">Validade</label>
                    <input
                      type="text"
                      maxLength={5}
                      value={cardInfo.expiry}
                      onChange={(e) => setCardInfo({ ...cardInfo, expiry: e.target.value })}
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-3 focus:outline-none focus:ring-2 focus:ring-rose-400"
                      placeholder="MM/AA"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-gray-400 text-sm mb-1">CVV</label>
                    <input
                      type="text"
                      maxLength={3}
                      value={cardInfo.cvv}
                      onChange={(e) => setCardInfo({ ...cardInfo, cvv: e.target.value })}
                      className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-3 px-3 focus:outline-none focus:ring-2 focus:ring-rose-400"
                      placeholder="123"
                      required
                    />
                  </div>
                </div>
                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-3 rounded-lg hover:opacity-90 transition-opacity shadow-lg"
                >
                  Pagar R$ {Number(amount).toFixed(2)}
                </button>
              </form>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
-- Migration 004: Social Features
-- Created: 2024-01-01
-- Description: Creates tables for friends, clubs, chat, and social interactions

-- Additional types for social features
CREATE TYPE club_role AS ENUM ('owner', 'admin', 'moderator', 'member');
CREATE TYPE club_privacy AS ENUM ('public', 'private', 'invite_only');
CREATE TYPE message_type AS ENUM ('text', 'image', 'system', 'challenge');

-- Friends table
CREATE TABLE friendships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    addressee_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status friend_request_status DEFAULT 'pending',
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    UNIQUE(requester_user_id, addressee_user_id),
    CONSTRAINT no_self_friendship CHECK (requester_user_id != addressee_user_id)
);

-- Clubs table
CREATE TABLE clubs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    rules TEXT,
    logo_url TEXT,
    banner_url TEXT,
    game_id UUID REFERENCES games(id) ON DELETE SET NULL,
    owner_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    privacy_type club_privacy DEFAULT 'public',
    max_members INTEGER DEFAULT 100,
    current_members INTEGER DEFAULT 1,
    level INTEGER DEFAULT 1,
    experience_points INTEGER DEFAULT 0,
    region VARCHAR(100),
    tags TEXT[],
    club_settings JSONB DEFAULT '{}',
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT max_members_valid CHECK (max_members >= 5 AND max_members <= 1000),
    CONSTRAINT current_members_valid CHECK (current_members >= 0 AND current_members <= max_members),
    CONSTRAINT club_name_length CHECK (LENGTH(name) >= 3)
);

-- Club members table
CREATE TABLE club_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    club_id UUID NOT NULL REFERENCES clubs(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role club_role DEFAULT 'member',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    contribution_points INTEGER DEFAULT 0,
    
    -- Constraints
    UNIQUE(club_id, user_id)
);

-- Club invitations table
CREATE TABLE club_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    club_id UUID NOT NULL REFERENCES clubs(id) ON DELETE CASCADE,
    inviter_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    invitee_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status friend_request_status DEFAULT 'pending',
    message TEXT,
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    
    -- Constraints
    UNIQUE(club_id, invitee_user_id)
);

-- Private chat conversations table
CREATE TABLE private_conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    participant1_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    participant2_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(participant1_user_id, participant2_user_id),
    CONSTRAINT no_self_conversation CHECK (participant1_user_id != participant2_user_id)
);

-- Private chat messages table
CREATE TABLE private_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES private_conversations(id) ON DELETE CASCADE,
    sender_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    message_type message_type DEFAULT 'text',
    attachment_url TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT message_length CHECK (LENGTH(message) <= 2000)
);

-- Club chat messages table
CREATE TABLE club_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    club_id UUID NOT NULL REFERENCES clubs(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    message_type message_type DEFAULT 'text',
    attachment_url TEXT,
    reply_to_message_id UUID REFERENCES club_messages(id) ON DELETE SET NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT club_message_length CHECK (LENGTH(message) <= 2000)
);

-- Friend challenges table
CREATE TABLE friend_challenges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    challenger_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    challenged_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
    bet_amount DECIMAL(10,2) NOT NULL CHECK (bet_amount >= 0),
    game_settings JSONB DEFAULT '{}',
    status friend_request_status DEFAULT 'pending',
    match_id UUID REFERENCES matches(id) ON DELETE SET NULL, -- Created when challenge is accepted
    message TEXT,
    challenged_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    
    -- Constraints
    CONSTRAINT no_self_challenge CHECK (challenger_user_id != challenged_user_id)
);

-- User blocks table (for blocking other users)
CREATE TABLE user_blocks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    blocker_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    blocked_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reason TEXT,
    blocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(blocker_user_id, blocked_user_id),
    CONSTRAINT no_self_block CHECK (blocker_user_id != blocked_user_id)
);

-- User reports table
CREATE TABLE user_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reporter_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reported_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    match_id UUID REFERENCES matches(id) ON DELETE SET NULL,
    reason VARCHAR(100) NOT NULL,
    description TEXT,
    evidence_urls TEXT[],
    status VARCHAR(20) DEFAULT 'open', -- 'open', 'under_review', 'resolved', 'dismissed'
    admin_notes TEXT,
    resolved_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT no_self_report CHECK (reporter_user_id != reported_user_id)
);

-- Create indexes
CREATE INDEX idx_friendships_requester_user_id ON friendships(requester_user_id);
CREATE INDEX idx_friendships_addressee_user_id ON friendships(addressee_user_id);
CREATE INDEX idx_friendships_status ON friendships(status);

CREATE INDEX idx_clubs_owner_user_id ON clubs(owner_user_id);
CREATE INDEX idx_clubs_game_id ON clubs(game_id);
CREATE INDEX idx_clubs_privacy_type ON clubs(privacy_type);
CREATE INDEX idx_clubs_slug ON clubs(slug);
CREATE INDEX idx_clubs_created_at ON clubs(created_at);

CREATE INDEX idx_club_members_club_id ON club_members(club_id);
CREATE INDEX idx_club_members_user_id ON club_members(user_id);
CREATE INDEX idx_club_members_role ON club_members(role);

CREATE INDEX idx_club_invitations_club_id ON club_invitations(club_id);
CREATE INDEX idx_club_invitations_invitee_user_id ON club_invitations(invitee_user_id);
CREATE INDEX idx_club_invitations_status ON club_invitations(status);

CREATE INDEX idx_private_conversations_participant1 ON private_conversations(participant1_user_id);
CREATE INDEX idx_private_conversations_participant2 ON private_conversations(participant2_user_id);
CREATE INDEX idx_private_conversations_last_message_at ON private_conversations(last_message_at);

CREATE INDEX idx_private_messages_conversation_id ON private_messages(conversation_id);
CREATE INDEX idx_private_messages_sender_user_id ON private_messages(sender_user_id);
CREATE INDEX idx_private_messages_created_at ON private_messages(created_at);
CREATE INDEX idx_private_messages_is_read ON private_messages(is_read);

CREATE INDEX idx_club_messages_club_id ON club_messages(club_id);
CREATE INDEX idx_club_messages_user_id ON club_messages(user_id);
CREATE INDEX idx_club_messages_created_at ON club_messages(created_at);

CREATE INDEX idx_friend_challenges_challenger_user_id ON friend_challenges(challenger_user_id);
CREATE INDEX idx_friend_challenges_challenged_user_id ON friend_challenges(challenged_user_id);
CREATE INDEX idx_friend_challenges_status ON friend_challenges(status);

CREATE INDEX idx_user_blocks_blocker_user_id ON user_blocks(blocker_user_id);
CREATE INDEX idx_user_blocks_blocked_user_id ON user_blocks(blocked_user_id);

CREATE INDEX idx_user_reports_reporter_user_id ON user_reports(reporter_user_id);
CREATE INDEX idx_user_reports_reported_user_id ON user_reports(reported_user_id);
CREATE INDEX idx_user_reports_status ON user_reports(status);

-- Apply updated_at triggers
CREATE TRIGGER update_clubs_updated_at BEFORE UPDATE ON clubs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

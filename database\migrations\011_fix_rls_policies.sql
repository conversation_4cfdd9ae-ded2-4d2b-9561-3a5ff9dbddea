-- Migration 011: Fix RLS Policies for Wallet and Affiliate Creation
-- Created: 2024-06-08
-- Description: Adds INSERT policies for wallets and affiliate_programs to allow automatic creation

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can only access their own wallet" ON wallets;
DROP POLICY IF EXISTS "Users can view their own affiliate data" ON affiliate_programs;

-- Wallet policies - separate SELECT, INSERT, UPDATE, DELETE
CREATE POLICY "Users can view their own wallet" ON wallets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own wallet" ON wallets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own wallet" ON wallets
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own wallet" ON wallets
    FOR DELETE USING (auth.uid() = user_id);

-- Affiliate program policies - separate SELECT, INSERT, UPDATE, DELETE
CREATE POLICY "Users can view their own affiliate data" ON affiliate_programs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own affiliate program" ON affiliate_programs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own affiliate data" ON affiliate_programs
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own affiliate data" ON affiliate_programs
    FOR DELETE USING (auth.uid() = user_id);

-- User referrals policies - add INSERT policy
CREATE POLICY "Users can create referrals" ON user_referrals
    FOR INSERT WITH CHECK (auth.uid() = referrer_user_id);

-- Commission transactions policies - add INSERT policy  
CREATE POLICY "System can create commission transactions" ON commission_transactions
    FOR INSERT WITH CHECK (true); -- Allow system to create commissions

-- Affiliate payouts policies
CREATE POLICY "Users can view their own payouts" ON affiliate_payouts
    FOR SELECT USING (auth.uid() = affiliate_user_id);

CREATE POLICY "Users can create payout requests" ON affiliate_payouts
    FOR INSERT WITH CHECK (auth.uid() = affiliate_user_id);

CREATE POLICY "Users can update their own payouts" ON affiliate_payouts
    FOR UPDATE USING (auth.uid() = affiliate_user_id);

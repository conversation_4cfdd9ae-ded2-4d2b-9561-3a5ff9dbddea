import React from 'react';
import { X, Trophy, Wallet, Gamepad2 } from 'lucide-react';

interface ChallengeReceivedModalProps {
  challenger: {
    id: string;
    name: string;
    avatar: string;
  };
  game: {
    id: string;
    title: string;
    imageUrl: string;
  };
  betAmount: number;
  onAccept: () => void;
  onDecline: () => void;
}

export default function ChallengeReceivedModal({
  challenger,
  game,
  betAmount,
  onAccept,
  onDecline
}: ChallengeReceivedModalProps) {
  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-xl w-full max-w-md">
        {/* Header */}
        <div className="p-4 border-b border-gray-700 flex items-center justify-between">
          <h3 className="text-white text-lg font-bold">Desafio Recebido</h3>
          <button 
            onClick={onDecline}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Challenger Info */}
          <div className="flex items-center gap-3">
            <img
              src={challenger.avatar}
              alt={challenger.name}
              className="w-12 h-12 rounded-full object-cover"
            />
            <div>
              <h4 className="text-white font-semibold">{challenger.name}</h4>
              <p className="text-sm text-gray-400">te desafiou para uma partida!</p>
            </div>
          </div>

          {/* Game Info */}
          <div className="bg-gray-700 rounded-lg overflow-hidden">
            <div className="relative aspect-video">
              <img
                src={game.imageUrl}
                alt={game.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent flex items-end p-4">
                <div className="flex items-center gap-2">
                  <Gamepad2 className="w-5 h-5 text-rose-400" />
                  <h5 className="text-white font-semibold">{game.title}</h5>
                </div>
              </div>
            </div>
            <div className="p-4">
              <div className="flex items-center gap-2">
                <Wallet className="w-5 h-5 text-rose-400" />
                <span className="text-white font-semibold">Aposta: R$ {betAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <button
              onClick={onDecline}
              className="flex-1 bg-gray-700 text-white font-bold py-3 rounded-lg hover:bg-gray-600"
            >
              Recusar
            </button>
            <button
              onClick={onAccept}
              className="flex-1 bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold py-3 rounded-lg flex items-center justify-center gap-2"
            >
              <Trophy className="w-5 h-5" />
              Aceitar Desafio
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
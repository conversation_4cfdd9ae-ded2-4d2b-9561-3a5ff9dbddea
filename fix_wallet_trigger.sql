-- Fix wallet balance trigger to work with both INSERT and UPDATE operations
-- This will ensure that transactions created directly with 'completed' status also update wallet balance

-- Drop existing trigger
DROP TRIGGER IF EXISTS update_wallet_balance_trigger ON transactions;

-- Recreate the function to handle both INSERT and UPDATE
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle UPDATE operations (status change from non-completed to completed)
    IF TG_OP = 'UPDATE' THEN
        IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
            IF NEW.type IN ('deposit', 'win', 'commission', 'refund') THEN
                -- Add to balance
                UPDATE wallets 
                SET balance = balance + NEW.amount,
                    updated_at = NOW()
                WHERE user_id = NEW.user_id;
                
                -- Update total deposited for deposits
                IF NEW.type = 'deposit' THEN
                    UPDATE wallets 
                    SET total_deposited = total_deposited + NEW.amount
                    WHERE user_id = NEW.user_id;
                END IF;
                
            ELSIF NEW.type IN ('withdrawal', 'bet') THEN
                -- Subtract from balance
                UPDATE wallets 
                SET balance = balance - NEW.amount,
                    updated_at = NOW()
                WHERE user_id = NEW.user_id;
                
                -- Update total withdrawn for withdrawals
                IF NEW.type = 'withdrawal' THEN
                    UPDATE wallets 
                    SET total_withdrawn = total_withdrawn + NEW.amount
                    WHERE user_id = NEW.user_id;
                END IF;
            END IF;
        END IF;
        RETURN NEW;
    END IF;
    
    -- Handle INSERT operations (transaction created directly with 'completed' status)
    IF TG_OP = 'INSERT' THEN
        IF NEW.status = 'completed' THEN
            IF NEW.type IN ('deposit', 'win', 'commission', 'refund') THEN
                -- Add to balance
                UPDATE wallets 
                SET balance = balance + NEW.amount,
                    updated_at = NOW()
                WHERE user_id = NEW.user_id;
                
                -- Update total deposited for deposits
                IF NEW.type = 'deposit' THEN
                    UPDATE wallets 
                    SET total_deposited = total_deposited + NEW.amount
                    WHERE user_id = NEW.user_id;
                END IF;
                
            ELSIF NEW.type IN ('withdrawal', 'bet') THEN
                -- Subtract from balance
                UPDATE wallets 
                SET balance = balance - NEW.amount,
                    updated_at = NOW()
                WHERE user_id = NEW.user_id;
                
                -- Update total withdrawn for withdrawals
                IF NEW.type = 'withdrawal' THEN
                    UPDATE wallets 
                    SET total_withdrawn = total_withdrawn + NEW.amount
                    WHERE user_id = NEW.user_id;
                END IF;
            END IF;
        END IF;
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for both INSERT and UPDATE operations
CREATE TRIGGER update_wallet_balance_insert_trigger
    AFTER INSERT ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_wallet_balance();

CREATE TRIGGER update_wallet_balance_update_trigger
    AFTER UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_wallet_balance();

-- Add some logging to help debug issues
CREATE OR REPLACE FUNCTION log_wallet_transaction()
RETURNS TRIGGER AS $$
BEGIN
    -- Log transaction processing for debugging
    RAISE NOTICE 'Processing transaction: ID=%, Type=%, Amount=%, Status=%, User=%', 
        NEW.id, NEW.type, NEW.amount, NEW.status, NEW.user_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Optional: Create logging trigger (can be removed in production)
CREATE TRIGGER log_wallet_transaction_trigger
    BEFORE INSERT OR UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION log_wallet_transaction();

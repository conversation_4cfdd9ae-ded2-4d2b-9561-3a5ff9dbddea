import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Trophy,
  Calendar,
  Users,
  Clock,
  ArrowLeft,
  Wallet,
  ChevronRight,
  Info,
  MessageCircle,
  Share2,
  Copy,
  CheckCircle,
  User,
  Shield,
  AlertTriangle,
  X,
  Bell,
  ArrowRight
} from 'lucide-react';
import TournamentBracket from '../components/TournamentBracket';
import TournamentLeaderboard from '../components/TournamentLeaderboard';
import TournamentSeasonSelector from '../components/TournamentSeasonSelector';
import TournamentBracketSelector from '../components/TournamentBracketSelector';
import TournamentProgressIndicator from '../components/TournamentProgressIndicator';
import UserBracketCard from '../components/UserBracketCard';
import TournamentSeasonNavigation from '../components/TournamentSeasonNavigation';
import TournamentWinnersModal from '../components/TournamentWinnersModal';
import MatchResultModal from '../components/MatchResultModal';
import { getTournamentById } from '../services/tournamentService';
import { Tournament, TournamentSeason, TournamentBracket as TournamentBracketType, Match } from '../types/tournament';
import { getNextMatch, markMatchAsSeen } from '../services/matchService';
import { realTournamentService } from '../services/realTournamentService';

// Interfaces já definidas em types/tournament.ts

export default function TournamentDetailsPage() {
  const navigate = useNavigate();
  const { tournamentId } = useParams();
  const [activeTab, setActiveTab] = useState<'overview' | 'bracket' | 'leaderboard' | 'participants' | 'rules'>(() => {
    // Recuperar a última aba ativa do localStorage ou usar 'overview' como padrão
    return (localStorage.getItem(`tournament_${tournamentId}_tab`) as any) || 'overview';
  });
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [registered, setRegistered] = useState(() => {
    // Verificar se o usuário já está inscrito neste torneio
    const registeredTournaments = JSON.parse(localStorage.getItem('registeredTournaments') || '[]');
    return registeredTournaments.includes(tournamentId);
  });
  const [copied, setCopied] = useState(false);

  // Estado para controle de temporadas e subchaves
  const [tournament, setTournament] = useState<Tournament | null>(null);
  const [activeSeason, setActiveSeason] = useState<string>('');
  const [activeBracket, setActiveBracket] = useState<string>('');
  const [showWinnersModal, setShowWinnersModal] = useState(false);
  const [showResultModal, setShowResultModal] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Carregar dados do torneio
  useEffect(() => {
    const loadTournamentData = async () => {
      if (tournamentId) {
        try {
          console.log('🔄 Carregando dados do torneio...');

          // Tentar carregar da API real primeiro
          const apiTournament = await realTournamentService.getTournamentById(tournamentId);

          if (apiTournament) {
            console.log('✅ Torneio carregado da API:', apiTournament.id);
            // Converter dados da API para o formato esperado
            const tournamentData: Tournament = {
              id: apiTournament.id,
              title: apiTournament.title,
              description: apiTournament.description,
              game: apiTournament.game_name,
              gameIcon: apiTournament.game_icon_url || 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
              status: apiTournament.status as any,
              startDate: apiTournament.start_date,
              endDate: apiTournament.end_date,
              registrationDeadline: apiTournament.registration_deadline,
              prizePool: apiTournament.prize_pool,
              entryFee: apiTournament.entry_fee,
              totalParticipants: apiTournament.max_participants,
              currentParticipants: apiTournament.current_participants,
              format: apiTournament.format as any,
              prizeDistribution: {
                first: 50,
                second: 30,
                third: 20
              },
              seasons: [],
              currentSeasonId: 'season_1',
              userBracket: null
            };
            setTournament(tournamentData);
          } else {
            // Fallback para dados mock
            console.warn('⚠️ API não retornou dados, usando fallback mock');
            const tournamentData = getTournamentById(tournamentId);
            setTournament(tournamentData);
          }
        } catch (error) {
          console.error('❌ Erro ao carregar torneio da API, usando fallback:', error);
          // Fallback para dados mock
          const tournamentData = getTournamentById(tournamentId);
          setTournament(tournamentData);

          // Definir temporada e subchave ativas iniciais
          if (tournamentData && tournamentData.seasons && tournamentData.seasons.length > 0) {
            // Usar a temporada atual do torneio como padrão
            setActiveSeason(tournamentData.currentSeasonId);

            // Se o usuário tiver uma subchave, usar como padrão
            if (tournamentData.userBracket) {
              // Se a subchave do usuário estiver na temporada atual, usá-la
              if (tournamentData.userBracket.seasonId === tournamentData.currentSeasonId) {
                setActiveBracket(tournamentData.userBracket.bracketId);
              } else {
                // Caso contrário, usar a primeira subchave da temporada atual
                const currentSeason = tournamentData.seasons.find(s => s.id === tournamentData.currentSeasonId);
                if (currentSeason && currentSeason.brackets.length > 0) {
                  setActiveBracket(currentSeason.brackets[0].id);
                }
              }
            } else {
              // Se não houver subchave do usuário, usar a primeira da temporada atual
              const currentSeason = tournamentData.seasons.find(s => s.id === tournamentData.currentSeasonId);
              if (currentSeason && currentSeason.brackets.length > 0) {
                setActiveBracket(currentSeason.brackets[0].id);
              }
            }
          }
        }
      }
    };

    loadTournamentData();
  }, [tournamentId]);

  // Atualizar subchave ativa quando a temporada mudar
  useEffect(() => {
    if (tournament && activeSeason) {
      const season = tournament.seasons.find(s => s.id === activeSeason);
      if (season && season.brackets.length > 0) {
        // Verificar se o usuário tem uma subchave nesta temporada
        if (tournament.userBracket && tournament.userBracket.seasonId === activeSeason) {
          setActiveBracket(tournament.userBracket.bracketId);
        } else {
          // Caso contrário, usar a primeira subchave da temporada
          setActiveBracket(season.brackets[0].id);
        }
      }
    }
  }, [activeSeason, tournament]);

  // Obter temporada e subchave ativas
  const getActiveSeason = (): TournamentSeason | undefined => {
    return tournament?.seasons.find(s => s.id === activeSeason);
  };

  const getActiveBracket = (): TournamentBracketType | undefined => {
    const season = getActiveSeason();
    return season?.brackets.find(b => b.id === activeBracket);
  };

  const handleRegister = async () => {
    try {
      console.log('🔄 Registrando no torneio...');

      // Tentar registrar via API real
      if (tournamentId) {
        const success = await realTournamentService.registerForTournament(tournamentId);

        if (success) {
          console.log('✅ Registro realizado via API');
        } else {
          console.warn('⚠️ Falha no registro via API, continuando localmente');
        }
      }

      // Atualizar o estado local independentemente do resultado da API
      setRegistered(true);
      setShowRegisterModal(false);

      // Persistir a inscrição no localStorage
      const registeredTournaments = JSON.parse(localStorage.getItem('registeredTournaments') || '[]');
      if (!registeredTournaments.includes(tournamentId)) {
        registeredTournaments.push(tournamentId);
        localStorage.setItem('registeredTournaments', JSON.stringify(registeredTournaments));
      }
    } catch (error) {
      console.error('❌ Erro no registro, continuando localmente:', error);

      // Fallback para registro local
      setRegistered(true);
      setShowRegisterModal(false);

      const registeredTournaments = JSON.parse(localStorage.getItem('registeredTournaments') || '[]');
      if (!registeredTournaments.includes(tournamentId)) {
        registeredTournaments.push(tournamentId);
        localStorage.setItem('registeredTournaments', JSON.stringify(registeredTournaments));
      }
    }
  };

  const handleCancelRegistration = () => {
    // Atualizar o estado local
    setRegistered(false);

    // Remover a inscrição do localStorage
    const registeredTournaments = JSON.parse(localStorage.getItem('registeredTournaments') || '[]');
    const updatedTournaments = registeredTournaments.filter((id: string) => id !== tournamentId);
    localStorage.setItem('registeredTournaments', JSON.stringify(updatedTournaments));
  };

  // Função para abrir o modal de envio de resultado
  const handleOpenResultModal = (match: Match) => {
    setSelectedMatch(match);
    setShowResultModal(true);
  };

  // Função para processar o resultado enviado
  const handleResultSubmit = (winner: string, player1Score: number, player2Score: number, awardPrize?: boolean) => {
    if (!selectedMatch || !tournament) return;

    // Em uma implementação real, você enviaria esses dados para o backend
    console.log('Resultado enviado:', {
      matchId: selectedMatch.id,
      winner,
      player1Score,
      player2Score,
      awardPrize
    });

    // Atualizar o estado local para refletir o resultado
    const updatedTournament = { ...tournament };
    const season = updatedTournament.seasons.find(s => s.id === activeSeason);
    if (season) {
      const bracket = season.brackets.find(b => b.id === activeBracket);
      if (bracket) {
        const matchIndex = bracket.matches.findIndex(m => m.id === selectedMatch.id);
        if (matchIndex !== -1) {
          // Determinar o ID do vencedor
          let winnerId = null;

          // Se o prêmio deve ser concedido, o vencedor deve ser um dos jogadores
          if (awardPrize) {
            // Verificar se o nome do vencedor corresponde a um dos jogadores
            if (winner === selectedMatch.player1?.name) {
              winnerId = selectedMatch.player1?.id;
            } else if (winner === selectedMatch.player2?.name) {
              winnerId = selectedMatch.player2?.id;
            }
          }

          // Atualizar o match com o resultado
          bracket.matches[matchIndex] = {
            ...bracket.matches[matchIndex],
            status: 'completed',
            winner: winnerId,
            awardPrize: awardPrize,
            player1: {
              ...bracket.matches[matchIndex].player1!,
              score: player1Score
            },
            player2: {
              ...bracket.matches[matchIndex].player2!,
              score: player2Score
            }
          };

          // Adicionar mensagem no console para debug
          console.log(`Match atualizado: ID=${selectedMatch.id}, Vencedor=${winner}, ID do vencedor=${winnerId}, Prêmio concedido=${awardPrize}`);
        }
      }
    }

    setTournament(updatedTournament);
    setShowResultModal(false);
    setSelectedMatch(null);
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(`https://playstrike.com/tournaments/${tournamentId}`);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/tournaments')}
          className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Voltar</span>
        </button>
      </div>

      {/* Tournament Header */}
      {tournament && (
        <div className="relative overflow-hidden bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-xl p-6">
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mt-10 -mr-10 blur-xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -mb-8 -ml-8 blur-xl"></div>

          <div className="relative z-10">
            <div className="flex items-center gap-3 mb-2">
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-2">
                <img
                  src={tournament.gameIcon}
                  alt={tournament.game}
                  className="w-10 h-10 object-cover rounded-md"
                />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <span className="text-white/80 text-sm">{tournament.game}</span>
                  <span className="bg-white/20 text-white text-xs px-2 py-0.5 rounded-full">
                    {tournament.status === 'upcoming' ? 'Em breve' :
                     tournament.status === 'in_progress' ? 'Em andamento' : 'Concluído'}
                  </span>
                </div>
                <h1 className="text-2xl font-bold text-white">{tournament.title}</h1>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <Calendar className="w-4 h-4 text-white" />
                  <span className="text-white/80 text-sm">Data</span>
                </div>
                <p className="text-white font-medium">
                  {formatDate(tournament.startDate)}
                  {tournament.format === 'points' && tournament.startDate !== tournament.endDate &&
                    ` - ${formatDate(tournament.endDate)}`
                  }
                </p>
              </div>

              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <Trophy className="w-4 h-4 text-white" />
                  <span className="text-white/80 text-sm">Premiação</span>
                </div>
                <p className="text-white font-medium">R$ {tournament.prizePool.toLocaleString()}</p>
              </div>

              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <Users className="w-4 h-4 text-white" />
                  <span className="text-white/80 text-sm">Participantes</span>
                </div>
                <p className="text-white font-medium">{tournament.totalParticipants} total</p>
              </div>

              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <Clock className="w-4 h-4 text-white" />
                  <span className="text-white/80 text-sm">Inscrições até</span>
                </div>
                <p className="text-white font-medium">{formatDate(tournament.registrationDeadline)}</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-3 mt-6">
            {!registered ? (
              <button
                onClick={() => setShowRegisterModal(true)}
                className="bg-white/20 hover:bg-white/30 transition-colors text-white px-6 py-2 rounded-lg flex items-center gap-2 font-medium"
              >
                <Trophy className="w-5 h-5" />
                Inscrever-se
              </button>
            ) : (
              <div className="flex gap-2">
                <div className="bg-green-500/20 text-green-400 px-6 py-2 rounded-lg flex items-center gap-2 font-medium">
                  <CheckCircle className="w-5 h-5" />
                  Inscrito
                </div>
                <button
                  onClick={handleCancelRegistration}
                  className="bg-gray-700/50 hover:bg-gray-700/70 transition-colors text-gray-300 px-4 py-2 rounded-lg flex items-center gap-2 font-medium"
                >
                  <X className="w-5 h-5" />
                  Cancelar
                </button>
              </div>
            )}

            <button
              onClick={handleCopyLink}
              className="bg-white/20 hover:bg-white/30 transition-colors text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              {copied ? (
                <>
                  <CheckCircle className="w-5 h-5" />
                  Link copiado
                </>
              ) : (
                <>
                  <Share2 className="w-5 h-5" />
                  Compartilhar
                </>
              )}
            </button>
            </div>
          </div>
        </div>
      )}  {/* Fechamento correto do tournament && (...) */}

      {/* Tabs */}
      <div className="border-b border-gray-700">
        <div className="flex overflow-x-auto hide-scrollbar">
          <button
            onClick={() => {
              setActiveTab('overview');
              localStorage.setItem(`tournament_${tournamentId}_tab`, 'overview');
            }}
            className={`px-4 py-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'overview'
                ? 'text-white border-b-2 border-indigo-500'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Visão Geral
          </button>
          <button
            onClick={() => {
              setActiveTab('bracket');
              localStorage.setItem(`tournament_${tournamentId}_tab`, 'bracket');
            }}
            className={`px-4 py-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'bracket'
                ? 'text-white border-b-2 border-indigo-500'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Chaveamento
          </button>
          {tournament && tournament.format === 'points' && (
            <button
              onClick={() => {
                setActiveTab('leaderboard');
                localStorage.setItem(`tournament_${tournamentId}_tab`, 'leaderboard');
              }}
              className={`px-4 py-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'leaderboard'
                  ? 'text-white border-b-2 border-indigo-500'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Classificação
            </button>
          )}
          <button
            onClick={() => {
              setActiveTab('participants');
              localStorage.setItem(`tournament_${tournamentId}_tab`, 'participants');
            }}
            className={`px-4 py-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'participants'
                ? 'text-white border-b-2 border-indigo-500'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Participantes
          </button>
          <button
            onClick={() => {
              setActiveTab('rules');
              localStorage.setItem(`tournament_${tournamentId}_tab`, 'rules');
            }}
            className={`px-4 py-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'rules'
                ? 'text-white border-b-2 border-indigo-500'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Regras
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && tournament && (
          <div className="space-y-6">
            {/* Progresso do Torneio */}
            <TournamentProgressIndicator
              seasons={tournament.seasons}
              currentSeasonId={tournament.currentSeasonId}
            />

            {/* Sua Subchave (se o usuário estiver inscrito) */}
            {registered && tournament.userBracket && (
              <UserBracketCard
                tournament={tournament}
                onViewBracket={() => {
                  setActiveTab('bracket');
                  setActiveSeason(tournament.userBracket!.seasonId);
                  setActiveBracket(tournament.userBracket!.bracketId);
                  localStorage.setItem(`tournament_${tournamentId}_tab`, 'bracket');
                }}
              />
            )}

            {/* Tournament Description */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h2 className="text-white text-lg font-bold mb-4">Sobre o Torneio</h2>
              <p className="text-gray-300">{tournament.description}</p>

              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-2 flex items-center gap-2">
                    <Trophy className="w-5 h-5 text-yellow-400" />
                    Premiação
                  </h3>
                  <ul className="space-y-2">
                    <li className="flex items-center justify-between">
                      <span className="text-gray-300">1º Lugar:</span>
                      <span className="text-white font-medium">R$ {Math.round(tournament.prizePool * tournament.prizeDistribution.first / 100).toLocaleString()}</span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span className="text-gray-300">2º Lugar:</span>
                      <span className="text-white font-medium">R$ {Math.round(tournament.prizePool * tournament.prizeDistribution.second / 100).toLocaleString()}</span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span className="text-gray-300">3º Lugar:</span>
                      <span className="text-white font-medium">R$ {Math.round(tournament.prizePool * tournament.prizeDistribution.third / 100).toLocaleString()}</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-2 flex items-center gap-2">
                    <Info className="w-5 h-5 text-indigo-400" />
                    Informações
                  </h3>
                  <ul className="space-y-2">
                    <li className="flex items-center justify-between">
                      <span className="text-gray-300">Formato:</span>
                      <span className="text-white font-medium">
                        {tournament.format === 'elimination' ? 'Eliminatória' : 'Pontuação'}
                      </span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span className="text-gray-300">Taxa de Inscrição:</span>
                      <span className="text-white font-medium">R$ {tournament.entryFee}</span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span className="text-gray-300">Total de Participantes:</span>
                      <span className="text-white font-medium">{tournament.totalParticipants}</span>
                    </li>
                    <li className="flex items-center justify-between">
                      <span className="text-gray-300">Temporadas:</span>
                      <span className="text-white font-medium">{tournament.seasons.length}</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-2 flex items-center gap-2">
                    <User className="w-5 h-5 text-indigo-400" />
                    Organizador
                  </h3>
                  <div className="flex items-center gap-3">
                    <img
                      src={tournament.organizer.avatar}
                      alt={tournament.organizer.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div>
                      <p className="text-white font-medium">{tournament.organizer.name}</p>
                      <button className="text-indigo-400 text-sm hover:text-indigo-300">Ver perfil</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Your Next Match */}
            {registered && tournament.currentPlayerNextMatch && (
              <div className="bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-indigo-500/20 rounded-xl p-4 sm:p-6 mb-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-4">
                  <h2 className="text-white text-lg font-bold flex items-center gap-2">
                    <Calendar className="w-5 h-5 text-indigo-400" />
                    Sua Próxima Partida
                  </h2>
                  <button
                    onClick={() => {
                      // Salvar a aba atual no localStorage antes de navegar
                      localStorage.setItem(`tournament_${tournamentId}_tab`, 'overview');
                      // Marcar a próxima partida como vista para não mostrar o lembrete novamente
                      if (tournament.currentPlayerNextMatch) {
                        markMatchAsSeen(tournament.currentPlayerNextMatch.id);
                      }
                      navigate('/player-matches');
                    }}
                    className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center gap-1 self-end sm:self-auto"
                  >
                    Ver suas próximas partidas
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>

                <div className="bg-gray-800/70 backdrop-blur-sm rounded-lg p-3 sm:p-4">
                  {/* Layout para telas móveis (empilhado) */}
                  <div className="flex flex-col gap-4 sm:hidden">
                    <div className="flex justify-center items-center gap-3">
                      <div className="flex items-center gap-2">
                        <img
                          src={tournament.currentPlayerNextMatch.player1.avatar}
                          alt={tournament.currentPlayerNextMatch.player1.name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                        <div>
                          <span className="text-indigo-400 font-medium">{tournament.currentPlayerNextMatch.player1.name}</span>
                          <p className="text-gray-400 text-xs">Você</p>
                        </div>
                      </div>

                      <span className="text-white font-bold mx-2">VS</span>

                      <div className="flex items-center gap-2">
                        <div>
                          <span className="text-white font-medium">{tournament.currentPlayerNextMatch.player2.name}</span>
                          <p className="text-gray-400 text-xs">Oponente</p>
                        </div>
                        <img
                          src={tournament.currentPlayerNextMatch.player2.avatar}
                          alt={tournament.currentPlayerNextMatch.player2.name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      </div>
                    </div>

                    <div className="flex justify-center items-center gap-4">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4 text-indigo-400" />
                        <span className="text-white text-sm">{formatDate(tournament.currentPlayerNextMatch.scheduledTime)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4 text-indigo-400" />
                        <span className="text-white text-sm">
                          {new Date(tournament.currentPlayerNextMatch.scheduledTime).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Layout para tablets e desktop (lado a lado) */}
                  <div className="hidden sm:flex sm:items-center sm:justify-between">
                    <div className="flex items-center gap-2">
                      <img
                        src={tournament.currentPlayerNextMatch.player1.avatar}
                        alt={tournament.currentPlayerNextMatch.player1.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div>
                        <span className="text-indigo-400 font-medium">{tournament.currentPlayerNextMatch.player1.name}</span>
                        <p className="text-gray-400 text-xs">Você</p>
                      </div>
                    </div>

                    <div className="flex flex-col items-center">
                      <div className="flex items-center gap-2 mb-1">
                        <Calendar className="w-4 h-4 text-indigo-400" />
                        <span className="text-white">{formatDate(tournament.currentPlayerNextMatch.scheduledTime)}</span>
                      </div>
                      <div className="flex items-center gap-2 mb-1">
                        <Clock className="w-4 h-4 text-indigo-400" />
                        <span className="text-white">
                          {new Date(tournament.currentPlayerNextMatch.scheduledTime).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                      <span className="text-white font-bold mt-1">VS</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <div>
                        <span className="text-white font-medium">{tournament.currentPlayerNextMatch.player2.name}</span>
                        <p className="text-gray-400 text-xs text-right">Oponente</p>
                      </div>
                      <img
                        src={tournament.currentPlayerNextMatch.player2.avatar}
                        alt={tournament.currentPlayerNextMatch.player2.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    </div>
                  </div>

                  <div className="mt-3 flex justify-center sm:justify-end">
                    <button className="bg-indigo-500/20 text-indigo-400 px-3 py-1 rounded-lg flex items-center gap-1 text-sm hover:bg-indigo-500/30">
                      <Bell className="w-4 h-4" />
                      Receber lembrete
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Next Matches or Recent Results */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-white text-lg font-bold">Próximas Partidas</h2>
                <button
                  onClick={() => {
                    setActiveTab('bracket');
                    localStorage.setItem(`tournament_${tournamentId}_tab`, 'bracket');
                  }}
                  className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center gap-1"
                >
                  Ver todas
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-3">
                {getActiveBracket() && getActiveBracket()!.matches
                  .filter(match => match.status === 'scheduled' && match.player1 && match.player2)
                  .slice(0, 3)
                  .map(match => (
                    <div key={match.id} className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <img
                            src={match.player1!.avatar}
                            alt={match.player1!.name}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                          <span className="text-white font-medium">{match.player1!.name}</span>
                        </div>

                        <div className="flex flex-col items-center">
                          <span className="text-gray-400 text-xs mb-1">
                            {match.scheduledTime ? formatDate(match.scheduledTime) + ' - ' +
                              new Date(match.scheduledTime).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }) : 'Data a definir'}
                          </span>
                          <span className="text-white font-bold">VS</span>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className="text-white font-medium">{match.player2!.name}</span>
                          <img
                            src={match.player2!.avatar}
                            alt={match.player2!.name}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        </div>
                      </div>

                      {/* Botão para enviar resultado */}
                      <div className="mt-3 flex justify-end">
                        <button
                          onClick={() => handleOpenResultModal(match)}
                          className="bg-indigo-500/20 text-indigo-400 px-3 py-1 rounded-lg flex items-center gap-1 text-sm hover:bg-indigo-500/30"
                        >
                          <Upload className="w-4 h-4" />
                          Enviar Resultado
                        </button>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'bracket' && tournament && (
          <div className="space-y-6">
            {/* Progresso do Torneio */}
            <TournamentProgressIndicator
              seasons={tournament.seasons}
              currentSeasonId={tournament.currentSeasonId}
            />

            {/* Sua Subchave (se o usuário estiver inscrito) */}
            {registered && tournament.userBracket && (
              <UserBracketCard
                tournament={tournament}
                onViewBracket={() => {
                  setActiveSeason(tournament.userBracket!.seasonId);
                  setActiveBracket(tournament.userBracket!.bracketId);
                }}
              />
            )}

            {/* Seletor de Temporadas */}
            <TournamentSeasonSelector
              seasons={tournament.seasons}
              activeSeason={activeSeason}
              onSeasonChange={setActiveSeason}
            />

            {/* Navegação entre Temporadas */}
            <TournamentSeasonNavigation
              seasons={tournament.seasons}
              activeSeason={activeSeason}
              onSeasonChange={setActiveSeason}
              onShowWinners={() => setShowWinnersModal(true)}
            />

            {/* Seletor de Subchaves */}
            {getActiveSeason() && (
              <TournamentBracketSelector
                brackets={getActiveSeason()!.brackets}
                activeBracket={activeBracket}
                onBracketChange={setActiveBracket}
                userBracketId={tournament.userBracket?.bracketId}
              />
            )}

            {/* Chaveamento */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 overflow-x-auto">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-white text-lg font-bold">Chaveamento do Torneio</h2>
                {getActiveBracket() && (
                  <div className="bg-indigo-500/20 text-indigo-400 px-3 py-1 rounded-lg text-sm">
                    {getActiveSeason()?.name} - {getActiveBracket()?.name}
                  </div>
                )}
              </div>
              {getActiveBracket() ? (
                <TournamentBracket
                  matches={getActiveBracket()!.matches}
                  highlightParticipantId={tournament.userBracket?.seasonId === activeSeason ? 'current_player' : undefined}
                  bracketName={getActiveBracket()!.name}
                  bracketStatus={getActiveBracket()!.status}
                  winnerId={getActiveBracket()!.winnerId}
                />
              ) : (
                <div className="text-center py-8">
                  <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-white font-bold text-lg mb-2">Nenhuma subchave disponível</h3>
                  <p className="text-gray-400">Esta temporada ainda não possui subchaves definidas.</p>
                </div>
              )}
            </div>

            {/* Modal de Vencedores */}
            {showWinnersModal && getActiveSeason() && (
              <TournamentWinnersModal
                season={getActiveSeason()!}
                onClose={() => setShowWinnersModal(false)}
              />
            )}
          </div>
        )}

        {activeTab === 'leaderboard' && tournament && getActiveBracket() && (
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h2 className="text-white text-lg font-bold mb-4">Classificação - {getActiveSeason()?.name} - {getActiveBracket()?.name}</h2>
            <TournamentLeaderboard participants={getActiveBracket()!.participants} />
          </div>
        )}

        {activeTab === 'participants' && tournament && getActiveBracket() && (
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h2 className="text-white text-lg font-bold mb-4">Participantes ({getActiveBracket()!.participants.length}/{tournament.totalParticipants})</h2>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {getActiveBracket()!.participants.map(participant => (
                <div key={participant.id} className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-3 flex items-center gap-3">
                  <img
                    src={participant.avatar}
                    alt={participant.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div>
                    <p className="text-white font-medium">{participant.name}</p>
                    <span className={`text-xs ${
                      participant.status === 'confirmed' ? 'text-green-400' :
                      participant.status === 'registered' ? 'text-yellow-400' :
                      'text-red-400'
                    }`}>
                      {participant.status === 'confirmed' ? 'Confirmado' :
                       participant.status === 'registered' ? 'Pendente' :
                       'Eliminado'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'rules' && tournament && (
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h2 className="text-white text-lg font-bold mb-4">Regras do Torneio</h2>
            <div className="prose prose-invert max-w-none">
              <pre className="whitespace-pre-wrap text-gray-300 font-sans">{tournament.rules}</pre>
            </div>
          </div>
        )}
      </div>

      {/* Match Result Modal */}
      {showResultModal && selectedMatch && (
        <MatchResultModal
          onClose={() => {
            setShowResultModal(false);
            setSelectedMatch(null);
            setSelectedFile(null);
          }}
          onResultSubmit={handleResultSubmit}
          match={selectedMatch}
          selectedFile={selectedFile}
          onFileSelect={setSelectedFile}
        />
      )}

      {/* Registration Modal */}
      {showRegisterModal && tournament && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl w-full max-w-md shadow-xl">
            <div className="p-4 border-b border-gray-700 flex items-center justify-between">
              <h3 className="text-white text-lg font-bold">Inscrição no Torneio</h3>
              <button
                onClick={() => setShowRegisterModal(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="p-6 space-y-6">
              <div className="flex items-center gap-4">
                <div className="bg-gray-700 p-2 rounded-lg">
                  <Trophy className="w-8 h-8 text-indigo-400" />
                </div>
                <div>
                  <h4 className="text-white font-bold">{tournament.title}</h4>
                  <p className="text-gray-400 text-sm">{tournament.game}</p>
                </div>
              </div>

              <div className="bg-gray-700/30 backdrop-blur-sm border border-white/5 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <Info className="w-5 h-5 text-indigo-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-gray-300 text-sm">
                      Taxa de inscrição: <span className="text-white font-semibold">R$ {tournament.entryFee}</span>
                    </p>
                    <p className="text-gray-300 text-sm mt-1">
                      Seu saldo atual: <span className="text-white font-semibold">R$ 1.248,00</span>
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-500/10 rounded-lg p-4 flex items-start gap-2">
                <AlertTriangle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
                <p className="text-yellow-200 text-sm">
                  Ao se inscrever, você concorda com as regras do torneio e confirma que estará disponível nas datas das partidas.
                </p>
              </div>

              <div className="flex items-center gap-3">
                <Shield className="w-5 h-5 text-indigo-400" />
                <p className="text-gray-300 text-sm">
                  Sua inscrição será confirmada após o pagamento da taxa.
                </p>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowRegisterModal(false)}
                  className="flex-1 bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white font-bold py-3 rounded-lg hover:bg-gray-700/70"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleRegister}
                  className="flex-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-3 rounded-lg hover:opacity-90"
                >
                  Confirmar Inscrição
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

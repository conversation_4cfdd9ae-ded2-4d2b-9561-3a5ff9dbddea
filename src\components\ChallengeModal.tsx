import React, { useState } from 'react';
import { X, Trophy, Wallet, Gamepad2 } from 'lucide-react';

interface ChallengeModalProps {
  friend: {
    id: string;
    name: string;
    avatar: string;
  };
  onClose: () => void;
  onChallenge: (gameId: string, betAmount: number) => void;
}

interface Game {
  id: string;
  title: string;
  imageUrl: string;
  minBet: number;
}

export default function ChallengeModal({ friend, onClose, onChallenge }: ChallengeModalProps) {
  const [selectedGame, setSelectedGame] = useState<string>('');
  const [betAmount, setBetAmount] = useState<number>(10);

  const games: Game[] = [
    {
      id: '1',
      title: 'Counter-Strike 2',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      minBet: 10
    },
    {
      id: '2',
      title: 'FIFA 24',
      imageUrl: 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80',
      minBet: 20
    },
    {
      id: '4',
      title: 'Call of Duty: Mobile',
      imageUrl: 'https://images.unsplash.com/photo-1509198397868-475647b2a1e5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1947&q=80',
      minBet: 15
    }
  ];

  const selectedGameInfo = games.find(game => game.id === selectedGame);

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-xl w-full max-w-md">
        {/* Header */}
        <div className="p-4 border-b border-gray-700 flex items-center justify-between">
          <h3 className="text-white text-lg font-bold">Desafiar para uma Partida</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Friend Info */}
          <div className="flex items-center gap-3">
            <img
              src={friend.avatar}
              alt={friend.name}
              className="w-12 h-12 rounded-full object-cover"
            />
            <div>
              <h4 className="text-white font-semibold">{friend.name}</h4>
              <p className="text-sm text-gray-400">Online</p>
            </div>
          </div>

          {/* Game Selection */}
          <div>
            <label className="block text-gray-400 text-sm mb-2">Escolha o Jogo</label>
            <div className="grid grid-cols-2 gap-3">
              {games.map(game => (
                <button
                  key={game.id}
                  onClick={() => {
                    setSelectedGame(game.id);
                    setBetAmount(game.minBet);
                  }}
                  className={`relative aspect-video rounded-lg overflow-hidden group ${
                    selectedGame === game.id
                      ? 'ring-2 ring-rose-400'
                      : 'hover:ring-2 hover:ring-gray-600'
                  }`}
                >
                  <img
                    src={game.imageUrl}
                    alt={game.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent p-3 flex items-end">
                    <div>
                      <h5 className="text-white text-sm font-semibold">{game.title}</h5>
                      <p className="text-rose-400 text-xs">Min: R$ {game.minBet}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {selectedGame && (
            <>
              {/* Bet Amount */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Valor da Aposta</label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">R$</span>
                  <input
                    type="number"
                    min={selectedGameInfo?.minBet}
                    step="5"
                    value={betAmount}
                    onChange={(e) => setBetAmount(Number(e.target.value))}
                    className="w-full bg-gray-700 text-white rounded-lg py-3 pl-10 pr-3 focus:outline-none focus:ring-2 focus:ring-rose-400"
                  />
                </div>
              </div>

              {/* Challenge Button */}
              <button
                onClick={() => onChallenge(selectedGame, betAmount)}
                className="w-full bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold py-3 rounded-lg flex items-center justify-center gap-2"
              >
                <Trophy className="w-5 h-5" />
                Enviar Desafio
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
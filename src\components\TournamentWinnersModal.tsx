import React from 'react';
import { TournamentSeason, TournamentBracket } from '../types/tournament';
import { X, Trophy, Medal } from 'lucide-react';

interface TournamentWinnersModalProps {
  season: TournamentSeason;
  onClose: () => void;
}

export default function TournamentWinnersModal({
  season,
  onClose
}: TournamentWinnersModalProps) {
  // Filtrar apenas subchaves concluídas com vencedores
  const completedBrackets = season.brackets.filter(
    bracket => bracket.status === 'completed' && bracket.winnerId
  );
  
  // Função para obter o nome do vencedor de uma subchave
  const getWinnerName = (bracket: TournamentBracket): string => {
    const winner = bracket.participants.find(p => p.id === bracket.winnerId);
    return winner ? winner.name : 'Desconhecido';
  };
  
  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fadeIn">
      <div className="bg-gray-800/90 backdrop-blur-sm border border-indigo-500/20 rounded-xl w-full max-w-2xl shadow-xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-white" />
            <h3 className="text-white font-bold">Vencedores - {season.name}</h3>
          </div>
          <button 
            onClick={onClose}
            className="text-white/80 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-5">
          {completedBrackets.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {completedBrackets.map(bracket => (
                <div 
                  key={bracket.id}
                  className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-yellow-500/20"
                >
                  <div className="flex items-center gap-2 mb-3">
                    <div className="bg-yellow-500/20 p-2 rounded-full">
                      <Trophy className="w-4 h-4 text-yellow-400" />
                    </div>
                    <div>
                      <h4 className="text-white font-medium">{bracket.name}</h4>
                      <p className="text-gray-400 text-xs">{bracket.participants.length} participantes</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    {bracket.participants.find(p => p.id === bracket.winnerId)?.avatar && (
                      <img
                        src={bracket.participants.find(p => p.id === bracket.winnerId)?.avatar}
                        alt={getWinnerName(bracket)}
                        className="w-10 h-10 rounded-full object-cover border-2 border-yellow-500"
                      />
                    )}
                    <div>
                      <p className="text-yellow-400 font-medium">{getWinnerName(bracket)}</p>
                      <p className="text-gray-400 text-xs">Vencedor</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Medal className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <h4 className="text-white font-bold text-lg mb-2">Nenhum vencedor ainda</h4>
              <p className="text-gray-400">
                As subchaves desta temporada ainda não foram concluídas.
              </p>
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t border-gray-700 flex justify-end">
          <button
            onClick={onClose}
            className="bg-gray-700 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-600"
          >
            Fechar
          </button>
        </div>
      </div>
    </div>
  );
}

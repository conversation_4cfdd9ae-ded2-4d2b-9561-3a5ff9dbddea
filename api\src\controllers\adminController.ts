import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, SystemSettingsData } from '../types/api';
import { logger, logBusinessEvent, logSecurityEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';

/**
 * Get admin dashboard data
 */
export const getAdminDashboard = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    // Get user statistics
    const { count: totalUsers } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true });

    const { count: activeUsers } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');

    const { count: newUsersToday } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', new Date().toISOString().split('T')[0]);

    // Get match statistics
    const { count: totalMatches } = await supabaseAdmin
      .from('matches')
      .select('*', { count: 'exact', head: true });

    const { count: activeMatches } = await supabaseAdmin
      .from('matches')
      .select('*', { count: 'exact', head: true })
      .in('status', ['scheduled', 'in_progress']);

    const { count: completedMatches } = await supabaseAdmin
      .from('matches')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'completed');

    // Get tournament statistics
    const { count: totalTournaments } = await supabaseAdmin
      .from('tournaments')
      .select('*', { count: 'exact', head: true });

    const { count: activeTournaments } = await supabaseAdmin
      .from('tournaments')
      .select('*', { count: 'exact', head: true })
      .in('status', ['registration_open', 'in_progress']);

    // Get financial statistics
    const { data: financialStats } = await supabaseAdmin
      .from('transactions')
      .select('type, amount, status')
      .eq('status', 'completed');

    const totalDeposits = financialStats?.filter(t => t.type === 'deposit').reduce((sum, t) => sum + t.amount, 0) || 0;
    const totalWithdrawals = financialStats?.filter(t => t.type === 'withdrawal').reduce((sum, t) => sum + t.amount, 0) || 0;
    const totalRevenue = financialStats?.filter(t => t.type === 'bet').reduce((sum, t) => sum + t.amount, 0) || 0;

    // Get pending transactions
    const { count: pendingTransactions } = await supabaseAdmin
      .from('transactions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    // Get recent activity
    const { data: recentUsers } = await supabaseAdmin
      .from('users')
      .select('id, username, display_name, created_at, status')
      .order('created_at', { ascending: false })
      .limit(10);

    const { data: recentMatches } = await supabaseAdmin
      .from('matches')
      .select(`
        id, status, entry_fee, created_at,
        games (name),
        match_participants (
          users (username)
        )
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    // Get system health
    const systemHealth = {
      database: 'healthy',
      api: 'healthy',
      websockets: 'healthy',
      external_services: 'healthy'
    };

    const response: ApiResponse = {
      success: true,
      data: {
        statistics: {
          users: {
            total: totalUsers || 0,
            active: activeUsers || 0,
            new_today: newUsersToday || 0
          },
          matches: {
            total: totalMatches || 0,
            active: activeMatches || 0,
            completed: completedMatches || 0
          },
          tournaments: {
            total: totalTournaments || 0,
            active: activeTournaments || 0
          },
          financial: {
            total_deposits: totalDeposits,
            total_withdrawals: totalWithdrawals,
            total_revenue: totalRevenue,
            pending_transactions: pendingTransactions || 0
          }
        },
        recent_activity: {
          users: recentUsers || [],
          matches: recentMatches || []
        },
        system_health: systemHealth
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get admin dashboard error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get all users (admin view)
 */
export const getUsers = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { page, limit } = extractPagination(req.query);
    const { status, search, sort_by = 'created_at', sort_order = 'desc' } = req.query;

    let query = supabaseAdmin
      .from('users')
      .select(`
        id, email, username, display_name, status, created_at, last_login_at,
        user_profiles (country, timezone),
        user_stats (total_matches, total_wins, total_earnings)
      `, { count: 'exact' });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (search) {
      query = query.or(`username.ilike.%${search}%,email.ilike.%${search}%,display_name.ilike.%${search}%`);
    }

    // Apply sorting
    const ascending = sort_order === 'asc';
    query = query.order(sort_by as string, { ascending });

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data: users, error, count } = await query;

    if (error) {
      logger.error('Failed to get users:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve users'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        users: users || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get users error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Update user status
 */
export const updateUserStatus = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const admin = req.user!;
    const { id } = req.params;
    const { status, reason } = req.body;

    // Validate status
    const validStatuses = ['active', 'suspended', 'banned', 'inactive'];
    if (!validStatuses.includes(status)) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid status'
      };
      res.status(400).json(response);
      return;
    }

    // Get user details
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, username, status')
      .eq('id', id)
      .single();

    if (userError || !user) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      res.status(404).json(response);
      return;
    }

    // Prevent admin from changing their own status
    if (user.id === admin.id) {
      const response: ApiResponse = {
        success: false,
        error: 'Cannot change your own status'
      };
      res.status(400).json(response);
      return;
    }

    // Update user status
    const { error: updateError } = await supabaseAdmin
      .from('users')
      .update({ status })
      .eq('id', id);

    if (updateError) {
      logger.error('Failed to update user status:', updateError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to update user status'
      };
      res.status(500).json(response);
      return;
    }

    // Log admin action
    await supabaseAdmin
      .from('admin_actions')
      .insert({
        admin_user_id: admin.id,
        action_type: 'user_status_change',
        target_user_id: id,
        details: {
          old_status: user.status,
          new_status: status,
          reason
        }
      });

    logSecurityEvent('user_status_changed', 'medium', {
      admin_user_id: admin.id,
      target_user_id: id,
      old_status: user.status,
      new_status: status,
      reason
    });

    const response: ApiResponse = {
      success: true,
      message: 'User status updated successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Update user status error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get all transactions (admin view)
 */
export const getTransactions = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { page, limit } = extractPagination(req.query);
    const { type, status, user_id, date_from, date_to } = req.query;

    let query = supabaseAdmin
      .from('transactions')
      .select(`
        id, type, amount, status, created_at, description,
        users (id, username, email),
        wallets (id)
      `, { count: 'exact' });

    // Apply filters
    if (type) {
      query = query.eq('type', type);
    }
    if (status) {
      query = query.eq('status', status);
    }
    if (user_id) {
      query = query.eq('user_id', user_id);
    }
    if (date_from) {
      query = query.gte('created_at', date_from);
    }
    if (date_to) {
      query = query.lte('created_at', date_to);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: transactions, error, count } = await query;

    if (error) {
      logger.error('Failed to get transactions:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve transactions'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        transactions: transactions || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get transactions error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get user reports
 */
export const getReports = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { page, limit } = extractPagination(req.query);
    const { status, type, severity } = req.query;

    let query = supabaseAdmin
      .from('user_reports')
      .select(`
        id, report_type, reason, description, status, severity, created_at,
        reporter:users!user_reports_reporter_user_id_fkey (
          id, username, display_name
        ),
        reported:users!user_reports_reported_user_id_fkey (
          id, username, display_name
        )
      `, { count: 'exact' });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (type) {
      query = query.eq('report_type', type);
    }
    if (severity) {
      query = query.eq('severity', severity);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: reports, error, count } = await query;

    if (error) {
      logger.error('Failed to get reports:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve reports'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        reports: reports || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get reports error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get system settings
 */
export const getSystemSettings = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { data: settings, error } = await supabaseAdmin
      .from('system_settings')
      .select('*')
      .order('category');

    if (error) {
      logger.error('Failed to get system settings:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve system settings'
      };
      res.status(500).json(response);
      return;
    }

    // Group settings by category
    const groupedSettings = (settings || []).reduce((acc: any, setting: any) => {
      if (!acc[setting.category]) {
        acc[setting.category] = {};
      }
      acc[setting.category][setting.key] = {
        value: setting.value,
        description: setting.description,
        type: setting.type,
        updated_at: setting.updated_at
      };
      return acc;
    }, {});

    const response: ApiResponse = {
      success: true,
      data: {
        settings: groupedSettings
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get system settings error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Update system settings
 */
export const updateSystemSettings = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const admin = req.user!;
    const settingsData: SystemSettingsData = req.body;

    const updatedSettings = [];

    // Update each setting
    for (const [category, categorySettings] of Object.entries(settingsData)) {
      for (const [key, value] of Object.entries(categorySettings as any)) {
        const { error } = await supabaseAdmin
          .from('system_settings')
          .upsert({
            category,
            key,
            value,
            updated_by: admin.id,
            updated_at: new Date().toISOString()
          });

        if (error) {
          logger.error(`Failed to update setting ${category}.${key}:`, error);
          continue;
        }

        updatedSettings.push(`${category}.${key}`);
      }
    }

    // Log admin action
    await supabaseAdmin
      .from('admin_actions')
      .insert({
        admin_user_id: admin.id,
        action_type: 'system_settings_update',
        details: {
          updated_settings: updatedSettings,
          settings_data: settingsData
        }
      });

    logBusinessEvent('system_settings_updated', admin.id, {
      updated_settings: updatedSettings
    });

    const response: ApiResponse = {
      success: true,
      message: 'System settings updated successfully',
      data: {
        updated_settings: updatedSettings
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Update system settings error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get admin actions log
 */
export const getAdminActions = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { page, limit } = extractPagination(req.query);
    const { admin_user_id, action_type, date_from, date_to } = req.query;

    let query = supabaseAdmin
      .from('admin_actions')
      .select(`
        id, action_type, details, created_at,
        admin:users!admin_actions_admin_user_id_fkey (
          id, username, display_name
        ),
        target:users!admin_actions_target_user_id_fkey (
          id, username, display_name
        )
      `, { count: 'exact' });

    // Apply filters
    if (admin_user_id) {
      query = query.eq('admin_user_id', admin_user_id);
    }
    if (action_type) {
      query = query.eq('action_type', action_type);
    }
    if (date_from) {
      query = query.gte('created_at', date_from);
    }
    if (date_to) {
      query = query.lte('created_at', date_to);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: actions, error, count } = await query;

    if (error) {
      logger.error('Failed to get admin actions:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve admin actions'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        actions: actions || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get admin actions error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Process report action
 */
export const processReport = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const admin = req.user!;
    const { id } = req.params;
    const { action, reason } = req.body;

    // Validate action
    const validActions = ['dismiss', 'warn', 'suspend', 'ban'];
    if (!validActions.includes(action)) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid action'
      };
      res.status(400).json(response);
      return;
    }

    // Get report details
    const { data: report, error: reportError } = await supabaseAdmin
      .from('user_reports')
      .select('*')
      .eq('id', id)
      .single();

    if (reportError || !report) {
      const response: ApiResponse = {
        success: false,
        error: 'Report not found'
      };
      res.status(404).json(response);
      return;
    }

    // Update report status
    const { error: updateError } = await supabaseAdmin
      .from('user_reports')
      .update({
        status: 'resolved',
        admin_action: action,
        admin_reason: reason,
        resolved_by: admin.id,
        resolved_at: new Date().toISOString()
      })
      .eq('id', id);

    if (updateError) {
      logger.error('Failed to update report:', updateError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to process report'
      };
      res.status(500).json(response);
      return;
    }

    // Apply action to reported user if necessary
    if (action !== 'dismiss') {
      let newStatus = 'active';
      if (action === 'suspend') newStatus = 'suspended';
      if (action === 'ban') newStatus = 'banned';

      if (newStatus !== 'active') {
        await supabaseAdmin
          .from('users')
          .update({ status: newStatus })
          .eq('id', report.reported_user_id);
      }
    }

    // Log admin action
    await supabaseAdmin
      .from('admin_actions')
      .insert({
        admin_user_id: admin.id,
        action_type: 'report_processed',
        target_user_id: report.reported_user_id,
        details: {
          report_id: id,
          action,
          reason,
          original_report: report.reason
        }
      });

    logSecurityEvent('report_processed', 'medium', {
      admin_user_id: admin.id,
      report_id: id,
      action,
      reported_user_id: report.reported_user_id
    });

    const response: ApiResponse = {
      success: true,
      message: 'Report processed successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Process report error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

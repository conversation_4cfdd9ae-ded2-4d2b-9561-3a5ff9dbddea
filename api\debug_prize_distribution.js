const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugPrizeDistribution() {
  console.log('🔍 Iniciando diagnóstico do sistema de prêmios...\n');

  try {
    // 1. Verificar transações de prêmio recentes
    console.log('1. Verificando transações de prêmio recentes...');
    const { data: winTransactions, error: winError } = await supabase
      .from('transactions')
      .select(`
        id,
        user_id,
        type,
        amount,
        status,
        description,
        created_at,
        completed_at,
        users!inner(username, email)
      `)
      .eq('type', 'win')
      .order('created_at', { ascending: false })
      .limit(10);

    if (winError) {
      console.error('❌ Erro ao buscar transações de prêmio:', winError);
    } else {
      console.log(`📊 Encontradas ${winTransactions.length} transações de prêmio recentes:`);
      winTransactions.forEach(tx => {
        console.log(`  - ID: ${tx.id}`);
        console.log(`    Usuário: ${tx.users.username} (${tx.user_id})`);
        console.log(`    Valor: R$ ${tx.amount}`);
        console.log(`    Status: ${tx.status}`);
        console.log(`    Criado: ${tx.created_at}`);
        console.log(`    Completado: ${tx.completed_at || 'N/A'}`);
        console.log(`    Descrição: ${tx.description}`);
        console.log('');
      });
    }

    // 2. Verificar partidas completadas recentes
    console.log('2. Verificando partidas completadas recentes...');
    const { data: matches, error: matchError } = await supabase
      .from('matches')
      .select(`
        id,
        status,
        winner_user_id,
        entry_fee,
        created_at,
        completed_at,
        match_participants!inner(
          user_id,
          earnings,
          placement,
          users!inner(username)
        )
      `)
      .eq('status', 'completed')
      .order('completed_at', { ascending: false })
      .limit(5);

    if (matchError) {
      console.error('❌ Erro ao buscar partidas:', matchError);
    } else {
      console.log(`🎮 Encontradas ${matches.length} partidas completadas recentes:`);
      matches.forEach(match => {
        console.log(`  - Match ID: ${match.id}`);
        console.log(`    Status: ${match.status}`);
        console.log(`    Vencedor: ${match.winner_user_id || 'N/A'}`);
        console.log(`    Taxa de entrada: R$ ${match.entry_fee || 0}`);
        console.log(`    Completada: ${match.completed_at}`);
        console.log(`    Participantes:`);
        match.match_participants.forEach(p => {
          console.log(`      - ${p.users.username} (${p.user_id})`);
          console.log(`        Colocação: ${p.placement || 'N/A'}`);
          console.log(`        Ganhos: R$ ${p.earnings || 0}`);
        });
        console.log('');
      });
    }

    // 3. Verificar saldos de wallet recentes
    console.log('3. Verificando atualizações de saldo recentes...');
    const { data: wallets, error: walletError } = await supabase
      .from('wallets')
      .select(`
        id,
        user_id,
        balance,
        updated_at,
        users!inner(username)
      `)
      .order('updated_at', { ascending: false })
      .limit(10);

    if (walletError) {
      console.error('❌ Erro ao buscar wallets:', walletError);
    } else {
      console.log(`💰 Últimas atualizações de saldo:`);
      wallets.forEach(wallet => {
        console.log(`  - ${wallet.users.username}: R$ ${wallet.balance}`);
        console.log(`    Atualizado: ${wallet.updated_at}`);
      });
      console.log('');
    }

    // 4. Verificar se há transações de prêmio sem correspondência no saldo
    console.log('4. Verificando inconsistências...');
    const { data: inconsistentTransactions, error: inconsistentError } = await supabase
      .from('transactions')
      .select(`
        id,
        user_id,
        amount,
        status,
        created_at,
        users!inner(username),
        wallets!inner(balance, updated_at)
      `)
      .eq('type', 'win')
      .eq('status', 'completed')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Últimas 24h

    if (inconsistentError) {
      console.error('❌ Erro ao verificar inconsistências:', inconsistentError);
    } else {
      console.log(`🔍 Transações de prêmio completadas nas últimas 24h: ${inconsistentTransactions.length}`);
      
      if (inconsistentTransactions.length === 0) {
        console.log('⚠️ Nenhuma transação de prêmio completada encontrada nas últimas 24h!');
      } else {
        inconsistentTransactions.forEach(tx => {
          console.log(`  - ${tx.users.username}: R$ ${tx.amount} (${tx.created_at})`);
          console.log(`    Saldo atual: R$ ${tx.wallets.balance}`);
          console.log(`    Wallet atualizada: ${tx.wallets.updated_at}`);
        });
      }
    }

    // 5. Testar trigger manualmente
    console.log('\n5. Testando trigger de atualização de saldo...');
    
    // Buscar um usuário para teste
    const { data: testUser, error: userError } = await supabase
      .from('users')
      .select('id, username')
      .limit(1)
      .single();

    if (userError || !testUser) {
      console.error('❌ Erro ao buscar usuário para teste:', userError);
    } else {
      console.log(`👤 Usando usuário de teste: ${testUser.username} (${testUser.id})`);
      
      // Buscar wallet do usuário
      const { data: testWallet, error: walletTestError } = await supabase
        .from('wallets')
        .select('id, balance')
        .eq('user_id', testUser.id)
        .single();

      if (walletTestError || !testWallet) {
        console.error('❌ Erro ao buscar wallet do usuário:', walletTestError);
      } else {
        console.log(`💰 Saldo atual: R$ ${testWallet.balance}`);
        
        // Criar transação de teste
        const testAmount = 10.00;
        const { data: testTransaction, error: txError } = await supabase
          .from('transactions')
          .insert({
            user_id: testUser.id,
            wallet_id: testWallet.id,
            type: 'win',
            amount: testAmount,
            description: 'Teste de diagnóstico - prêmio',
            status: 'completed'
          })
          .select()
          .single();

        if (txError) {
          console.error('❌ Erro ao criar transação de teste:', txError);
        } else {
          console.log(`✅ Transação de teste criada: ${testTransaction.id}`);
          
          // Aguardar um pouco para o trigger processar
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Verificar se o saldo foi atualizado
          const { data: updatedWallet, error: updateError } = await supabase
            .from('wallets')
            .select('balance, updated_at')
            .eq('user_id', testUser.id)
            .single();

          if (updateError) {
            console.error('❌ Erro ao verificar saldo atualizado:', updateError);
          } else {
            const expectedBalance = testWallet.balance + testAmount;
            if (Math.abs(updatedWallet.balance - expectedBalance) < 0.01) {
              console.log(`✅ Trigger funcionando! Saldo atualizado: R$ ${updatedWallet.balance}`);
            } else {
              console.log(`❌ Trigger NÃO funcionando! Saldo esperado: R$ ${expectedBalance}, Atual: R$ ${updatedWallet.balance}`);
            }
          }
          
          // Limpar transação de teste
          await supabase
            .from('transactions')
            .delete()
            .eq('id', testTransaction.id);
          
          // Reverter saldo se necessário
          if (updatedWallet && Math.abs(updatedWallet.balance - (testWallet.balance + testAmount)) < 0.01) {
            await supabase
              .from('wallets')
              .update({ balance: testWallet.balance })
              .eq('user_id', testUser.id);
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ Erro durante diagnóstico:', error);
  }
}

// Executar diagnóstico
debugPrizeDistribution()
  .then(() => {
    console.log('\n✅ Diagnóstico concluído!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

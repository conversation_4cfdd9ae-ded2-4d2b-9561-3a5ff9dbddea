import { Router } from 'express';
import {
  getMatches,
  createMatch,
  getMatchById,
  joinMatch,
  leaveMatch,
  submitMatchResult,
  disputeMatchResult,
  getMatchHistory,
  getUpcomingMatches,
  getMatchStats,
  forceFinalize
} from '../controllers/matchController';
import { validate, schemas } from '../middleware/validation';
import { authenticate, optionalAuthenticate } from '../middleware/auth';
import { matchCreationRateLimit } from '../middleware/rateLimiting';

const router = Router();

/**
 * @route   GET /api/matches/history
 * @desc    Get user's match history
 * @access  Private
 */
router.get('/history',
  authenticate,
  validate(schemas.pagination, 'query'),
  getMatchHistory
);

/**
 * @route   GET /api/matches/upcoming
 * @desc    Get user's upcoming matches
 * @access  Private
 */
router.get('/upcoming',
  authenticate,
  validate(schemas.pagination, 'query'),
  getUpcomingMatches
);

/**
 * @route   GET /api/matches/stats
 * @desc    Get user's match statistics
 * @access  Private
 */
router.get('/stats',
  authenticate,
  getMatchStats
);

/**
 * @route   GET /api/matches
 * @desc    Get matches (with filters)
 * @access  Public
 */
router.get('/',
  optionalAuthenticate,
  validate(schemas.pagination, 'query'),
  getMatches
);

/**
 * @route   POST /api/matches
 * @desc    Create a new match
 * @access  Private
 */
router.post('/',
  authenticate,
  matchCreationRateLimit,
  createMatch
);

/**
 * @route   GET /api/matches/:id
 * @desc    Get match by ID
 * @access  Public
 */
router.get('/:id',
  optionalAuthenticate,
  validate(schemas.uuidParam, 'params'),
  getMatchById
);

/**
 * @route   POST /api/matches/:id/join
 * @desc    Join a match
 * @access  Private
 */
router.post('/:id/join',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  joinMatch
);

/**
 * @route   POST /api/matches/:id/leave
 * @desc    Leave a match
 * @access  Private
 */
router.post('/:id/leave',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  leaveMatch
);

/**
 * @route   POST /api/matches/:id/result
 * @desc    Submit match result
 * @access  Private
 */
router.post('/:id/result',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  validate(schemas.matchResult),
  submitMatchResult
);

/**
 * @route   POST /api/matches/:id/dispute
 * @desc    Dispute match result
 * @access  Private
 */
router.post('/:id/dispute',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  validate(schemas.report),
  disputeMatchResult
);

/**
 * @route   POST /api/matches/:id/force-finalize
 * @desc    Force finalize a match (debugging)
 * @access  Public (for debugging)
 */
router.post('/:id/force-finalize',
  validate(schemas.uuidParam, 'params'),
  forceFinalize
);

export default router;

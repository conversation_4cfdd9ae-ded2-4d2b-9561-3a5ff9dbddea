import React, { useState } from 'react';
import { X, Users, Gamepad2 } from 'lucide-react';

interface CreateSquadModalProps {
  onClose: () => void;
  onCreateSquad: (squadData: any) => void;
  gameId: string;
  gameName: string;
}

export default function CreateSquadModal({ onClose, onCreateSquad, gameId, gameName }: CreateSquadModalProps) {
  const [squadName, setSquadName] = useState('');
  const [maxMembers, setMaxMembers] = useState(4);
  const [isPrivate, setIsPrivate] = useState(false);
  const [description, setDescription] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const squadData = {
      name: squadName,
      gameId,
      gameName,
      maxMembers,
      isPrivate,
      description,
      createdAt: new Date().toISOString()
    };
    
    onCreateSquad(squadData);
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-xl w-full max-w-md relative overflow-hidden">
        <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-white font-bold text-lg">Criar Squad</h2>
            <button 
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label htmlFor="squad-name" className="block text-gray-400 text-sm mb-1">
              Nome do Squad
            </label>
            <input
              id="squad-name"
              type="text"
              value={squadName}
              onChange={(e) => setSquadName(e.target.value)}
              placeholder="Digite um nome para o squad"
              className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
              required
            />
          </div>
          
          <div>
            <label htmlFor="game" className="block text-gray-400 text-sm mb-1">
              Jogo
            </label>
            <div className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 flex items-center gap-2">
              <Gamepad2 className="w-4 h-4 text-indigo-400" />
              <span>{gameName}</span>
            </div>
          </div>
          
          <div>
            <label htmlFor="max-members" className="block text-gray-400 text-sm mb-1">
              Número Máximo de Membros
            </label>
            <select
              id="max-members"
              value={maxMembers}
              onChange={(e) => setMaxMembers(Number(e.target.value))}
              className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400"
            >
              <option value={2}>2 jogadores</option>
              <option value={3}>3 jogadores</option>
              <option value={4}>4 jogadores</option>
              <option value={5}>5 jogadores</option>
              <option value={6}>6 jogadores</option>
            </select>
          </div>
          
          <div className="flex items-center gap-2">
            <input
              id="private-squad"
              type="checkbox"
              checked={isPrivate}
              onChange={(e) => setIsPrivate(e.target.checked)}
              className="w-4 h-4 bg-gray-700 border-gray-600 rounded focus:ring-indigo-400"
            />
            <label htmlFor="private-squad" className="text-white">
              Squad Privado (apenas por convite)
            </label>
          </div>
          
          <div>
            <label htmlFor="description" className="block text-gray-400 text-sm mb-1">
              Descrição (opcional)
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Descreva o objetivo do seu squad..."
              className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-400 h-24 resize-none"
            />
          </div>
          
          <div className="flex gap-3 pt-2">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-700 text-white font-semibold py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="flex-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-semibold py-2 rounded-lg hover:opacity-90 transition-opacity flex items-center justify-center gap-2"
              disabled={!squadName.trim()}
            >
              <Users className="w-4 h-4" />
              Criar Squad
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

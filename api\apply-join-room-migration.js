const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  try {
    console.log('🚀 Applying join room function migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../database/migrations/013_join_room_function.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the migration
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }
    
    console.log('✅ Join room function migration applied successfully');
    
    // Test the function exists
    const { data: functions, error: funcError } = await supabase
      .rpc('exec_sql', { 
        sql: "SELECT proname FROM pg_proc WHERE proname = 'join_game_room';" 
      });
    
    if (funcError) {
      console.error('❌ Error checking function:', funcError);
    } else {
      console.log('✅ Function join_game_room is available');
    }
    
  } catch (error) {
    console.error('❌ Error applying migration:', error);
    process.exit(1);
  }
}

applyMigration();

import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import HomePage from './pages/HomePage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import AffiliateProgramPage from './pages/AffiliateProgramPage';
import WalletPage from './pages/WalletPage';
import TournamentsPage from './pages/TournamentsPage';
import TournamentDetailsPage from './pages/TournamentDetailsPage';
import AffiliateReferralsPage from './pages/AffiliateReferralsPage';
import AffiliateEarningsPage from './pages/AffiliateEarningsPage';
import ClubsPage from './pages/ClubsPage';
import ExternalGamesPage from './pages/ExternalGamesPage';
import SocialPage from './pages/SocialPage';
import RankingPage from './pages/RankingPage';
import FifaGamePage from './pages/games/fifa/FifaGamePage';
import MobileLegendsPage from './pages/games/mobile-legends/MobileLegendsPage';
import CodMobilePage from './pages/games/cod-mobile/CodMobilePage';
import GameRoomsPage from './pages/GameRoomsPage';
import GameRoomPage from './pages/GameRoomPage';
import MatchmakingPage from './pages/MatchmakingPage';
import MatchAnalysisPage from './pages/MatchAnalysisPage';
import ExternalGamePage from './pages/ExternalGamePage';
import ClubPage from './pages/ClubPage';
import CreateClubPage from './pages/CreateClubPage';
import CodMatchmakingPage from './pages/cod-mobile/CodMatchmakingPage';
import StatsPage from './pages/StatsPage';
import ResultsPage from './pages/ResultsPage';
import StreamsPage from './pages/StreamsPage';
import FifaMatchmakingPage from './pages/fifa/FifaMatchmakingPage';
import ReactionGame from './pages/games/ReactionGame';
import FlapRocket from './pages/games/FlapRocket';
import ColorMatch from './pages/games/ColorMatch';
import WordScramble from './pages/games/WordScramble';
import StickmanArcher from './pages/games/StickmanArcher';
import Cs2MatchmakingPage from './pages/cs2/Cs2MatchmakingPage';
import MatchResultsPage from './pages/MatchResultsPage';
import PlayerMatchesPage from './pages/PlayerMatchesPage';
import InternalGamesPage from './pages/InternalGamesPage';
import FlapRocketPage from './pages/games/FlapRocketPage';
import ReactionGamePage from './pages/games/ReactionGamePage';
import ColorMatchPage from './pages/games/ColorMatchPage';
import WordScramblePage from './pages/games/WordScramblePage';
import StickmanArcherPage from './pages/games/StickmanArcherPage';
import ApiTestPage from './pages/ApiTestPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import { UserProvider } from './contexts/UserContext';

function App() {
  return (
    <UserProvider>
      <BrowserRouter>
        <Routes>
          {/* Rotas de autenticação fora do Layout */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />

          <Route path="/" element={<Layout />}>
            <Route index element={<ExternalGamesPage />} />
            <Route path="/external-games" element={<ExternalGamesPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="/affiliate-program" element={<AffiliateProgramPage />} />
            <Route path="/affiliate-program/referrals" element={<AffiliateReferralsPage />} />
            <Route path="/affiliate-program/earnings" element={<AffiliateEarningsPage />} />
            <Route path="/wallet" element={<WalletPage />} />
            <Route path="/tournaments" element={<TournamentsPage />} />
            <Route path="/tournaments/:tournamentId" element={<TournamentDetailsPage />} />
            <Route path="/player-matches/:playerId?" element={<PlayerMatchesPage />} />
            <Route path="/streams" element={<StreamsPage />} />
            <Route path="/social" element={<SocialPage />} />
            <Route path="/clubs" element={<ClubsPage />} />
            <Route path="/ranking" element={<RankingPage />} />
            <Route path="/stats" element={<StatsPage />} />
            <Route path="/streams" element={<StreamsPage />} />
            <Route path="/results" element={<ResultsPage />} />
            <Route path="/clubs/:clubId" element={<ClubPage />} />
            <Route path="/clubs/create" element={<CreateClubPage />} />
            <Route path="/external-games/:gameId" element={<ExternalGamePage />} />
            <Route path="/external-games/fifa" element={<FifaGamePage />} />
            <Route path="/external-games/mobile-legends" element={<MobileLegendsPage />} />
            <Route path="/external-games/cod-mobile" element={<CodMobilePage />} />
            <Route path="/external-games/cs2/matchmaking" element={<Cs2MatchmakingPage />} />
            <Route path="/external-games/cod-mobile/matchmaking" element={<CodMatchmakingPage />} />
            <Route path="/external-games/fifa/matchmaking" element={<FifaMatchmakingPage />} />
            <Route path="/internal-games" element={<InternalGamesPage />} />
            <Route path="/games/flaprocket" element={<FlapRocketPage />} />
            <Route path="/games/reaction" element={<ReactionGamePage />} />
            <Route path="/games/colormatch" element={<ColorMatchPage />} />
            <Route path="/games/wordscramble" element={<WordScramblePage />} />
            <Route path="/games/stickmanarcher" element={<StickmanArcherPage />} />
            <Route path="/games/:gameId" element={<GameRoomsPage />} />
            <Route path="/games/:gameId/rooms/:roomId" element={<GameRoomPage />} />
            <Route path="/games/:gameId/matchmaking" element={<MatchmakingPage />} />
            <Route path="/games/:gameId/analysis" element={<MatchAnalysisPage />} />
            <Route path="/games/:gameId/results/:matchId" element={<MatchResultsPage />} />
            <Route path="/games/reaction/rooms/:roomId/play" element={<ReactionGame />} />
            <Route path="/games/flaprocket/rooms/:roomId/play" element={<FlapRocket />} />
            <Route path="/games/colormatch/rooms/:roomId/play" element={<ColorMatch />} />
            <Route path="/games/wordscramble/rooms/:roomId/play" element={<WordScramble />} />
            <Route path="/games/stickmanarcher/rooms/:roomId/play" element={<StickmanArcher />} />
            <Route path="/api-test" element={<ApiTestPage />} />
          </Route>
        </Routes>
      </BrowserRouter>
    </UserProvider>
  );
}

export default App;
import { Router } from 'express';
import {
  getAffiliateStats,
  getReferrals,
  getEarnings,
  getAffiliateLink,
  withdrawCommissions,
  getCommissionRate
} from '../controllers/affiliateController';
import { validate, schemas } from '../middleware/validation';
import { authenticate } from '../middleware/auth';

const router = Router();

/**
 * @route   GET /api/affiliate/stats
 * @desc    Get affiliate statistics
 * @access  Private
 */
router.get('/stats',
  authenticate,
  getAffiliateStats
);

/**
 * @route   GET /api/affiliate/referrals
 * @desc    Get referrals list
 * @access  Private
 */
router.get('/referrals',
  authenticate,
  validate(schemas.pagination, 'query'),
  getReferrals
);

/**
 * @route   GET /api/affiliate/earnings
 * @desc    Get earnings history
 * @access  Private
 */
router.get('/earnings',
  authenticate,
  validate(schemas.pagination, 'query'),
  getEarnings
);

/**
 * @route   GET /api/affiliate/link
 * @desc    Get affiliate link
 * @access  Private
 */
router.get('/link',
  authenticate,
  getAffiliateLink
);

/**
 * @route   GET /api/affiliate/commission-rate
 * @desc    Get current commission rate
 * @access  Private
 */
router.get('/commission-rate',
  authenticate,
  getCommissionRate
);

/**
 * @route   POST /api/affiliate/withdraw
 * @desc    Withdraw affiliate commissions
 * @access  Private
 */
router.post('/withdraw',
  authenticate,
  validate(schemas.withdrawal),
  withdrawCommissions
);

export default router;

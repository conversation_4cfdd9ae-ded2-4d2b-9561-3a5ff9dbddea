-- Migration 011: Create Wallet Function
-- Created: 2024-01-01
-- Description: Creates the missing create_user_wallet function

-- Function to create a wallet for a specific user
CREATE OR REPLACE FUNCTION create_user_wallet(user_id_param UUID)
RETURNS UUID AS $$
DECLARE
    wallet_id UUID;
BEGIN
    -- Check if wallet already exists
    SELECT id INTO wallet_id
    FROM wallets
    WHERE user_id = user_id_param;
    
    -- If wallet doesn't exist, create it
    IF wallet_id IS NULL THEN
        INSERT INTO wallets (user_id, balance, frozen_balance, total_deposited, total_withdrawn)
        VALUES (user_id_param, 0.00, 0.00, 0.00, 0.00)
        RETURNING id INTO wallet_id;
        
        RAISE NOTICE 'Wallet created for user %', user_id_param;
    ELSE
        RAISE NOTICE 'Wallet already exists for user %', user_id_param;
    END IF;
    
    RETURN wallet_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_user_wallet(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION create_user_wallet(UUID) TO service_role;

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugRoomIssues() {
  try {
    console.log('🔍 Investigando problemas da sala Teste2...');
    
    // Buscar a sala Teste2
    const { data: rooms, error: roomsError } = await supabase
      .from('game_rooms')
      .select('*')
      .eq('name', 'Teste2');
    
    if (roomsError || !rooms || rooms.length === 0) {
      console.log('❌ Sala "Teste2" não encontrada');
      return;
    }
    
    const room = rooms[0];
    console.log('📋 Dados da sala Teste2:');
    console.log(`  ID: ${room.id}`);
    console.log(`  Status: ${room.status}`);
    console.log(`  Iniciada em: ${room.started_at || 'Não iniciada'}`);
    console.log(`  Finalizada em: ${room.finished_at || 'Não finalizada'}`);
    
    // Buscar participantes com detalhes
    const { data: participants, error: participantsError } = await supabase
      .from('room_participants')
      .select(`
        user_id,
        is_ready,
        joined_at,
        left_at,
        users (id, username, display_name, email)
      `)
      .eq('room_id', room.id);
    
    if (participantsError) {
      console.error('❌ Erro ao buscar participantes:', participantsError);
      return;
    }
    
    console.log('\n👥 Participantes na sala:');
    if (participants && participants.length > 0) {
      participants.forEach((p, index) => {
        console.log(`  ${index + 1}. ${p.users?.username || 'Unknown'} (${p.user_id})`);
        console.log(`     Display Name: ${p.users?.display_name || 'N/A'}`);
        console.log(`     Email: ${p.users?.email || 'N/A'}`);
        console.log(`     Pronto: ${p.is_ready ? 'Sim' : 'Não'}`);
        console.log(`     Entrou em: ${p.joined_at}`);
        console.log(`     Saiu em: ${p.left_at || 'Ainda na sala'}`);
        console.log('');
      });
    } else {
      console.log('  Nenhum participante encontrado');
    }
    
    // Buscar mensagens de chat
    const { data: messages, error: messagesError } = await supabase
      .from('room_chat_messages')
      .select(`
        id,
        message,
        message_type,
        created_at,
        user_id,
        users (username, display_name)
      `)
      .eq('room_id', room.id)
      .order('created_at', { ascending: true });
    
    if (messagesError) {
      console.error('❌ Erro ao buscar mensagens:', messagesError);
    } else {
      console.log('\n💬 Mensagens do chat:');
      if (messages && messages.length > 0) {
        messages.forEach((msg, index) => {
          const sender = msg.users?.username || 'Unknown';
          const displayName = msg.users?.display_name || sender;
          console.log(`  ${index + 1}. [${new Date(msg.created_at).toLocaleTimeString()}] ${displayName}: ${msg.message}`);
          console.log(`     User ID: ${msg.user_id}`);
          console.log(`     Username: ${sender}`);
          console.log('');
        });
      } else {
        console.log('  Nenhuma mensagem encontrada');
      }
    }
    
    // Verificar se há matches associados à sala
    const { data: matches, error: matchesError } = await supabase
      .from('matches')
      .select('*')
      .eq('room_id', room.id);
    
    if (matchesError) {
      console.error('❌ Erro ao buscar matches:', matchesError);
    } else {
      console.log('\n🎮 Matches associados:');
      if (matches && matches.length > 0) {
        matches.forEach((match, index) => {
          console.log(`  ${index + 1}. Match ID: ${match.id}`);
          console.log(`     Status: ${match.status}`);
          console.log(`     Iniciado em: ${match.started_at || 'Não iniciado'}`);
          console.log(`     Finalizado em: ${match.finished_at || 'Não finalizado'}`);
          console.log(`     Vencedor: ${match.winner_user_id || 'Não definido'}`);
          console.log('');
        });
      } else {
        console.log('  Nenhum match encontrado');
      }
    }
    
    // Verificar usuários matt2 e matt3
    console.log('\n🔍 Verificando usuários matt2 e matt3...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, display_name, email')
      .in('username', ['matt2', 'matt3']);
    
    if (usersError) {
      console.error('❌ Erro ao buscar usuários:', usersError);
    } else if (users) {
      users.forEach(user => {
        console.log(`  ${user.username}:`);
        console.log(`    ID: ${user.id}`);
        console.log(`    Display Name: ${user.display_name}`);
        console.log(`    Email: ${user.email}`);
        
        const isInRoom = participants?.some(p => p.user_id === user.id);
        console.log(`    Na sala: ${isInRoom ? 'SIM' : 'NÃO'}`);
        
        if (isInRoom) {
          const participant = participants.find(p => p.user_id === user.id);
          console.log(`    Status pronto: ${participant?.is_ready ? 'SIM' : 'NÃO'}`);
        }
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

debugRoomIssues();

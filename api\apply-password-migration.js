const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyPasswordMigration() {
  try {
    console.log('🔧 Applying password column migration...');
    
    // Add password_hash column
    const { error: columnError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);'
    });
    
    if (columnError) {
      console.log('⚠️ Column might already exist or using direct SQL...');
      console.log('Error:', columnError);
    } else {
      console.log('✅ Password column added successfully');
    }
    
    // Add indexes
    const { error: indexError1 } = await supabase.rpc('exec_sql', {
      sql: 'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);'
    });
    
    const { error: indexError2 } = await supabase.rpc('exec_sql', {
      sql: 'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);'
    });
    
    if (indexError1 || indexError2) {
      console.log('⚠️ Indexes might already exist');
    } else {
      console.log('✅ Indexes created successfully');
    }
    
    // Check if user exists and has password
    console.log('\n🔍 <NAME_EMAIL>...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, username, password_hash')
      .eq('email', '<EMAIL>')
      .single();
    
    if (userError) {
      console.log('❌ User not found:', userError.message);
      console.log('\n📝 You need to register the user first!');
    } else {
      console.log('✅ User found:', user.username, `(${user.id})`);
      console.log('Password hash exists:', !!user.password_hash);
      
      if (!user.password_hash) {
        console.log('\n⚠️ User exists but has no password hash!');
        console.log('This user was probably created before the password system was implemented.');
        console.log('You need to register again or manually set a password.');
      }
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

applyPasswordMigration();

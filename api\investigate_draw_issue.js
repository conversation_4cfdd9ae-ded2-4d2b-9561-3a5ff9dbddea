const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function investigateDrawIssue() {
  console.log('🔍 Investigando problema de empate com vencedor identificado...\n');

  try {
    // Buscar partidas recentes completadas
    console.log('1. Buscando partidas recentes completadas...');
    const { data: recentMatches, error: matchError } = await supabase
      .from('matches')
      .select(`
        id,
        status,
        winner_user_id,
        entry_fee,
        total_prize,
        created_at,
        finished_at,
        match_participants!inner(
          user_id,
          placement,
          earnings,
          score,
          stats,
          users!inner(username)
        )
      `)
      .eq('status', 'completed')
      .order('finished_at', { ascending: false })
      .limit(5);

    if (matchError) {
      console.error('❌ Erro ao buscar partidas:', matchError);
      return;
    }

    console.log(`📊 Encontradas ${recentMatches.length} partidas completadas recentes:\n`);

    recentMatches.forEach((match, index) => {
      console.log(`${index + 1}. Match ID: ${match.id}`);
      console.log(`   Status: ${match.status}`);
      console.log(`   Vencedor ID: ${match.winner_user_id || 'NULL'}`);
      console.log(`   Taxa entrada: R$ ${match.entry_fee || 0}`);
      console.log(`   Prêmio total: R$ ${match.total_prize || 0}`);
      console.log(`   Finalizada: ${match.finished_at}`);
      console.log(`   Participantes:`);
      
      match.match_participants.forEach(p => {
        console.log(`     - ${p.users.username} (${p.user_id})`);
        console.log(`       Colocação: ${p.placement || 'NULL'}`);
        console.log(`       Ganhos: R$ ${p.earnings || 0}`);
        console.log(`       Score: ${p.score || 0}`);
        console.log(`       Stats: ${JSON.stringify(p.stats || {})}`);
      });
      
      // Analisar problemas potenciais
      const hasWinner = match.winner_user_id !== null;
      const hasPlacement1 = match.match_participants.some(p => p.placement === 1);
      const hasEarnings = match.match_participants.some(p => p.earnings > 0);
      
      console.log(`   🔍 Análise:`);
      console.log(`     - Tem vencedor definido: ${hasWinner ? 'SIM' : 'NÃO'}`);
      console.log(`     - Tem participante com colocação 1: ${hasPlacement1 ? 'SIM' : 'NÃO'}`);
      console.log(`     - Tem participante com ganhos: ${hasEarnings ? 'SIM' : 'NÃO'}`);
      
      if (hasWinner && !hasEarnings) {
        console.log(`   ⚠️ PROBLEMA: Vencedor identificado mas sem ganhos distribuídos!`);
      }
      
      if (!hasWinner && hasPlacement1) {
        console.log(`   ⚠️ PROBLEMA: Participante com colocação 1 mas sem vencedor definido!`);
      }
      
      console.log('');
    });

    // Buscar especificamente partidas com matt2 como vencedor
    console.log('2. Buscando partidas específicas com matt2...');
    const { data: matt2User, error: userError } = await supabase
      .from('users')
      .select('id, username')
      .eq('username', 'matt2')
      .single();

    if (userError || !matt2User) {
      console.log('⚠️ Usuário matt2 não encontrado');
    } else {
      console.log(`👤 Matt2 ID: ${matt2User.id}`);
      
      const { data: matt2Matches, error: matt2Error } = await supabase
        .from('matches')
        .select(`
          id,
          status,
          winner_user_id,
          entry_fee,
          total_prize,
          created_at,
          finished_at,
          match_participants!inner(
            user_id,
            placement,
            earnings,
            score,
            stats,
            users!inner(username)
          )
        `)
        .eq('winner_user_id', matt2User.id)
        .order('finished_at', { ascending: false })
        .limit(3);

      if (matt2Error) {
        console.error('❌ Erro ao buscar partidas do matt2:', matt2Error);
      } else {
        console.log(`🎯 Partidas onde matt2 é o vencedor: ${matt2Matches.length}\n`);
        
        matt2Matches.forEach((match, index) => {
          console.log(`${index + 1}. Match ID: ${match.id}`);
          console.log(`   Finalizada: ${match.finished_at}`);
          console.log(`   Taxa entrada: R$ ${match.entry_fee || 0}`);
          console.log(`   Participantes:`);
          
          match.match_participants.forEach(p => {
            const isMatt2 = p.user_id === matt2User.id;
            console.log(`     ${isMatt2 ? '🏆' : '  '} ${p.users.username} (${p.user_id})`);
            console.log(`       Colocação: ${p.placement || 'NULL'}`);
            console.log(`       Ganhos: R$ ${p.earnings || 0}`);
            
            if (isMatt2 && p.earnings === 0) {
              console.log(`       ❌ PROBLEMA: Matt2 é vencedor mas não recebeu ganhos!`);
            }
          });
          console.log('');
        });
      }
    }

    // Verificar transações de prêmio para matt2
    if (matt2User) {
      console.log('3. Verificando transações de prêmio para matt2...');
      const { data: matt2Transactions, error: txError } = await supabase
        .from('transactions')
        .select(`
          id,
          type,
          amount,
          status,
          description,
          created_at
        `)
        .eq('user_id', matt2User.id)
        .eq('type', 'win')
        .order('created_at', { ascending: false })
        .limit(5);

      if (txError) {
        console.error('❌ Erro ao buscar transações:', txError);
      } else {
        console.log(`💰 Transações de prêmio do matt2: ${matt2Transactions.length}`);
        matt2Transactions.forEach(tx => {
          console.log(`  - R$ ${tx.amount} (${tx.status}) - ${tx.description}`);
          console.log(`    Criada: ${tx.created_at}`);
        });
      }
    }

    // Verificar lógica de determinação de empate
    console.log('\n4. Analisando lógica de determinação de resultado...');
    console.log('📋 Critérios atuais para determinar resultado:');
    console.log('  - Vitória: winner_user_id === user.id OU placement === 1');
    console.log('  - Empate: Não há lógica específica implementada');
    console.log('  - Derrota: Todos os outros casos');
    
    console.log('\n💡 Possíveis causas do problema:');
    console.log('  1. winner_user_id está sendo definido mas placement não está sendo definido como 1');
    console.log('  2. Sistema de distribuição de prêmios não está sendo chamado');
    console.log('  3. Conflito entre diferentes lógicas de determinação de vencedor');
    console.log('  4. Interface mostrando "empate" baseado em lógica diferente do backend');

  } catch (error) {
    console.error('❌ Erro durante investigação:', error);
  }
}

// Executar investigação
investigateDrawIssue()
  .then(() => {
    console.log('\n✅ Investigação concluída!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

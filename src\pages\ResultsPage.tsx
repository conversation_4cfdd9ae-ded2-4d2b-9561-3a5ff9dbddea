import React, { useState } from 'react';
import { Trophy, Users, Play, Calendar, Filter, Search, ChevronRight, X, Medal, Clock, Eye } from 'lucide-react';

interface Tournament {
  id: string;
  title: string;
  game: string;
  date: string;
  prizePool: number;
  participants: number;
  thumbnail: string;
  recordingUrl: string;
  winner: {
    name: string;
    avatar: string;
    prize: number;
  };
  topPlayers: Array<{
    position: number;
    name: string;
    avatar: string;
    prize: number;
  }>;
}

export default function ResultsPage() {
  const [selectedGame, setSelectedGame] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const tournaments: Tournament[] = [
    {
      id: '1',
      title: 'ESL Challenger League - Season 49',
      game: 'Counter-Strike 2',
      date: '2024-03-10',
      prizePool: 50000,
      participants: 32,
      thumbnail: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      recordingUrl: 'https://www.twitch.tv/videos/123456789',
      winner: {
        name: 'Furia Academy',
        avatar: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        prize: 25000
      },
      topPlayers: [
        {
          position: 1,
          name: 'Furia Academy',
          avatar: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          prize: 25000
        },
        {
          position: 2,
          name: 'Imperial E-sports',
          avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          prize: 15000
        },
        {
          position: 3,
          name: 'MIBR Academy',
          avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          prize: 10000
        }
      ]
    },
    {
      id: '2',
      title: 'Apex Pro League - Split 2',
      game: 'Apex Legends',
      date: '2024-03-05',
      prizePool: 30000,
      participants: 20,
      thumbnail: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      recordingUrl: 'https://www.twitch.tv/videos/987654321',
      winner: {
        name: 'Team Liquid',
        avatar: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        prize: 15000
      },
      topPlayers: [
        {
          position: 1,
          name: 'Team Liquid',
          avatar: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          prize: 15000
        },
        {
          position: 2,
          name: 'NRG',
          avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          prize: 10000
        },
        {
          position: 3,
          name: 'TSM',
          avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          prize: 5000
        }
      ]
    }
  ];

  const games = [
    { id: 'cs2', name: 'Counter-Strike 2' },
    { id: 'apex', name: 'Apex Legends' },
    { id: 'valorant', name: 'VALORANT' }
  ];

  const filteredTournaments = tournaments.filter(tournament => {
    if (selectedGame && tournament.game !== games.find(g => g.id === selectedGame)?.name) {
      return false;
    }
    if (searchQuery && !tournament.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-xl p-6 mb-8">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mt-10 -mr-10 blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -mb-8 -ml-8 blur-xl"></div>

        <div className="relative z-10">
          <div className="flex items-center gap-2 mb-2">
            <Trophy className="w-6 h-6 text-white" />
            <h1 className="text-2xl font-bold text-white">Resultados</h1>
          </div>
          <p className="text-white/80 max-w-2xl">Assista aos torneios encerrados e veja os vencedores das competições mais recentes</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Buscar torneios..."
            className="w-full bg-gray-800/50 text-white rounded-xl py-3 pl-10 pr-10 focus:outline-none focus:ring-2 focus:ring-indigo-400 backdrop-blur-sm border border-white/10"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        <div className="flex flex-wrap gap-2">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 p-2 rounded-lg">
            <Filter className="w-5 h-5 text-indigo-400" />
          </div>
          {games.map(game => (
            <button
              key={game.id}
              onClick={() => setSelectedGame(selectedGame === game.id ? null : game.id)}
              className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                selectedGame === game.id
                  ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                  : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
              }`}
            >
              {game.name}
            </button>
          ))}
          {selectedGame && (
            <button
              onClick={() => setSelectedGame(null)}
              className="px-4 py-2 rounded-lg bg-gray-800/50 backdrop-blur-sm border border-white/10 text-gray-400 hover:text-white hover:bg-gray-700/50 transition-all duration-200 flex items-center gap-1"
            >
              <X className="w-4 h-4" />
              Limpar filtros
            </button>
          )}
        </div>
      </div>

      {/* Tournaments Grid */}
      <div className="grid gap-8">
        {filteredTournaments.length > 0 ? (
          filteredTournaments.map(tournament => (
          <div
            key={tournament.id}
            className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-[1.01]"
          >
            {/* Tournament Header */}
            <div className="relative h-48">
              <img
                src={tournament.thumbnail}
                alt={tournament.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent">
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-black/40 backdrop-blur-sm px-3 py-1 rounded-lg inline-block mb-2">
                    <span className="text-white text-sm">{tournament.game}</span>
                  </div>
                  <h2 className="text-xl font-bold text-white mb-2">{tournament.title}</h2>
                  <div className="flex flex-wrap items-center gap-4 text-gray-300">
                    <span className="flex items-center gap-1">
                      <Trophy className="w-4 h-4 text-indigo-400" />
                      R$ {tournament.prizePool.toLocaleString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      {tournament.participants} times
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(tournament.date).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Tournament Content */}
            <div className="p-6 space-y-6 relative">
              {/* Tournament Stats */}
              <div className="grid grid-cols-3 gap-4 mb-2">
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <Trophy className="w-4 h-4 text-indigo-400" />
                    <span className="text-gray-300 text-sm">Premiação</span>
                  </div>
                  <p className="text-white font-bold">R$ {tournament.prizePool.toLocaleString()}</p>
                </div>
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <Users className="w-4 h-4 text-purple-400" />
                    <span className="text-gray-300 text-sm">Participantes</span>
                  </div>
                  <p className="text-white font-bold">{tournament.participants} times</p>
                </div>
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <Calendar className="w-4 h-4 text-pink-400" />
                    <span className="text-gray-300 text-sm">Data</span>
                  </div>
                  <p className="text-white font-bold">{new Date(tournament.date).toLocaleDateString()}</p>
                </div>
              </div>
              {/* Winner Section */}
              <div>
                <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
                  <Trophy className="w-5 h-5 text-indigo-400" />
                  Time Vencedor
                </h3>
                <div className="bg-gradient-to-r from-indigo-500/20 to-purple-500/20 backdrop-blur-sm border border-indigo-500/20 rounded-xl p-4 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <img
                      src={tournament.winner.avatar}
                      alt={tournament.winner.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h4 className="text-white font-semibold">{tournament.winner.name}</h4>
                      <p className="text-green-400">
                        Prêmio: R$ {tournament.winner.prize.toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <a
                    href={tournament.recordingUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:opacity-90 shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <Play className="w-4 h-4" fill="white" />
                    Assistir Replay
                  </a>
                </div>
              </div>

              {/* Top Players */}
              <div>
                <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
                  <Medal className="w-5 h-5 text-indigo-400" />
                  Ranking Final
                </h3>
                <div className="space-y-2">
                  {tournament.topPlayers.map((player) => (
                    <div
                      key={player.position}
                      className={`rounded-lg p-3 flex items-center justify-between ${player.position === 1 ? 'bg-gradient-to-r from-yellow-500/20 to-amber-500/20 border border-yellow-500/20' : 'bg-gray-700/70'}`}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
                          player.position === 1 ? 'bg-yellow-400 text-black' :
                          player.position === 2 ? 'bg-gray-300 text-black' :
                          player.position === 3 ? 'bg-amber-700 text-white' :
                          'bg-gray-700 text-gray-300'
                        }`}>
                          {player.position}
                        </div>
                        <img
                          src={player.avatar}
                          alt={player.name}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                        <span className="text-white">{player.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Eye className="w-4 h-4 text-gray-400" />
                        <span className="text-green-400 font-medium">
                          R$ {player.prize.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))
        ) : (
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
            <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <h3 className="text-white font-bold text-lg mb-2">Nenhum torneio encontrado</h3>
            <p className="text-gray-400 mb-4">
              {searchQuery && selectedGame ?
                `Não encontramos torneios de ${games.find(g => g.id === selectedGame)?.name} com o termo "${searchQuery}".` :
               searchQuery ?
                `Não encontramos torneios com o termo "${searchQuery}".` :
               selectedGame ?
                `Não encontramos torneios de ${games.find(g => g.id === selectedGame)?.name}.` :
                'Não há torneios disponíveis no momento.'}
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedGame(null);
              }}
              className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg inline-flex items-center gap-2 hover:opacity-90 shadow-lg transition-all duration-300 hover:scale-105"
            >
              <X className="w-4 h-4" />
              Limpar filtros
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
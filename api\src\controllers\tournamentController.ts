import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, TournamentCreationData } from '../types/api';
import { logger, logBusinessEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';

/**
 * Get tournaments with filters
 */
export const getTournaments = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { page, limit } = extractPagination(req.query);
    const { status, game_id, format, is_public = 'true' } = req.query;

    let query = supabaseAdmin
      .from('tournaments')
      .select(`
        id, title, description, format, is_public, entry_fee, max_participants,
        current_participants, prize_distribution, registration_start_date,
        registration_end_date, start_date, end_date, status, created_at,
        games (id, name, slug, icon_url),
        users (username, display_name, avatar_url)
      `, { count: 'exact' });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (game_id) {
      query = query.eq('game_id', game_id);
    }
    if (format) {
      query = query.eq('format', format);
    }
    if (is_public === 'true') {
      query = query.eq('is_public', true);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: tournaments, error, count } = await query;

    if (error) {
      logger.error('Failed to get tournaments:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve tournaments'
      };
      res.status(500).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        tournaments: tournaments || []
      },
      pagination: createPagination(page, limit, count || 0)
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get tournaments error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Create tournament
 */
export const createTournament = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const tournamentData: TournamentCreationData = req.body;

    // Verify game exists
    const { data: game, error: gameError } = await supabaseAdmin
      .from('games')
      .select('id, name, is_active')
      .eq('id', tournamentData.game_id)
      .eq('is_active', true)
      .single();

    if (gameError || !game) {
      const response: ApiResponse = {
        success: false,
        error: 'Game not found or inactive'
      };
      res.status(404).json(response);
      return;
    }

    // Validate prize distribution (should sum to 100%)
    const totalDistribution = Object.values(tournamentData.prize_distribution).reduce((sum, value) => sum + value, 0);
    if (Math.abs(totalDistribution - 100) > 0.01) {
      const response: ApiResponse = {
        success: false,
        error: 'Prize distribution must sum to 100%'
      };
      res.status(400).json(response);
      return;
    }

    // Create tournament
    const { data: tournament, error: tournamentError } = await supabaseAdmin
      .from('tournaments')
      .insert({
        game_id: tournamentData.game_id,
        organizer_user_id: user.id,
        title: tournamentData.title,
        description: tournamentData.description,
        rules: tournamentData.rules,
        format: tournamentData.format,
        is_public: tournamentData.is_public,
        entry_fee: tournamentData.entry_fee,
        max_participants: tournamentData.max_participants,
        prize_distribution: tournamentData.prize_distribution,
        registration_start_date: tournamentData.registration_start_date || new Date().toISOString(),
        registration_end_date: tournamentData.registration_end_date,
        start_date: tournamentData.start_date,
        end_date: tournamentData.end_date,
        tournament_settings: tournamentData.tournament_settings || {}
      })
      .select('*')
      .single();

    if (tournamentError || !tournament) {
      logger.error('Failed to create tournament:', tournamentError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create tournament'
      };
      res.status(500).json(response);
      return;
    }

    logBusinessEvent('tournament_created', user.id, {
      tournament_id: tournament.id,
      game_id: tournamentData.game_id,
      entry_fee: tournamentData.entry_fee,
      max_participants: tournamentData.max_participants
    });

    const response: ApiResponse = {
      success: true,
      message: 'Tournament created successfully',
      data: {
        tournament: {
          id: tournament.id,
          title: tournament.title,
          format: tournament.format,
          status: tournament.status,
          entry_fee: tournament.entry_fee,
          max_participants: tournament.max_participants,
          registration_end_date: tournament.registration_end_date,
          start_date: tournament.start_date,
          created_at: tournament.created_at
        }
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Create tournament error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get tournament by ID
 */
export const getTournamentById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const { data: tournament, error } = await supabaseAdmin
      .from('tournaments')
      .select(`
        *,
        games (id, name, slug, icon_url, type),
        users (username, display_name, avatar_url),
        tournament_participants (
          user_id, registration_date, is_checked_in,
          users (username, display_name, avatar_url)
        ),
        tournament_brackets (
          id, round_number, match_number, participant1_id, participant2_id,
          winner_id, status
        )
      `)
      .eq('id', id)
      .single();

    if (error || !tournament) {
      const response: ApiResponse = {
        success: false,
        error: 'Tournament not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if user can view this tournament
    if (!tournament.is_public) {
      const currentUser = req.user;
      const isParticipant = currentUser && tournament.tournament_participants?.some(
        (p: any) => p.user_id === currentUser.id
      );
      const isOrganizer = currentUser && tournament.organizer_user_id === currentUser.id;

      if (!isParticipant && !isOrganizer) {
        const response: ApiResponse = {
          success: false,
          error: 'Access denied to this tournament'
        };
        res.status(403).json(response);
        return;
      }
    }

    const response: ApiResponse = {
      success: true,
      data: {
        tournament
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get tournament by ID error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Register for tournament
 */
export const registerForTournament = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;

    // Get tournament details
    const { data: tournament, error: tournamentError } = await supabaseAdmin
      .from('tournaments')
      .select(`
        *,
        tournament_participants (user_id)
      `)
      .eq('id', id)
      .single();

    if (tournamentError || !tournament) {
      const response: ApiResponse = {
        success: false,
        error: 'Tournament not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check tournament status
    if (tournament.status !== 'registration_open') {
      const response: ApiResponse = {
        success: false,
        error: 'Tournament registration is not open'
      };
      res.status(400).json(response);
      return;
    }

    // Check registration deadline
    const now = new Date();
    const registrationEnd = new Date(tournament.registration_end_date);
    if (now > registrationEnd) {
      const response: ApiResponse = {
        success: false,
        error: 'Tournament registration has ended'
      };
      res.status(400).json(response);
      return;
    }

    // Check if user is already registered
    const isAlreadyRegistered = tournament.tournament_participants?.some(
      (p: any) => p.user_id === user.id
    );

    if (isAlreadyRegistered) {
      const response: ApiResponse = {
        success: false,
        error: 'Already registered for this tournament'
      };
      res.status(400).json(response);
      return;
    }

    // Check if tournament is full
    const currentParticipants = tournament.tournament_participants?.length || 0;
    if (currentParticipants >= tournament.max_participants) {
      const response: ApiResponse = {
        success: false,
        error: 'Tournament is full'
      };
      res.status(400).json(response);
      return;
    }

    // Check user balance for entry fee
    if (tournament.entry_fee > 0) {
      const { data: wallet } = await supabaseAdmin
        .from('wallets')
        .select('balance, frozen_balance')
        .eq('user_id', user.id)
        .single();

      if (!wallet || (wallet.balance - wallet.frozen_balance) < tournament.entry_fee) {
        const response: ApiResponse = {
          success: false,
          error: 'Insufficient balance'
        };
        res.status(400).json(response);
        return;
      }
    }

    // Register user for tournament
    const { error: registrationError } = await supabaseAdmin
      .from('tournament_participants')
      .insert({
        tournament_id: id,
        user_id: user.id,
        registration_date: new Date().toISOString()
      });

    if (registrationError) {
      logger.error('Failed to register for tournament:', registrationError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to register for tournament'
      };
      res.status(500).json(response);
      return;
    }

    // Freeze entry fee
    if (tournament.entry_fee > 0) {
      await supabaseAdmin
        .from('wallets')
        .update({
          frozen_balance: tournament.entry_fee // Will be handled by SQL function
        })
        .eq('user_id', user.id);
    }

    // Update tournament participant count
    await supabaseAdmin
      .from('tournaments')
      .update({
        current_participants: currentParticipants + 1
      })
      .eq('id', id);

    logBusinessEvent('tournament_registered', user.id, {
      tournament_id: id,
      entry_fee: tournament.entry_fee
    });

    const response: ApiResponse = {
      success: true,
      message: 'Successfully registered for tournament'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Register for tournament error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Unregister from tournament
 */
export const unregisterFromTournament = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;

    // Get tournament details
    const { data: tournament, error: tournamentError } = await supabaseAdmin
      .from('tournaments')
      .select('*')
      .eq('id', id)
      .single();

    if (tournamentError || !tournament) {
      const response: ApiResponse = {
        success: false,
        error: 'Tournament not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if tournament allows unregistration
    if (tournament.status !== 'registration_open') {
      const response: ApiResponse = {
        success: false,
        error: 'Cannot unregister from tournament that has already started'
      };
      res.status(400).json(response);
      return;
    }

    // Remove registration
    const { error: unregisterError } = await supabaseAdmin
      .from('tournament_participants')
      .delete()
      .eq('tournament_id', id)
      .eq('user_id', user.id);

    if (unregisterError) {
      logger.error('Failed to unregister from tournament:', unregisterError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to unregister from tournament'
      };
      res.status(500).json(response);
      return;
    }

    // Unfreeze entry fee
    if (tournament.entry_fee > 0) {
      await supabaseAdmin
        .from('wallets')
        .update({
          frozen_balance: 0 // Will be handled by SQL function
        })
        .eq('user_id', user.id);
    }

    // Update tournament participant count
    await supabaseAdmin
      .from('tournaments')
      .update({
        current_participants: 0 // Will be handled by SQL function
      })
      .eq('id', id);

    logBusinessEvent('tournament_unregistered', user.id, {
      tournament_id: id,
      entry_fee: tournament.entry_fee
    });

    const response: ApiResponse = {
      success: true,
      message: 'Successfully unregistered from tournament'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Unregister from tournament error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get tournament brackets
 */
export const getTournamentBrackets = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Verify tournament exists
    const { data: tournament, error: tournamentError } = await supabaseAdmin
      .from('tournaments')
      .select('id, title, format, status')
      .eq('id', id)
      .single();

    if (tournamentError || !tournament) {
      const response: ApiResponse = {
        success: false,
        error: 'Tournament not found'
      };
      res.status(404).json(response);
      return;
    }

    // Get brackets
    const { data: brackets, error: bracketsError } = await supabaseAdmin
      .from('tournament_brackets')
      .select(`
        id, round_number, match_number, participant1_id, participant2_id,
        winner_id, status, scheduled_time, completed_at,
        participant1:users!tournament_brackets_participant1_id_fkey (
          username, display_name, avatar_url
        ),
        participant2:users!tournament_brackets_participant2_id_fkey (
          username, display_name, avatar_url
        ),
        winner:users!tournament_brackets_winner_id_fkey (
          username, display_name, avatar_url
        )
      `)
      .eq('tournament_id', id)
      .order('round_number')
      .order('match_number');

    if (bracketsError) {
      logger.error('Failed to get tournament brackets:', bracketsError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve tournament brackets'
      };
      res.status(500).json(response);
      return;
    }

    // Group brackets by round
    const bracketsByRound = (brackets || []).reduce((acc: any, bracket: any) => {
      const round = bracket.round_number;
      if (!acc[round]) {
        acc[round] = [];
      }
      acc[round].push(bracket);
      return acc;
    }, {});

    const response: ApiResponse = {
      success: true,
      data: {
        tournament: {
          id: tournament.id,
          title: tournament.title,
          format: tournament.format,
          status: tournament.status
        },
        brackets: bracketsByRound
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get tournament brackets error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get tournament participants
 */
export const getTournamentParticipants = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { page, limit } = extractPagination(req.query);

    // Verify tournament exists
    const { data: tournament, error: tournamentError } = await supabaseAdmin
      .from('tournaments')
      .select('id, title, is_public')
      .eq('id', id)
      .single();

    if (tournamentError || !tournament) {
      const response: ApiResponse = {
        success: false,
        error: 'Tournament not found'
      };
      res.status(404).json(response);
      return;
    }

    const offset = (page - 1) * limit;

    const { data: participants, error, count } = await supabaseAdmin
      .from('tournament_participants')
      .select(`
        user_id, registration_date, is_checked_in,
        users (username, display_name, avatar_url, country),
        user_stats (ranking_points, total_matches, win_rate)
      `, { count: 'exact' })
      .eq('tournament_id', id)
      .order('registration_date')
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get tournament participants:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve tournament participants'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        tournament: {
          id: tournament.id,
          title: tournament.title
        },
        participants: participants || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get tournament participants error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get tournament matches
 */
export const getTournamentMatches = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { page, limit } = extractPagination(req.query);
    const { status, round } = req.query;

    let query = supabaseAdmin
      .from('matches')
      .select(`
        id, status, match_type, entry_fee, prize_pool, scheduled_time,
        started_at, finished_at, created_at,
        games (id, name, slug, icon_url),
        match_participants (
          user_id, score, placement, earnings,
          users (username, display_name, avatar_url)
        )
      `, { count: 'exact' })
      .eq('tournament_id', id);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('scheduled_time', { ascending: true })
      .range(offset, offset + limit - 1);

    const { data: matches, error, count } = await query;

    if (error) {
      logger.error('Failed to get tournament matches:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve tournament matches'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        matches: matches || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get tournament matches error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get tournament seasons
 */
export const getTournamentSeasons = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const { data: seasons, error } = await supabaseAdmin
      .from('tournament_seasons')
      .select(`
        id, season_number, title, start_date, end_date, status,
        prize_pool, participant_count, created_at
      `)
      .eq('tournament_id', id)
      .order('season_number', { ascending: false });

    if (error) {
      logger.error('Failed to get tournament seasons:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve tournament seasons'
      };
      res.status(500).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        seasons: seasons || []
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get tournament seasons error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get tournament results
 */
export const getTournamentResults = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { page, limit } = extractPagination(req.query);
    const { game_id, format } = req.query;

    let query = supabaseAdmin
      .from('tournaments')
      .select(`
        id, title, format, entry_fee, prize_pool, start_date, end_date,
        current_participants, status, created_at,
        games (id, name, slug, icon_url),
        users (username, display_name, avatar_url),
        tournament_participants!inner (
          user_id, final_placement, prize_won,
          users (username, display_name, avatar_url)
        )
      `, { count: 'exact' })
      .eq('status', 'completed');

    // Apply filters
    if (game_id) {
      query = query.eq('game_id', game_id);
    }
    if (format) {
      query = query.eq('format', format);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('end_date', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: tournaments, error, count } = await query;

    if (error) {
      logger.error('Failed to get tournament results:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve tournament results'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        tournaments: tournaments || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get tournament results error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

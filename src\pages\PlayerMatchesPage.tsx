import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  ArrowLeft,
  Calendar,
  Clock,
  Trophy,
  Users,
  Filter,
  Search,
  X,
  ChevronRight,
  Bell,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { realMatchService, Match } from '../services/realMatchService';

// Interface Match importada do serviço

export default function PlayerMatchesPage() {
  const navigate = useNavigate();
  const { playerId } = useParams();
  const [activeFilter, setActiveFilter] = useState<'upcoming' | 'all' | 'completed'>('upcoming');
  const [searchQuery, setSearchQuery] = useState('');

  // Estados para dados da API
  const [playerMatches, setPlayerMatches] = useState<Match[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPlayerMatches = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando partidas do jogador...');

        // Carrega partidas do usuário atual
        const matches = await realMatchService.getUserMatches();
        setPlayerMatches(matches);

        console.log(`✅ ${matches.length} partidas carregadas`);

      } catch (err) {
        console.error('❌ Erro ao carregar partidas:', err);
        setError('Erro ao carregar partidas. Usando dados offline.');

        // Fallback para dados mock
        const mockMatches: Match[] = [
          {
            id: '1',
            game_id: 'cs2',
            game_name: 'Counter-Strike 2',
            player1: {
              id: 'p1',
              username: 'João Silva',
              display_name: 'João Silva',
              avatar_url: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
            },
            player2: {
              id: 'p2',
              username: 'Ana Silva',
              display_name: 'Ana Silva',
              avatar_url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
            },
            scheduled_time: new Date(Date.now() + 86400000).toISOString(), // Amanhã
            status: 'scheduled',
            bet_amount: 50,
            prize_amount: 100
          }
        ];
        setPlayerMatches(mockMatches);
      } finally {
        setIsLoading(false);
      }
    };

    loadPlayerMatches();
  }, []);

  // Filter matches based on active filter and search query
  const filteredMatches = playerMatches.filter(match => {
    // Filter by status
    if (activeFilter === 'upcoming' && match.status !== 'scheduled') return false;
    if (activeFilter === 'completed' && match.status !== 'completed') return false;

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        (match.tournament_name || match.game_name).toLowerCase().includes(query) ||
        (match.player1.display_name || match.player1.username).toLowerCase().includes(query) ||
        (match.player2.display_name || match.player2.username).toLowerCase().includes(query)
      );
    }

    return true;
  });

  // Sort matches: upcoming first, then by date
  const sortedMatches = [...filteredMatches].sort((a, b) => {
    // First sort by status (scheduled first)
    if (a.status === 'scheduled' && b.status !== 'scheduled') return -1;
    if (a.status !== 'scheduled' && b.status === 'scheduled') return 1;

    // Then sort by date
    return new Date(a.scheduled_time).getTime() - new Date(b.scheduled_time).getTime();
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isToday = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  const isTomorrow = (dateString: string) => {
    const date = new Date(dateString);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return (
      date.getDate() === tomorrow.getDate() &&
      date.getMonth() === tomorrow.getMonth() &&
      date.getFullYear() === tomorrow.getFullYear()
    );
  };

  const getDateLabel = (dateString: string) => {
    if (isToday(dateString)) return 'Hoje';
    if (isTomorrow(dateString)) return 'Amanhã';
    return formatDate(dateString);
  };

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => {
              // Ao voltar, garantir que a aba 'overview' seja selecionada
              const previousPage = document.referrer;
              if (previousPage.includes('/tournaments/')) {
                const tournamentId = previousPage.split('/tournaments/')[1];
                localStorage.setItem(`tournament_${tournamentId}_tab`, 'overview');
              }
              navigate(-1);
            }}
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Voltar</span>
          </button>
          <h1 className="text-2xl font-bold text-white">Suas Partidas</h1>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Buscar partidas..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-gray-800/50 text-white rounded-xl py-3 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-400 backdrop-blur-sm border border-white/10"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        <div className="flex gap-2">
          <button
            onClick={() => setActiveFilter('upcoming')}
            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
              activeFilter === 'upcoming'
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Próximas
          </button>
          <button
            onClick={() => setActiveFilter('completed')}
            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
              activeFilter === 'completed'
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Concluídas
          </button>
          <button
            onClick={() => setActiveFilter('all')}
            className={`px-4 py-2 rounded-lg transition-all duration-200 ${
              activeFilter === 'all'
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Todas
          </button>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-xl p-4 text-center">
          <p className="text-yellow-400 text-sm">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Loader2 className="w-8 h-8 text-indigo-400 mx-auto mb-4 animate-spin" />
          <h3 className="text-white font-bold text-lg mb-2">Carregando partidas...</h3>
          <p className="text-gray-400">Aguarde enquanto buscamos suas partidas.</p>
        </div>
      ) : (
        <>
      {/* Matches List */}
      {sortedMatches.length > 0 ? (
        <div className="space-y-6">
          {sortedMatches.map(match => (
            <div
              key={match.id}
              className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden hover:bg-gray-700/50 transition-all duration-300 cursor-pointer"
              onClick={() => {
                // Garantir que a aba 'overview' seja selecionada ao retornar para a página do torneio
                localStorage.setItem(`tournament_${match.tournamentId}_tab`, 'overview');
                navigate(`/tournaments/${match.tournamentId}`);
              }}
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Trophy className="w-5 h-5 text-indigo-400" />
                    <span className="text-white font-semibold">{match.tournament_name || match.game_name}</span>
                    {match.round && <span className="text-gray-400 text-sm">Round {match.round}</span>}
                  </div>
                  {match.status === 'scheduled' && isToday(match.scheduled_time) && (
                    <div className="bg-yellow-500/20 text-yellow-400 px-3 py-1 rounded-lg flex items-center gap-1 text-sm">
                      <AlertCircle className="w-4 h-4" />
                      Hoje
                    </div>
                  )}
                </div>

                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4">
                  <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                    <div className="flex items-center gap-3 sm:flex-1">
                      <img
                        src={match.player1.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'}
                        alt={match.player1.display_name || match.player1.username}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div className="text-center sm:text-left">
                        <p className={`font-medium ${match.player1.id === 'p1' ? 'text-indigo-400' : 'text-white'}`}>
                          {match.player1.display_name || match.player1.username}
                          {match.player1.id === 'p1' && ' (Você)'}
                        </p>
                        {match.status === 'completed' && match.score && (
                          <p className="text-gray-400 text-sm">{match.score.player1_score} pontos</p>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col items-center">
                      {match.status === 'completed' ? (
                        <div className="bg-gray-600/50 px-4 py-2 rounded-lg text-center">
                          <p className="text-gray-300 text-sm mb-1">Resultado</p>
                          <p className="text-white font-bold text-xl">
                            {match.score?.player1_score || 0} - {match.score?.player2_score || 0}
                          </p>
                        </div>
                      ) : (
                        <>
                          <div className="flex items-center gap-2 mb-1">
                            <Calendar className="w-4 h-4 text-indigo-400" />
                            <span className="text-white">{getDateLabel(match.scheduled_time)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4 text-indigo-400" />
                            <span className="text-white">{formatTime(match.scheduled_time)}</span>
                          </div>
                          <p className="text-white font-bold mt-1">VS</p>
                        </>
                      )}
                    </div>

                    <div className="flex items-center gap-3 sm:flex-1 sm:justify-end">
                      <div className="text-center sm:text-right">
                        <p className={`font-medium ${match.player2.id === 'p1' ? 'text-indigo-400' : 'text-white'}`}>
                          {match.player2.display_name || match.player2.username}
                          {match.player2.id === 'p1' && ' (Você)'}
                        </p>
                        {match.status === 'completed' && match.score && (
                          <p className="text-gray-400 text-sm">{match.score.player2_score} pontos</p>
                        )}
                      </div>
                      <img
                        src={match.player2.avatar_url || 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'}
                        alt={match.player2.display_name || match.player2.username}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    </div>
                  </div>
                </div>

                {match.status === 'scheduled' && (
                  <div className="mt-3 flex justify-end">
                    <button className="bg-indigo-500/20 text-indigo-400 px-3 py-1 rounded-lg flex items-center gap-1 text-sm hover:bg-indigo-500/30">
                      <Bell className="w-4 h-4" />
                      Receber lembrete
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Calendar className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-white font-bold text-lg mb-2">Nenhuma partida encontrada</h3>
          <p className="text-gray-400 mb-4">
            {searchQuery ?
              `Não encontramos partidas com o termo "${searchQuery}".` :
              activeFilter === 'upcoming' ?
                'Você não tem partidas agendadas no momento.' :
              activeFilter === 'completed' ?
                'Você ainda não participou de nenhuma partida.' :
                'Não há partidas para exibir.'}
          </p>
          <button
            onClick={() => {
              setSearchQuery('');
              setActiveFilter('upcoming');
            }}
            className="text-indigo-400 hover:text-indigo-300"
          >
            Limpar filtros
          </button>
        </div>
      )}
        </>
      )}
    </div>
  );
}

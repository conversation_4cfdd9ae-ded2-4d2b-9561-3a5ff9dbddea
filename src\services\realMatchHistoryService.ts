import { apiService } from './apiService';

// Tipos para histórico de partidas
export interface MatchHistory {
  id: string;
  game_name: string;
  game_slug: string;
  match_type: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'disputed';
  entry_fee: number;
  total_prize: number;
  scheduled_time?: string;
  started_at?: string;
  finished_at?: string;
  winner_user_id?: string;
  result_data: any;
  created_at: string;
  
  // Dados do participante
  participant: {
    score: number;
    placement?: number;
    earnings: number;
    stats: any;
  };
  
  // Resultado para o usuário
  result: 'victory' | 'defeat' | 'draw';
  points: number;
}

export interface MatchHistoryFilters {
  game_id?: string;
  match_type?: string;
  status?: string;
  date_from?: string;
  date_to?: string;
}

class RealMatchHistoryService {
  
  /**
   * Busca o histórico de partidas do usuário
   */
  async getMatchHistory(
    page: number = 1, 
    limit: number = 10, 
    filters?: MatchHistoryFilters
  ): Promise<{
    matches: MatchHistory[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    console.log('🎮 Buscando histórico de partidas...');
    
    const params = {
      page,
      limit,
      ...filters
    };
    
    const response = await apiService.get<{
      matches: MatchHistory[];
      pagination: any;
    }>('/matches/history', { params });
    
    if (response.success && response.data) {
      console.log(`✅ ${response.data.matches.length} partidas carregadas`);
      return response.data;
    }
    
    throw new Error(response.error || 'Erro ao buscar histórico de partidas');
  }

  /**
   * Busca detalhes de uma partida específica
   */
  async getMatchDetails(matchId: string): Promise<MatchHistory> {
    console.log(`🔍 Buscando detalhes da partida: ${matchId}`);
    
    const response = await apiService.get<MatchHistory>(`/matches/${matchId}`);
    
    if (response.success && response.data) {
      console.log('✅ Detalhes da partida carregados');
      return response.data;
    }
    
    throw new Error(response.error || 'Erro ao buscar detalhes da partida');
  }

  /**
   * Busca estatísticas resumidas do usuário
   */
  async getUserMatchStats(): Promise<{
    total_matches: number;
    total_wins: number;
    total_losses: number;
    total_draws: number;
    win_rate: number;
    total_earnings: number;
    current_streak: number;
    best_streak: number;
    favorite_game?: string;
    recent_performance: Array<{
      date: string;
      result: 'win' | 'loss' | 'draw';
      game: string;
    }>;
  }> {
    console.log('📊 Buscando estatísticas de partidas...');
    
    const response = await apiService.get<any>('/matches/stats');
    
    if (response.success && response.data) {
      console.log('✅ Estatísticas de partidas carregadas');
      return response.data;
    }
    
    throw new Error(response.error || 'Erro ao buscar estatísticas de partidas');
  }
}

export const realMatchHistoryService = new RealMatchHistoryService();

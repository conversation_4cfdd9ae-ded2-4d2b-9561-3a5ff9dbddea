import { apiService } from './apiService';

// Tipos para estatísticas
export interface UserStats {
  user_id: string;
  total_matches: number;
  total_wins: number;
  total_losses: number;
  total_draws: number;
  win_rate: number;
  total_earnings: number;
  current_streak: number;
  best_streak: number;
  ranking_points: number;
  level: number;
  experience_points: number;
  achievements_count: number;
  favorite_game?: string;
  total_playtime?: number;
  last_match_date?: string;
}

export interface GameStats {
  game_id: string;
  game_name: string;
  matches_played: number;
  wins: number;
  losses: number;
  draws: number;
  win_rate: number;
  earnings: number;
  best_streak: number;
  average_match_duration: number;
  last_played: string;
  skill_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon_url?: string;
  category: 'matches' | 'earnings' | 'streaks' | 'social' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  progress: number;
  max_progress: number;
  is_completed: boolean;
  completed_at?: string;
  reward_xp?: number;
  reward_currency?: number;
}

export interface RankingEntry {
  position: number;
  user_id: string;
  username: string;
  display_name: string;
  avatar_url?: string;
  ranking_points: number;
  level: number;
  total_wins: number;
  total_earnings: number;
  win_rate: number;
  favorite_game?: string;
  country?: string;
  is_current_user?: boolean;
}

export interface LeaderboardFilters {
  game_id?: string;
  period?: 'daily' | 'weekly' | 'monthly' | 'all_time';
  region?: string;
  category?: 'points' | 'wins' | 'earnings' | 'win_rate';
}

class RealStatsService {
  
  // ===== ESTATÍSTICAS DO USUÁRIO =====
  
  // Obtém estatísticas gerais do usuário
  async getUserStats(userId?: string): Promise<UserStats | null> {
    try {
      const endpoint = userId ? `/users/${userId}/stats` : '/auth/stats';
      console.log('📊 Buscando estatísticas do usuário...');
      
      const response = await apiService.get<UserStats>(endpoint);
      
      if (response.success && response.data) {
        console.log('✅ Estatísticas do usuário carregadas');
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar estatísticas:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao buscar estatísticas:', error);
      return null;
    }
  }

  // Obtém estatísticas por jogo
  async getGameStats(userId?: string): Promise<GameStats[]> {
    try {
      const endpoint = userId ? `/users/${userId}/game-stats` : '/auth/game-stats';
      console.log('🎮 Buscando estatísticas por jogo...');
      
      const response = await apiService.get<GameStats[]>(endpoint);
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} estatísticas de jogos carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar estatísticas de jogos:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar estatísticas de jogos:', error);
      return [];
    }
  }

  // Obtém conquistas do usuário
  async getUserAchievements(userId?: string): Promise<Achievement[]> {
    try {
      const endpoint = userId ? `/users/${userId}/achievements` : '/auth/achievements';
      console.log('🏆 Buscando conquistas do usuário...');
      
      const response = await apiService.get<Achievement[]>(endpoint);
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} conquistas carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar conquistas:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar conquistas:', error);
      return [];
    }
  }

  // ===== RANKINGS E LEADERBOARDS =====
  
  // Obtém ranking global
  async getGlobalRanking(filters?: LeaderboardFilters, page: number = 1, limit: number = 50): Promise<RankingEntry[]> {
    try {
      console.log(`🏆 Buscando ranking global (página ${page})...`);
      
      const params: any = { page, limit };
      if (filters) Object.assign(params, filters);
      
      const response = await apiService.get<RankingEntry[]>('/rankings/global', { params });
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} entradas do ranking carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar ranking:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar ranking:', error);
      return [];
    }
  }

  // Obtém ranking por jogo
  async getGameRanking(gameId: string, page: number = 1, limit: number = 50): Promise<RankingEntry[]> {
    try {
      console.log(`🎮 Buscando ranking do jogo ${gameId} (página ${page})...`);
      
      const response = await apiService.get<RankingEntry[]>(`/rankings/games/${gameId}`, {
        params: { page, limit }
      });
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} entradas do ranking do jogo carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar ranking do jogo:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar ranking do jogo:', error);
      return [];
    }
  }

  // Obtém posição do usuário no ranking
  async getUserRankingPosition(userId?: string, gameId?: string): Promise<{ position: number; total_players: number } | null> {
    try {
      const endpoint = userId ? `/users/${userId}/ranking-position` : '/auth/ranking-position';
      console.log('📍 Buscando posição no ranking...');
      
      const params: any = {};
      if (gameId) params.game_id = gameId;
      
      const response = await apiService.get<{ position: number; total_players: number }>(endpoint, { params });
      
      if (response.success && response.data) {
        console.log(`✅ Posição no ranking: ${response.data.position}/${response.data.total_players}`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar posição no ranking:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao buscar posição no ranking:', error);
      return null;
    }
  }

  // Obtém ranking de amigos
  async getFriendsRanking(): Promise<RankingEntry[]> {
    try {
      console.log('👥 Buscando ranking de amigos...');
      
      const response = await apiService.get<RankingEntry[]>('/rankings/friends');
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} amigos no ranking carregados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar ranking de amigos:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar ranking de amigos:', error);
      return [];
    }
  }

  // ===== HISTÓRICO E ANÁLISES =====
  
  // Obtém histórico de estatísticas
  async getStatsHistory(period: 'daily' | 'weekly' | 'monthly', days: number = 30): Promise<Array<{
    date: string;
    matches: number;
    wins: number;
    earnings: number;
    ranking_points: number;
  }>> {
    try {
      console.log(`📈 Buscando histórico de estatísticas (${period}, ${days} dias)...`);
      
      const response = await apiService.get<Array<{
        date: string;
        matches: number;
        wins: number;
        earnings: number;
        ranking_points: number;
      }>>('/auth/stats/history', {
        params: { period, days }
      });
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} entradas do histórico carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar histórico:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar histórico:', error);
      return [];
    }
  }

  // Obtém comparação com outros usuários
  async compareWithUser(otherUserId: string): Promise<{
    current_user: UserStats;
    other_user: UserStats;
    comparison: {
      wins_difference: number;
      earnings_difference: number;
      win_rate_difference: number;
      ranking_difference: number;
    };
  } | null> {
    try {
      console.log(`⚖️ Comparando estatísticas com usuário ${otherUserId}...`);
      
      const response = await apiService.get<{
        current_user: UserStats;
        other_user: UserStats;
        comparison: {
          wins_difference: number;
          earnings_difference: number;
          win_rate_difference: number;
          ranking_difference: number;
        };
      }>(`/auth/stats/compare/${otherUserId}`);
      
      if (response.success && response.data) {
        console.log('✅ Comparação de estatísticas carregada');
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar comparação:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao comparar estatísticas:', error);
      return null;
    }
  }

  // Obtém estatísticas globais da plataforma
  async getPlatformStats(): Promise<{
    total_users: number;
    total_matches: number;
    total_prize_pool: number;
    active_users_today: number;
    popular_games: Array<{
      game_id: string;
      game_name: string;
      player_count: number;
      match_count: number;
    }>;
  } | null> {
    try {
      console.log('🌍 Buscando estatísticas da plataforma...');
      
      const response = await apiService.get<{
        total_users: number;
        total_matches: number;
        total_prize_pool: number;
        active_users_today: number;
        popular_games: Array<{
          game_id: string;
          game_name: string;
          player_count: number;
          match_count: number;
        }>;
      }>('/stats/platform');
      
      if (response.success && response.data) {
        console.log('✅ Estatísticas da plataforma carregadas');
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar estatísticas da plataforma:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao buscar estatísticas da plataforma:', error);
      return null;
    }
  }
}

// Instância singleton
export const realStatsService = new RealStatsService();

// Funções de compatibilidade
export const getUserStats = realStatsService.getUserStats.bind(realStatsService);
export const getGameStats = realStatsService.getGameStats.bind(realStatsService);
export const getUserAchievements = realStatsService.getUserAchievements.bind(realStatsService);
export const getGlobalRanking = realStatsService.getGlobalRanking.bind(realStatsService);

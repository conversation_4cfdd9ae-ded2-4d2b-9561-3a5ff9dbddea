// Configurações de API

// ===== BACKEND API CONFIGURATION =====
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api';
export const API_TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT || '10000');
export const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:3002';

// Development flags
export const NODE_ENV = import.meta.env.VITE_NODE_ENV || 'development';
export const DEBUG = import.meta.env.VITE_DEBUG === 'true';
export const ENABLE_MOCK_DATA = import.meta.env.VITE_ENABLE_MOCK_DATA === 'true';
export const ENABLE_API_LOGGING = import.meta.env.VITE_ENABLE_API_LOGGING === 'true';

// ===== OPENAI API CONFIGURATION =====
// Chave da API OpenAI
export const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY || '********************************************************************************************************************************************************************';

// ID da organização OpenAI
export const OPENAI_ORGANIZATION_ID = import.meta.env.VITE_OPENAI_ORGANIZATION_ID || 'org-I7nn0ydPOGYLfmMLLgDPhDVp';

// URL base da API OpenAI
export const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

// Modelo da OpenAI para análise de imagens
export const OPENAI_VISION_MODEL = 'gpt-4o';

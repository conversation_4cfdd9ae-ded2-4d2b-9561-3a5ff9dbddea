const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createWalletForUser() {
  try {
    console.log('🔍 Buscando usuário matt...');
    
    // Buscar o usuário matt
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email')
      .eq('username', 'matt')
      .single();

    if (userError || !user) {
      console.error('❌ Usuário não encontrado:', userError);
      return;
    }

    console.log(`✅ Usuário encontrado: ${user.username} (${user.id})`);

    // Verificar se já existe carteira
    const { data: existingWallet } = await supabase
      .from('wallets')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (existingWallet) {
      console.log('⚠️ Carteira já existe para este usuário');
      return;
    }

    // Criar carteira usando service role (bypassa RLS)
    console.log('💰 Criando carteira...');
    const { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .insert({
        user_id: user.id,
        balance: 0.00,
        frozen_balance: 0.00,
        total_deposited: 0.00,
        total_withdrawn: 0.00
      })
      .select('*')
      .single();

    if (walletError) {
      console.error('❌ Erro ao criar carteira:', walletError);
      return;
    }

    console.log('✅ Carteira criada com sucesso!');
    console.log(`   ID: ${wallet.id}`);
    console.log(`   Saldo: R$ ${wallet.balance.toFixed(2)}`);
    console.log(`   Usuário: ${user.username}`);

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

createWalletForUser();

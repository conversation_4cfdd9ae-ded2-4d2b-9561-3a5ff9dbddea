import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, UserProfileUpdateData, PaginationQuery } from '../types/api';
import { logger, logBusinessEvent } from '../utils/logger';
import { createPagination } from '../utils/pagination';

/**
 * Get user profile by ID
 */
export const getUserById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // Get user with profile and stats
    const { data: userData, error } = await supabaseAdmin
      .from('users')
      .select(`
        id, username, display_name, avatar_url, status, country,
        created_at, last_login_at,
        user_profiles (
          bio, favorite_games, gaming_experience, preferred_game_modes,
          social_links, privacy_settings
        ),
        user_stats (
          total_matches, total_wins, total_losses, total_draws, win_rate,
          total_earnings, current_streak, best_streak, ranking_points,
          level, experience_points, achievements_count
        )
      `)
      .eq('id', id)
      .eq('status', 'active')
      .single();

    if (error || !userData) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check privacy settings
    const privacySettings = userData.user_profiles?.[0]?.privacy_settings || {};
    const isOwnProfile = currentUser?.id === id;
    const isFriend = currentUser ? await checkFriendship(currentUser.id, id) : false;

    // Filter data based on privacy settings
    let filteredData = { ...userData };

    if (!isOwnProfile) {
      // Hide email and phone for non-owners
      delete (filteredData as any).email;
      delete (filteredData as any).phone;

      // Apply privacy settings
      if (privacySettings.profile_visibility === 'private') {
        const response: ApiResponse = {
          success: false,
          error: 'Profile is private'
        };
        res.status(403).json(response);
        return;
      }

      if (privacySettings.stats_visibility === 'friends' && !isFriend) {
        delete (filteredData as any).user_stats;
      }

      if (privacySettings.match_history_visibility === 'private' ||
          (privacySettings.match_history_visibility === 'friends' && !isFriend)) {
        // Will be handled in match history endpoint
      }
    }

    const response: ApiResponse = {
      success: true,
      data: {
        user: filteredData
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get user by ID error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Update user profile
 */
export const updateProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const updateData: UserProfileUpdateData = req.body;

    // Update user basic info
    const userUpdates: any = {};
    if (updateData.display_name) userUpdates.display_name = updateData.display_name;
    if (updateData.avatar_url) userUpdates.avatar_url = updateData.avatar_url;

    if (Object.keys(userUpdates).length > 0) {
      const { error: userError } = await supabaseAdmin
        .from('users')
        .update(userUpdates)
        .eq('id', user.id);

      if (userError) {
        logger.error('Failed to update user:', userError);
        const response: ApiResponse = {
          success: false,
          error: 'Failed to update user profile'
        };
        res.status(500).json(response);
        return;
      }
    }

    // Update user profile
    const profileUpdates: any = {};
    if (updateData.bio !== undefined) profileUpdates.bio = updateData.bio;
    if (updateData.favorite_games) profileUpdates.favorite_games = updateData.favorite_games;
    if (updateData.gaming_experience) profileUpdates.gaming_experience = updateData.gaming_experience;
    if (updateData.preferred_game_modes) profileUpdates.preferred_game_modes = updateData.preferred_game_modes;
    if (updateData.social_links) profileUpdates.social_links = updateData.social_links;
    if (updateData.privacy_settings) profileUpdates.privacy_settings = updateData.privacy_settings;
    if (updateData.notification_preferences) profileUpdates.notification_preferences = updateData.notification_preferences;

    if (Object.keys(profileUpdates).length > 0) {
      const { error: profileError } = await supabaseAdmin
        .from('user_profiles')
        .update(profileUpdates)
        .eq('user_id', user.id);

      if (profileError) {
        logger.error('Failed to update user profile:', profileError);
        const response: ApiResponse = {
          success: false,
          error: 'Failed to update user profile'
        };
        res.status(500).json(response);
        return;
      }
    }

    logBusinessEvent('profile_updated', user.id, updateData);

    const response: ApiResponse = {
      success: true,
      message: 'Profile updated successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Update profile error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get user statistics
 */
export const getUserStats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // Check if user can view stats
    if (currentUser?.id !== id) {
      const canView = await checkStatsVisibility(currentUser?.id, id);
      if (!canView) {
        const response: ApiResponse = {
          success: false,
          error: 'Cannot view user statistics'
        };
        res.status(403).json(response);
        return;
      }
    }

    // Get user stats with additional data
    const { data: stats, error } = await supabaseAdmin
      .from('user_stats')
      .select(`
        *,
        users!inner (username, display_name, avatar_url)
      `)
      .eq('user_id', id)
      .single();

    if (error || !stats) {
      const response: ApiResponse = {
        success: false,
        error: 'User statistics not found'
      };
      res.status(404).json(response);
      return;
    }

    // Get recent matches
    const { data: recentMatches } = await supabaseAdmin
      .from('matches')
      .select(`
        id, status, created_at, finished_at,
        games (name, slug),
        match_participants!inner (
          user_id, score, placement, earnings
        )
      `)
      .eq('match_participants.user_id', id)
      .eq('status', 'completed')
      .order('finished_at', { ascending: false })
      .limit(10);

    // Get achievements
    const { data: achievements } = await supabaseAdmin
      .from('user_achievements')
      .select(`
        unlocked_at,
        achievements (name, description, icon_url, rarity, points)
      `)
      .eq('user_id', id)
      .eq('is_unlocked', true)
      .order('unlocked_at', { ascending: false });

    const response: ApiResponse = {
      success: true,
      data: {
        stats,
        recent_matches: recentMatches || [],
        achievements: achievements || []
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get user stats error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Search users
 */
export const searchUsers = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { query, page = 1, limit = 20 } = req.query as PaginationQuery & { query?: string };

    if (!query || query.length < 2) {
      const response: ApiResponse = {
        success: false,
        error: 'Search query must be at least 2 characters long'
      };
      res.status(400).json(response);
      return;
    }

    const offset = (page - 1) * limit;

    // Search users by username or display name
    const { data: users, error, count } = await supabaseAdmin
      .from('users')
      .select(`
        id, username, display_name, avatar_url, country, created_at,
        user_stats (ranking_points, level, total_matches, win_rate)
      `, { count: 'exact' })
      .eq('status', 'active')
      .or(`username.ilike.%${query}%,display_name.ilike.%${query}%`)
      .order('ranking_points', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Search users error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to search users'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        users: users || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Search users error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get user achievements
 */
export const getUserAchievements = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // Check if user can view achievements
    if (currentUser?.id !== id) {
      const canView = await checkStatsVisibility(currentUser?.id, id);
      if (!canView) {
        const response: ApiResponse = {
          success: false,
          error: 'Cannot view user achievements'
        };
        res.status(403).json(response);
        return;
      }
    }

    // Get all achievements with user progress
    const { data: userAchievements, error } = await supabaseAdmin
      .from('achievements')
      .select(`
        id, name, description, icon_url, badge_url, category, rarity, points,
        user_achievements (
          progress, is_unlocked, unlocked_at
        )
      `)
      .eq('is_active', true)
      .eq('user_achievements.user_id', id)
      .order('category')
      .order('points');

    if (error) {
      logger.error('Get user achievements error:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get user achievements'
      };
      res.status(500).json(response);
      return;
    }

    // Separate unlocked and locked achievements
    const unlocked = userAchievements?.filter(a => a.user_achievements?.[0]?.is_unlocked) || [];
    const locked = userAchievements?.filter(a => !a.user_achievements?.[0]?.is_unlocked) || [];

    const response: ApiResponse = {
      success: true,
      data: {
        unlocked,
        locked,
        total_achievements: userAchievements?.length || 0,
        unlocked_count: unlocked.length,
        total_points: unlocked.reduce((sum, a) => sum + a.points, 0)
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get user achievements error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

// Helper functions
const checkFriendship = async (userId1: string, userId2: string): Promise<boolean> => {
  const { data } = await supabaseAdmin
    .from('friendships')
    .select('id')
    .or(`and(requester_user_id.eq.${userId1},addressee_user_id.eq.${userId2}),and(requester_user_id.eq.${userId2},addressee_user_id.eq.${userId1})`)
    .eq('status', 'accepted')
    .single();

  return !!data;
};

const checkStatsVisibility = async (viewerId: string | undefined, targetUserId: string): Promise<boolean> => {
  if (!viewerId) return false;

  // Get privacy settings
  const { data: profile } = await supabaseAdmin
    .from('user_profiles')
    .select('privacy_settings')
    .eq('user_id', targetUserId)
    .single();

  const privacySettings = profile?.privacy_settings || {};

  if (privacySettings.stats_visibility === 'public') return true;
  if (privacySettings.stats_visibility === 'private') return false;
  if (privacySettings.stats_visibility === 'friends') {
    return await checkFriendship(viewerId, targetUserId);
  }

  return true; // Default to public
};

import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, NotificationSettingsData } from '../types/api';
import { logger, logBusinessEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';

/**
 * Get user notifications
 */
export const getNotifications = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);
    const { type, is_read } = req.query;

    let query = supabaseAdmin
      .from('notifications')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id);

    // Apply filters
    if (type) {
      query = query.eq('type', type);
    }
    if (is_read !== undefined) {
      query = query.eq('is_read', is_read === 'true');
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: notifications, error, count } = await query;

    if (error) {
      logger.error('Failed to get notifications:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve notifications'
      };
      res.status(500).json(response);
      return;
    }

    // Get unread count
    const { count: unreadCount } = await supabaseAdmin
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('is_read', false);

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        notifications: notifications || [],
        unread_count: unreadCount || 0
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get notifications error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Mark notification as read
 */
export const markNotificationAsRead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;

    // Verify notification belongs to user
    const { data: notification, error: notificationError } = await supabaseAdmin
      .from('notifications')
      .select('id, is_read')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (notificationError || !notification) {
      const response: ApiResponse = {
        success: false,
        error: 'Notification not found'
      };
      res.status(404).json(response);
      return;
    }

    // Mark as read if not already read
    if (!notification.is_read) {
      const { error: updateError } = await supabaseAdmin
        .from('notifications')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('id', id);

      if (updateError) {
        logger.error('Failed to mark notification as read:', updateError);
        const response: ApiResponse = {
          success: false,
          error: 'Failed to mark notification as read'
        };
        res.status(500).json(response);
        return;
      }
    }

    const response: ApiResponse = {
      success: true,
      message: 'Notification marked as read'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Mark notification as read error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Mark all notifications as read
 */
export const markAllNotificationsAsRead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    const { error } = await supabaseAdmin
      .from('notifications')
      .update({
        is_read: true,
        read_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .eq('is_read', false);

    if (error) {
      logger.error('Failed to mark all notifications as read:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to mark all notifications as read'
      };
      res.status(500).json(response);
      return;
    }

    logBusinessEvent('notifications_marked_all_read', user.id, {});

    const response: ApiResponse = {
      success: true,
      message: 'All notifications marked as read'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Mark all notifications as read error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Delete notification
 */
export const deleteNotification = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { id } = req.params;

    // Verify notification belongs to user and delete
    const { error } = await supabaseAdmin
      .from('notifications')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) {
      logger.error('Failed to delete notification:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to delete notification'
      };
      res.status(500).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      message: 'Notification deleted successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Delete notification error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Update notification settings
 */
export const updateNotificationSettings = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const settingsData: NotificationSettingsData = req.body;

    // Get current user profile
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('notification_preferences')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      logger.error('Failed to get user profile:', profileError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get user profile'
      };
      res.status(500).json(response);
      return;
    }

    // Merge with existing preferences
    const currentPreferences = profile?.notification_preferences || {};
    const updatedPreferences = {
      ...currentPreferences,
      ...settingsData
    };

    // Update notification preferences
    const { error: updateError } = await supabaseAdmin
      .from('user_profiles')
      .update({
        notification_preferences: updatedPreferences
      })
      .eq('user_id', user.id);

    if (updateError) {
      logger.error('Failed to update notification settings:', updateError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to update notification settings'
      };
      res.status(500).json(response);
      return;
    }

    logBusinessEvent('notification_settings_updated', user.id, settingsData);

    const response: ApiResponse = {
      success: true,
      message: 'Notification settings updated successfully',
      data: {
        notification_preferences: updatedPreferences
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Update notification settings error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get notification settings
 */
export const getNotificationSettings = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    // Get user notification preferences
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('notification_preferences')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      logger.error('Failed to get user profile:', profileError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to get user profile'
      };
      res.status(500).json(response);
      return;
    }

    // Default notification preferences
    const defaultPreferences = {
      email_notifications: true,
      push_notifications: true,
      friend_requests: true,
      match_updates: true,
      tournament_updates: true,
      promotional_emails: false,
      weekly_summary: true,
      achievement_notifications: true,
      chat_notifications: true,
      challenge_notifications: true
    };

    const preferences = {
      ...defaultPreferences,
      ...(profile?.notification_preferences || {})
    };

    const response: ApiResponse = {
      success: true,
      data: {
        notification_preferences: preferences
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get notification settings error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Create notification (internal use)
 */
export const createNotification = async (
  userId: string,
  type: string,
  title: string,
  message: string,
  data?: any
): Promise<boolean> => {
  try {
    const { error } = await supabaseAdmin
      .from('notifications')
      .insert({
        user_id: userId,
        type,
        title,
        message,
        data: data || {}
      });

    if (error) {
      logger.error('Failed to create notification:', error);
      return false;
    }

    return true;
  } catch (error) {
    logger.error('Create notification error:', error);
    return false;
  }
};

/**
 * Send bulk notifications (internal use)
 */
export const sendBulkNotifications = async (
  userIds: string[],
  type: string,
  title: string,
  message: string,
  data?: any
): Promise<boolean> => {
  try {
    const notifications = userIds.map(userId => ({
      user_id: userId,
      type,
      title,
      message,
      data: data || {}
    }));

    const { error } = await supabaseAdmin
      .from('notifications')
      .insert(notifications);

    if (error) {
      logger.error('Failed to send bulk notifications:', error);
      return false;
    }

    return true;
  } catch (error) {
    logger.error('Send bulk notifications error:', error);
    return false;
  }
};

/**
 * Clean old notifications (internal use)
 */
export const cleanOldNotifications = async (): Promise<void> => {
  try {
    // Delete notifications older than 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { error } = await supabaseAdmin
      .from('notifications')
      .delete()
      .lt('created_at', thirtyDaysAgo.toISOString());

    if (error) {
      logger.error('Failed to clean old notifications:', error);
    } else {
      logger.info('Old notifications cleaned successfully');
    }
  } catch (error) {
    logger.error('Clean old notifications error:', error);
  }
};

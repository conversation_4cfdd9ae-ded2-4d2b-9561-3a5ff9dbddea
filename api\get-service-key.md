# How to Get Supabase Service Role Key

The current `.env` file has the same key for both `SUPABASE_ANON_KEY` and `SUPABASE_SERVICE_ROLE_KEY`, which is causing RLS policy violations.

## Steps to get the correct Service Role Key:

1. Go to your Supabase project dashboard: https://supabase.com/dashboard/project/bnibkehmzkbvkitchrfq

2. Navigate to **Settings** → **API**

3. You'll see two keys:
   - **anon public** (already correct in .env): `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJuaWJrZWhtemtidmtpdGNocmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzMjYyMDYsImV4cCI6MjA2NDkwMjIwNn0.fyb9oOyTFZ5iT83cUBY6yRNXTaQA3UJ9ghXwMk2LWV4`
   - **service_role secret** (this is what we need)

4. Copy the **service_role** key and replace the `SUPABASE_SERVICE_ROLE_KEY` value in your `.env` file

## Why this is needed:

- **anon key**: Used for client-side operations, respects RLS policies
- **service_role key**: Used for server-side admin operations, bypasses RLS policies

The wallet and affiliate program creation needs to bypass RLS because they're created automatically by the system, not by the user directly.

## Current Issue:
```
error: Failed to create wallet: new row violates row-level security policy for table "wallets"
error: Failed to create affiliate record: new row violates row-level security policy for table "affiliate_programs"
```

This happens because both operations are trying to use the anon key instead of the service role key.

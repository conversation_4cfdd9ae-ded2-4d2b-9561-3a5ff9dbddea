import { apiService, ApiResponse } from './apiService';

// Tipos para autenticação
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  display_name?: string;
  referral_code?: string | null;
}

export interface AuthResponse {
  user: {
    id: string;
    username: string;
    email: string;
    display_name: string;
    avatar_url?: string;
    status: string;
    created_at: string;
  };
  tokens: {
    access_token: string;
    refresh_token: string;
    expires_in: number;
  };
}

export interface RefreshTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface UserProfile {
  id: string;
  username: string;
  email: string;
  display_name: string;
  avatar_url?: string;
  status: string;
  country?: string;
  created_at: string;
  last_login_at?: string;
  user_profiles?: {
    bio?: string;
    favorite_games?: string[];
    gaming_experience?: string;
    preferred_game_modes?: string[];
    social_links?: Record<string, string>;
    privacy_settings?: Record<string, any>;
  };
  user_stats?: {
    total_matches: number;
    total_wins: number;
    total_losses: number;
    total_draws: number;
    win_rate: number;
    total_earnings: number;
    current_streak: number;
    best_streak: number;
    ranking_points: number;
    level: number;
    experience_points: number;
    achievements_count: number;
  };
}

class AuthService {
  // Login do usuário
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/login', credentials);

    if (response.success && response.data) {
      // Salva o token de acesso
      apiService.setAuthToken(response.data.tokens.access_token);

      // Salva o refresh token
      localStorage.setItem('refresh_token', response.data.tokens.refresh_token);

      // Salva dados do usuário
      localStorage.setItem('user_data', JSON.stringify(response.data.user));

      return response.data;
    }

    throw new Error(response.error || 'Erro no login');
  }

  // Registro de novo usuário
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/register', userData);

    if (response.success && response.data) {
      // Salva o token de acesso
      apiService.setAuthToken(response.data.tokens.access_token);

      // Salva o refresh token
      localStorage.setItem('refresh_token', response.data.tokens.refresh_token);

      // Salva dados do usuário
      localStorage.setItem('user_data', JSON.stringify(response.data.user));

      return response.data;
    }

    throw new Error(response.error || 'Erro no registro');
  }

  // Logout do usuário
  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      // Mesmo se der erro na API, limpa os dados locais
      console.warn('Erro ao fazer logout na API:', error);
    } finally {
      // Limpa todos os dados de autenticação
      apiService.clearAuthToken();
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user_data');
    }
  }

  // Renovar token de acesso
  async refreshToken(): Promise<string> {
    const refreshToken = localStorage.getItem('refresh_token');

    if (!refreshToken) {
      throw new Error('Refresh token não encontrado');
    }

    const response = await apiService.post<RefreshTokenResponse>('/auth/refresh', {
      refresh_token: refreshToken
    });

    if (response.success && response.data) {
      // Atualiza o token de acesso
      apiService.setAuthToken(response.data.access_token);

      // Atualiza o refresh token se fornecido
      if (response.data.refresh_token) {
        localStorage.setItem('refresh_token', response.data.refresh_token);
      }

      return response.data.access_token;
    }

    throw new Error(response.error || 'Erro ao renovar token');
  }

  // Obter perfil do usuário atual
  async getProfile(): Promise<UserProfile> {
    const response = await apiService.get<UserProfile>('/auth/profile');

    if (response.success && response.data) {
      // Atualiza dados do usuário no localStorage
      localStorage.setItem('user_data', JSON.stringify(response.data));
      return response.data;
    }

    throw new Error(response.error || 'Erro ao obter perfil');
  }

  // Verificar se o usuário está logado
  isAuthenticated(): boolean {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');
    return !!(token && userData);
  }

  // Obter dados do usuário do localStorage
  getCurrentUser(): UserProfile | null {
    const userData = localStorage.getItem('user_data');
    return userData ? JSON.parse(userData) : null;
  }

  // Obter token de autenticação
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  // Verificar email
  async verifyEmail(token: string): Promise<void> {
    const response = await apiService.post('/auth/verify-email', { token });

    if (!response.success) {
      throw new Error(response.error || 'Erro na verificação de email');
    }
  }

  // Solicitar recuperação de senha
  async requestPasswordReset(email: string): Promise<void> {
    const response = await apiService.post('/auth/forgot-password', { email });

    if (!response.success) {
      throw new Error(response.error || 'Erro ao solicitar recuperação de senha');
    }
  }

  // Redefinir senha
  async resetPassword(token: string, newPassword: string): Promise<void> {
    const response = await apiService.post('/auth/reset-password', {
      token,
      password: newPassword
    });

    if (!response.success) {
      throw new Error(response.error || 'Erro ao redefinir senha');
    }
  }

  // Atualizar perfil
  async updateProfile(profileData: Partial<UserProfile>): Promise<UserProfile> {
    const response = await apiService.put<UserProfile>('/auth/profile', profileData);

    if (response.success && response.data) {
      // Atualiza dados do usuário no localStorage
      localStorage.setItem('user_data', JSON.stringify(response.data));
      return response.data;
    }

    throw new Error(response.error || 'Erro ao atualizar perfil');
  }
}

// Instância singleton do serviço de autenticação
export const authService = new AuthService();

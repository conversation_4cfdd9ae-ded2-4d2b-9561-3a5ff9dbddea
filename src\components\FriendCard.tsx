import React from 'react';
import { MessageCircle, GamepadIcon, Swords } from 'lucide-react';

interface FriendCardProps {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'playing';
  avatar: string;
  game?: string;
  lastActive?: string;
  onChat: (id: string) => void;
  onChallenge: (id: string) => void;
}

export default function FriendCard({ 
  id,
  name, 
  status, 
  avatar, 
  game, 
  lastActive,
  onChat,
  onChallenge
}: FriendCardProps) {
  return (
    <div className="bg-gray-800 rounded-xl p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="relative">
            <img
              src={avatar}
              alt={name}
              className="w-12 h-12 rounded-full object-cover"
            />
            <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-gray-800 ${
              status === 'online' ? 'bg-green-500' :
              status === 'playing' ? 'bg-purple-500' :
              'bg-gray-500'
            }`} />
          </div>
          <div>
            <h3 className="text-white font-semibold">{name}</h3>
            <p className="text-sm text-gray-400">
              {status === 'playing' ? (
                <span className="flex items-center gap-1">
                  <GamepadIcon className="w-3 h-3" />
                  Jogando {game}
                </span>
              ) : status === 'offline' ? (
                `Visto por último ${lastActive}`
              ) : 'Online'}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {status !== 'offline' && (
            <button 
              onClick={() => onChallenge(id)}
              className="text-rose-400 hover:text-rose-300 transition-colors"
              title="Desafiar para uma partida"
            >
              <Swords className="w-5 h-5" />
            </button>
          )}
          <button 
            onClick={() => onChat(id)}
            className="text-yellow-400 hover:text-yellow-300 transition-colors"
            title="Enviar mensagem"
          >
            <MessageCircle className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
}
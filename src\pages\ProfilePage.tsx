import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Trophy,
  Target,
  BarChart3,
  Wallet,
  Gamepad,
  Swords,
  Brain,
  Medal,
  Crown,
  Flame,
  ArrowDownLeft,
  ArrowUpRight,
  CreditCard,
  CircleDollarSign,
  Settings,
  Loader2
} from 'lucide-react';
import StatCard from '../components/StatCard';
import AchievementCard from '../components/AchievementCard';
import AffiliateCard from '../components/AffiliateCard';
import GameHistoryCard from '../components/GameHistoryCard';
import TransactionCard from '../components/TransactionCard';
import DepositModal from '../components/DepositModal';
import WithdrawModal from '../components/WithdrawModal';
import { realUserService, UserProfile } from '../services/realUserService';
import { realWalletService, Transaction } from '../services/realWalletService';
import { realAffiliateService } from '../services/realAffiliateService';
import { realMatchHistoryService, MatchHistory } from '../services/realMatchHistoryService';
import { realAchievementsService, Achievement } from '../services/realAchievementsService';

export default function ProfilePage() {
  const [showDeposit, setShowDeposit] = useState(false);
  const [showWithdraw, setShowWithdraw] = useState(false);
  const [copied, setCopied] = useState(false);

  // Estados para dados da API
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [balance, setBalance] = useState<number>(0);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [affiliateStats, setAffiliateStats] = useState<any>(null);
  const [matchHistory, setMatchHistory] = useState<MatchHistory[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carrega dados do perfil
  useEffect(() => {
    const loadProfileData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando dados do perfil...');

        // Carrega perfil, saldo, transações, dados de afiliados, histórico e conquistas em paralelo
        // Usa Promise.allSettled para não falhar se uma API falhar
        const [profileResult, balanceResult, transactionsResult, affiliateResult, matchHistoryResult, achievementsResult] = await Promise.allSettled([
          realUserService.getCurrentUser(),
          realWalletService.getBalance(),
          realWalletService.getTransactions(1, 5), // Apenas as últimas 5 transações
          realAffiliateService.getAffiliateStats(),
          realMatchHistoryService.getMatchHistory(1, 3), // Últimas 3 partidas
          realAchievementsService.getMockAchievements() // Usando mock por enquanto
        ]);

        // Extrai dados dos resultados
        const profileData = profileResult.status === 'fulfilled' ? profileResult.value : null;
        const balanceData = balanceResult.status === 'fulfilled' ? balanceResult.value : null;
        const transactionsData = transactionsResult.status === 'fulfilled' ? transactionsResult.value : [];
        const affiliateData = affiliateResult.status === 'fulfilled' ? affiliateResult.value : null;

        if (profileData) {
          setUserProfile(profileData);
          console.log(`✅ Perfil carregado: ${profileData.username}`);
        }

        if (balanceData && typeof balanceData.balance === 'number') {
          setBalance(balanceData.balance);
          console.log(`✅ Saldo carregado: R$ ${balanceData.balance.toFixed(2)}`);
        } else {
          setBalance(0);
          console.log('⚠️ Saldo não encontrado, usando valor padrão R$ 0,00');
        }

        if (transactionsData && Array.isArray(transactionsData)) {
          setTransactions(transactionsData);
          console.log(`✅ ${transactionsData.length} transações carregadas`);
        } else {
          setTransactions([]);
          console.log('⚠️ Nenhuma transação encontrada ou dados inválidos');
        }

        if (affiliateData) {
          setAffiliateStats(affiliateData);
          console.log('✅ Dados de afiliados carregados');
        } else {
          console.log('⚠️ Dados de afiliados não encontrados');
        }

        // Processa histórico de partidas
        const matchHistoryData = matchHistoryResult.status === 'fulfilled' ? matchHistoryResult.value?.matches : null;
        if (matchHistoryData && Array.isArray(matchHistoryData)) {
          setMatchHistory(matchHistoryData);
          console.log(`✅ ${matchHistoryData.length} partidas do histórico carregadas`);
        } else {
          setMatchHistory([]);
          console.log('⚠️ Nenhuma partida encontrada no histórico');
        }

        // Processa conquistas
        const achievementsData = achievementsResult.status === 'fulfilled' ? achievementsResult.value : null;
        if (achievementsData && Array.isArray(achievementsData)) {
          setAchievements(achievementsData);
          console.log(`✅ ${achievementsData.length} conquistas carregadas`);
        } else {
          setAchievements([]);
          console.log('⚠️ Nenhuma conquista encontrada');
        }

      } catch (err) {
        console.error('❌ Erro ao carregar dados do perfil:', err);
        setError('Erro ao carregar dados do perfil. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadProfileData();
  }, []);

  const handleDeposit = async (amount: number, method: 'pix' | 'credit_card') => {
    const newTransaction = {
      id: Date.now().toString(),
      type: 'deposit' as const,
      amount: amount,
      date: 'Agora',
      description: `Depósito via ${method === 'pix' ? 'PIX' : 'Cartão'}`,
      status: 'pending' as const
    };
    setTransactions(prev => [newTransaction, ...prev]);

    setTimeout(() => {
      setTransactions(prev =>
        prev.map(t =>
          t.id === newTransaction.id
            ? { ...t, status: 'completed' as const }
            : t
        )
      );
      setBalance(prev => prev + amount);
    }, 2000);
  };

  const handleWithdraw = (amount: number, pixKey: string) => {
    const newTransaction = {
      id: Date.now().toString(),
      type: 'withdrawal' as const,
      amount: -amount,
      date: 'Agora',
      description: `Saque via PIX para ${pixKey}`,
      status: 'pending' as const
    };
    setTransactions(prev => [newTransaction, ...prev]);

    setTimeout(() => {
      setTransactions(prev =>
        prev.map(t =>
          t.id === newTransaction.id
            ? { ...t, status: 'completed' as const }
            : t
        )
      );
      setBalance(prev => prev - amount);
    }, 2000);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="w-8 h-8 animate-spin text-yellow-400" />
          <p className="text-gray-400">Carregando perfil...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-yellow-400 text-black px-4 py-2 rounded-lg hover:bg-yellow-300"
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Profile Header */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 sm:p-6 flex flex-col sm:flex-row items-center gap-4">
        <div className="relative">
          <img
            src={userProfile?.avatar_url || "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"}
            alt="Profile"
            className="w-16 h-16 sm:w-20 sm:h-20 rounded-full object-cover border-2 border-yellow-400"
          />
          <div className="absolute -bottom-1 -right-1 bg-green-500 w-4 h-4 rounded-full border-2 border-gray-900" />
        </div>
        <div className="text-center sm:text-left">
          <h1 className="text-white text-lg sm:text-xl font-bold">
            {userProfile?.display_name || userProfile?.username || 'Carregando...'}
          </h1>
          <p className="text-gray-400 text-sm sm:text-base">
            {userProfile?.user_stats ?
              `Nível ${userProfile.user_stats.level || 1} • ${userProfile.user_stats.ranking_points || 0} pontos` :
              'Carregando...'
            }
          </p>
          <div className="flex flex-wrap justify-center sm:justify-start gap-2 mt-2">
            <Link
              to="/stats"
              className="inline-flex items-center gap-1.5 bg-gray-800/50 backdrop-blur-sm border border-white/10 text-yellow-400 px-2 sm:px-3 py-1.5 rounded-lg hover:bg-gray-700/50 text-xs sm:text-sm"
            >
              <BarChart3 className="w-4 h-4" />
              <span className="text-sm font-medium">Ver Estatísticas</span>
            </Link>
            <Link
              to="/settings"
              className="inline-flex items-center gap-1.5 bg-gray-800/50 backdrop-blur-sm border border-white/10 text-gray-400 px-2 sm:px-3 py-1.5 rounded-lg hover:bg-gray-700/50 hover:text-white text-xs sm:text-sm"
            >
              <Settings className="w-4 h-4" />
              <span className="text-sm font-medium">Configurações</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Balance Card */}
      <div className="bg-gradient-to-r from-rose-400 to-pink-400 rounded-xl p-4 sm:p-6">
        <div className="flex items-center gap-2 mb-2">
          <Wallet className="w-6 h-6 text-black" />
          <h2 className="text-black font-bold">Saldo Disponível</h2>
        </div>
        <p className="text-2xl sm:text-3xl font-bold text-black mb-4">R$ {balance.toFixed(2)}</p>
        <div className="flex flex-col sm:flex-row gap-2">
          <button
            onClick={() => setShowDeposit(true)}
            className="flex-1 bg-black text-white rounded-lg py-2 px-4 flex items-center justify-center gap-2 hover:bg-gray-900"
          >
            <ArrowDownLeft className="w-5 h-5" />
            Depositar
          </button>
          <button
            onClick={() => setShowWithdraw(true)}
            className="flex-1 bg-black text-white rounded-lg py-2 px-4 flex items-center justify-center gap-2 hover:bg-gray-900"
          >
            <ArrowUpRight className="w-5 h-5" />
            Sacar
          </button>
        </div>
      </div>

      {/* Affiliate Program */}
      <section>
        <AffiliateCard
          affiliateLink={affiliateStats?.affiliate_link || `playstrike.com/ref/${userProfile?.username || 'user'}`}
          invitedFriends={affiliateStats?.total_referrals || 0}
          totalEarnings={affiliateStats?.total_earnings || 0}
        />
      </section>

      {/* Payment Methods */}
      <section>
        <h2 className="text-white text-lg font-bold mb-3">Métodos de Pagamento</h2>
        <div className="grid grid-cols-1 xs:grid-cols-2 gap-3">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 flex items-center gap-3">
            <div className="bg-gray-700 p-2 rounded-lg">
              <CreditCard className="w-6 h-6 text-yellow-400" />
            </div>
            <span className="text-white">Cartão de Crédito</span>
          </div>
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 flex items-center gap-3">
            <div className="bg-gray-700 p-2 rounded-lg">
              <CircleDollarSign className="w-6 h-6 text-yellow-400" />
            </div>
            <span className="text-white">PIX</span>
          </div>
        </div>
      </section>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 xs:grid-cols-4 gap-3">
        <StatCard
          icon={Trophy}
          label="Vitórias"
          value={userProfile?.user_stats?.total_wins?.toString() || "0"}
        />
        <StatCard
          icon={Target}
          label="Taxa de Vitória"
          value={userProfile?.user_stats?.win_rate ? `${userProfile.user_stats.win_rate.toFixed(0)}%` : "0%"}
        />
        <StatCard
          icon={Gamepad}
          label="Partidas"
          value={userProfile?.user_stats?.total_matches?.toString() || "0"}
        />
        <StatCard
          icon={Medal}
          label="Conquistas"
          value={userProfile?.user_stats?.achievements_count ? `${userProfile.user_stats.achievements_count}/30` : "0/30"}
        />
      </div>

      {/* Achievements */}
      <section>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-white text-base sm:text-lg font-bold">Conquistas</h2>
          <button className="text-yellow-400 text-xs sm:text-sm">Ver Tudo</button>
        </div>
        <div className="space-y-3">
          {achievements.length > 0 ? (
            achievements.slice(0, 3).map((achievement) => {
              // Mapeia ícones
              const iconMap: { [key: string]: any } = {
                'swords': Swords,
                'brain': Brain,
                'flame': Flame,
                'trophy': Trophy,
                'target': Target,
                'medal': Medal
              };
              const IconComponent = iconMap[achievement.icon] || Trophy;

              return (
                <AchievementCard
                  key={achievement.id}
                  icon={IconComponent}
                  title={achievement.name}
                  description={achievement.description}
                  progress={achievement.progress}
                  isCompleted={achievement.is_unlocked}
                />
              );
            })
          ) : (
            <div className="text-gray-400 text-center py-4">
              Nenhuma conquista encontrada
            </div>
          )}
        </div>
      </section>

      {/* Recent Transactions */}
      <section>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-white text-base sm:text-lg font-bold">Transações Recentes</h2>
          <button className="text-yellow-400 text-xs sm:text-sm">Ver Tudo</button>
        </div>
        <div className="space-y-3">
          {Array.isArray(transactions) && transactions.length > 0 ? (
            transactions.slice(0, 3).map((transaction) => (
              <TransactionCard
                key={transaction.id}
                type={transaction.type}
                amount={transaction.amount}
                date={transaction.date}
                description={transaction.description}
                status={transaction.status}
              />
            ))
          ) : (
            <div className="text-gray-400 text-center py-4">
              Nenhuma transação encontrada
            </div>
          )}
        </div>
      </section>

      {/* Game History */}
      <section>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-white text-base sm:text-lg font-bold">Histórico de Jogos</h2>
          <button className="text-yellow-400 text-xs sm:text-sm">Ver Tudo</button>
        </div>
        <div className="space-y-3">
          {matchHistory.length > 0 ? (
            matchHistory.map((match) => {
              // Formata o tempo
              const timeFormatted = match.finished_at
                ? new Date(match.finished_at).toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                  })
                : 'Em andamento';

              return (
                <GameHistoryCard
                  key={match.id}
                  gameTitle={match.game_name}
                  date={timeFormatted}
                  result={match.result}
                  points={match.points}
                  position={match.participant.placement || 1}
                  totalPlayers={match.result_data?.total_participants || 2}
                />
              );
            })
          ) : (
            <div className="text-gray-400 text-center py-4">
              Nenhuma partida encontrada
            </div>
          )}
        </div>
      </section>

      {/* Modals */}
      {showDeposit && (
        <DepositModal
          onClose={() => setShowDeposit(false)}
          onDeposit={handleDeposit}
        />
      )}

      {showWithdraw && (
        <WithdrawModal
          onClose={() => setShowWithdraw(false)}
          onWithdraw={handleWithdraw}
          balance={balance}
        />
      )}
    </div>
  );
}
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Trophy, ArrowLeft, Wallet, Coins, Loader2 } from 'lucide-react';
import { realMatchService, Match } from '../services/realMatchService';
import { useUser } from '../contexts/UserContext';
import { realWalletService } from '../services/realWalletService';

interface MatchResultData {
  winner: {
    id: string;
    name: string;
    avatar: string;
  };
  isCurrentPlayerWinner: boolean;
  amountWon: number;
  amountLost: number;
  totalPrize: number;
  entryFee: number;
}

export default function MatchResultsPage() {
  const navigate = useNavigate();
  const { gameId, matchId } = useParams();
  const { user, setUserFromAuth } = useUser();
  const [showPrizeAnimation, setShowPrizeAnimation] = useState(false);

  // Estados para dados da API
  const [match, setMatch] = useState<Match | null>(null);
  const [matchResult, setMatchResult] = useState<MatchResultData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isClaimingPrize, setIsClaimingPrize] = useState(false);

  // Carrega dados da partida
  useEffect(() => {
    const loadMatchData = async () => {
      if (!matchId) {
        console.log('⚠️ ID da partida não fornecido');
        setError('ID da partida não fornecido');
        setIsLoading(false);
        return;
      }

      // Aguardar o usuário carregar
      if (!user.id) {
        console.log('⚠️ Aguardando usuário carregar...');
        return; // Não definir erro, apenas aguardar
      }

      // Verificar se é uma página de erro
      if (matchId === 'error') {
        console.log('⚠️ Página de erro - Match ID não foi encontrado');
        setError('O ID da partida não foi encontrado. Isso pode acontecer se a partida não foi criada corretamente ou se houve um problema de conexão.');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        console.log(`🔄 Carregando dados da partida ${matchId}...`);

        const matchData = await realMatchService.getMatchById(matchId);

        if (matchData) {
          setMatch(matchData);
          console.log(`✅ Partida ${matchId} carregada:`, matchData);

          // Processar dados do resultado
          console.log(`🔍 Verificando vencedor da partida ${matchId}:`);
          console.log(`  winner_user_id: "${matchData.winner_user_id}"`);
          console.log(`  user.id: "${user.id}"`);
          console.log(`  São iguais? ${matchData.winner_user_id === user.id}`);

          const isWinner = matchData.winner_user_id === user.id;
          const entryFee = matchData.entry_fee || 0;
          const totalPrize = matchData.total_prize || matchData.prize_amount || 0;

          // Buscar dados do participante para obter earnings
          const participant = matchData.match_participants?.find((p: any) => p.user_id === user.id);
          const earnings = participant?.earnings || 0;

          console.log(`  participant:`, participant);
          console.log(`  earnings: ${earnings}`);

          const resultData: MatchResultData = {
            winner: {
              id: matchData.winner_user_id || '',
              name: 'Vencedor', // Será atualizado com dados reais se necessário
              avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
            },
            isCurrentPlayerWinner: isWinner,
            amountWon: isWinner ? earnings : 0,
            amountLost: isWinner ? 0 : entryFee,
            totalPrize,
            entryFee
          };

          setMatchResult(resultData);
          console.log(`📊 Resultado processado:`, resultData);

        } else {
          console.log('⚠️ Partida não encontrada');
          setError('Partida não encontrada no servidor. Verifique se a partida foi criada corretamente.');
        }

      } catch (err) {
        console.error('❌ Erro ao carregar partida:', err);
        setError('Erro ao carregar dados da partida');
      } finally {
        setIsLoading(false);
      }
    };

    loadMatchData();
  }, [matchId, user.id]);

  // Função para atualizar saldo da wallet
  const updateWalletBalance = async () => {
    try {
      const walletData = await realWalletService.getBalance();
      if (walletData) {
        // Atualizar contexto do usuário com novo saldo
        setUserFromAuth({
          user: user,
          tokens: { access_token: '', refresh_token: '' }
        });
        console.log('💰 Saldo da wallet atualizado');
      }
    } catch (error) {
      console.error('❌ Erro ao atualizar saldo da wallet:', error);
    }
  };

  // Função para resgatar prêmio
  const handleClaimPrize = async () => {
    setIsClaimingPrize(true);
    setShowPrizeAnimation(true);

    // Atualizar saldo da wallet
    await updateWalletBalance();

    // Wait for animation to complete
    await new Promise(resolve => setTimeout(resolve, 2500));

    // Navigate back to game page
    navigate(`/external-games/${gameId}`);
  };

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-6">
      <div className="bg-gray-800 rounded-xl p-8 w-full max-w-md">
        {/* Error State */}
        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-center mb-6">
            <p className="text-red-400 mb-2">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Tentar novamente
            </button>
          </div>
        )}

        {/* Loading State */}
        {isLoading ? (
          <div className="text-center space-y-6">
            <Loader2 className="w-12 h-12 text-indigo-400 mx-auto animate-spin" />
            <h3 className="text-white font-bold text-lg">Carregando resultado...</h3>
            <p className="text-gray-400">Aguarde enquanto buscamos os dados da partida.</p>
          </div>
        ) : matchResult ? (
          <div className="text-center space-y-6">
          {/* Trophy Animation */}
          <div className="relative">
            <Trophy className={`w-24 h-24 mx-auto animate-bounce ${
              matchResult.isCurrentPlayerWinner ? 'text-yellow-400' : 'text-gray-400'
            }`} />
            <div className={`absolute inset-0 blur-3xl rounded-full ${
              matchResult.isCurrentPlayerWinner ? 'bg-yellow-400/20' : 'bg-gray-400/20'
            }`} />
          </div>

          {/* Winner Info */}
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              {matchResult.isCurrentPlayerWinner ? 'Você Venceu!' : 'Você Perdeu!'}
            </h1>
            <div className="flex items-center justify-center gap-3 mb-4">
              <img
                src={user.avatar || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'}
                alt={user.name || user.username}
                className={`w-16 h-16 rounded-full border-4 ${
                  matchResult.isCurrentPlayerWinner ? 'border-yellow-400' : 'border-gray-400'
                }`}
              />
            </div>
          </div>

          {/* Prize/Loss Info */}
          <div className={`rounded-xl p-4 space-y-2 ${
            matchResult.isCurrentPlayerWinner ? 'bg-green-500/20' : 'bg-red-500/20'
          }`}>
            {matchResult.isCurrentPlayerWinner ? (
              <>
                <p className="text-green-400 font-bold text-lg">
                  Você ganhou: R$ {matchResult.amountWon.toFixed(2)}
                </p>
                <p className="text-gray-400 text-sm">
                  Prêmio total: R$ {matchResult.totalPrize.toFixed(2)}
                </p>
                <button
                  disabled={isClaimingPrize}
                  className="w-full bg-green-500 text-white font-bold py-3 rounded-lg flex items-center justify-center gap-2 hover:bg-green-600 disabled:opacity-50"
                  onClick={handleClaimPrize}
                >
                  <Wallet className="w-5 h-5" />
                  {isClaimingPrize ? 'Processando...' : 'Continuar'}
                </button>
              </>
            ) : (
              <>
                <p className="text-red-400 font-bold text-lg">
                  Você perdeu: R$ {matchResult.amountLost.toFixed(2)}
                </p>
                <p className="text-gray-400 text-sm">
                  Taxa de entrada perdida
                </p>
                <button
                  onClick={() => navigate(`/external-games/${gameId}`)}
                  className="w-full bg-gray-700 text-white font-bold py-3 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-600"
                >
                  <ArrowLeft className="w-5 h-5" />
                  Voltar ao Jogo
                </button>
              </>
            )}
          </div>
          </div>
        ) : (
          <div className="text-center space-y-6">
            <div className="text-white">
              <h2 className="text-xl font-bold mb-2">Dados não disponíveis</h2>
              <p className="text-gray-400 mb-4">Não foi possível carregar os dados da partida.</p>
              <button
                onClick={() => navigate(`/external-games/${gameId}`)}
                className="bg-gray-700 text-white font-bold py-3 px-6 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-600 mx-auto"
              >
                <ArrowLeft className="w-5 h-5" />
                Voltar
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Prize Animation */}
      {showPrizeAnimation && matchResult && matchResult.isCurrentPlayerWinner && (
        <div className="fixed inset-0 flex items-center justify-center z-[100] pointer-events-none">
          <div className="animate-[slideUp_2.5s_ease-out] opacity-0">
            <div className="bg-green-500 text-white font-bold px-6 py-4 rounded-xl flex items-center gap-3 shadow-lg">
              <Coins className="w-6 h-6" />
              <span className="text-2xl">+R$ {matchResult.amountWon.toFixed(2)}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
import { Tournament, TournamentSeason, TournamentBracket, Match, Participant } from '../types/tournament';

// Função auxiliar para gerar participantes mockados
const generateMockParticipants = (count: number, bracketId: string): Participant[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: `player_${bracketId}_${i + 1}`,
    name: `Jogador ${i + 1}`,
    avatar: `https://i.pravatar.cc/150?img=${(i % 70) + 1}`,
    status: i < Math.floor(count * 0.8) ? 'confirmed' : 'registered',
    position: i + 1,
    score: Math.floor(Math.random() * 1000)
  }));
};

// Função auxiliar para gerar partidas mockadas
const generateMockMatches = (bracketId: string, participants: Participant[]): Match[] => {
  const rounds = Math.log2(32); // 5 rounds para 32 participantes
  const matches: Match[] = [];

  for (let round = 1; round <= rounds; round++) {
    const matchesInRound = Math.pow(2, rounds - round);

    for (let position = 1; position <= matchesInRound; position++) {
      const match: Match = {
        id: `${bracketId}_r${round}_m${position}`,
        round,
        position,
        status: round === 1 ? 'scheduled' : 'scheduled'
      };

      // Adicionar jogadores às partidas da primeira rodada
      if (round === 1) {
        const player1Index = (position - 1) * 2;
        const player2Index = player1Index + 1;

        if (player1Index < participants.length) {
          match.player1 = {
            id: participants[player1Index].id,
            name: participants[player1Index].name,
            avatar: participants[player1Index].avatar
          };
        }

        if (player2Index < participants.length) {
          match.player2 = {
            id: participants[player2Index].id,
            name: participants[player2Index].name,
            avatar: participants[player2Index].avatar
          };
        }

        // Definir algumas partidas como concluídas
        if (position <= matchesInRound / 2) {
          match.status = 'completed';
          match.winner = match.player1?.id;
          match.player1 = { ...match.player1!, score: Math.floor(Math.random() * 16) + 10 };
          match.player2 = { ...match.player2!, score: Math.floor(Math.random() * 10) };
        }
      }

      matches.push(match);
    }
  }

  return matches;
};

// Função para gerar nome de grupo no formato A, B, C, ..., Z, AA, AB, AC, etc.
const generateGroupName = (index: number): string => {
  // Para índices de 0 a 25, retorna A-Z
  if (index < 26) {
    return String.fromCharCode(65 + index);
  }

  // Para índices maiores que 25, retorna AA, AB, AC, etc.
  const firstChar = String.fromCharCode(65 + Math.floor((index - 26) / 26));
  const secondChar = String.fromCharCode(65 + ((index - 26) % 26));
  return `${firstChar}${secondChar}`;
};

// Função para gerar uma subchave mockada
const generateMockBracket = (seasonId: string, bracketIndex: number, status: 'upcoming' | 'in_progress' | 'completed'): TournamentBracket => {
  const bracketId = `bracket_${seasonId}_${bracketIndex}`;
  const participants = generateMockParticipants(32, bracketId);
  const matches = generateMockMatches(bracketId, participants);

  return {
    id: bracketId,
    name: `Grupo ${generateGroupName(bracketIndex)}`, // A, B, C, ..., Z, AA, AB, AC, etc.
    matches,
    participants,
    status,
    ...(status === 'completed' ? { winnerId: participants[0].id } : {})
  };
};

// Função para gerar uma temporada mockada
const generateMockSeason = (
  order: number,
  name: string,
  status: 'upcoming' | 'in_progress' | 'completed',
  bracketCount: number,
  startDate: string,
  endDate: string
): TournamentSeason => {
  const seasonId = `season_${order}`;

  return {
    id: seasonId,
    name,
    order,
    status,
    startDate,
    endDate,
    brackets: Array.from({ length: bracketCount }, (_, i) =>
      generateMockBracket(seasonId, i, status)
    )
  };
};

// Torneio mockado com sistema de temporadas e subchaves
export const getMockLargeTournament = (tournamentId: string): Tournament => {
  // Definir o usuário atual como o jogador 5 na primeira subchave da primeira temporada
  const currentUserId = 'player_bracket_season_1_0_5';

  const seasons: TournamentSeason[] = [
    generateMockSeason(
      1,
      'Chaves de Entrada',
      'completed',
      32, // 32 subchaves com 32 participantes cada = 1024 participantes
      '2024-04-01',
      '2024-04-10'
    ),
    generateMockSeason(
      2,
      'Chaves das Oitavas',
      'in_progress',
      1, // 1 subchave com 32 participantes (vencedores da fase anterior)
      '2024-04-15',
      '2024-04-25'
    ),
    generateMockSeason(
      3,
      'Chaves das Quartas',
      'upcoming',
      1, // 1 subchave com 8 participantes
      '2024-05-01',
      '2024-05-05'
    ),
    generateMockSeason(
      4,
      'Chaves da Semi-Final',
      'upcoming',
      1, // 1 subchave com 4 participantes
      '2024-05-10',
      '2024-05-12'
    ),
    generateMockSeason(
      5,
      'Chaves da Final',
      'upcoming',
      1, // 1 subchave com 2 participantes
      '2024-05-20',
      '2024-05-20'
    )
  ];

  return {
    id: tournamentId,
    title: 'CS2 Pro League - Season 1',
    game: 'Counter-Strike 2',
    gameIcon: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80',
    format: 'elimination',
    status: 'in_progress',
    startDate: '2024-04-01',
    endDate: '2024-05-20',
    registrationDeadline: '2024-03-31',
    entryFee: 50,
    prizePool: 10000,
    participants: 1024,
    maxParticipants: 1024,
    description: 'Participe do maior torneio de CS2 da plataforma! Competição eliminatória com os melhores jogadores do Brasil.',
    rules: `# Regras do Torneio

1. Todas as partidas serão no formato MR15 (melhor de 30 rounds)
2. Em caso de empate, haverá overtime com MR3
3. Os mapas serão escolhidos por veto
4. Cada jogador tem até 10 minutos de pausa técnica por partida
5. Comportamento antidesportivo resultará em desclassificação
6. Todas as partidas devem ser gravadas
7. Decisões da administração do torneio são finais`,
    organizer: {
      name: 'Playstrike Official',
      avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
    },
    prizeDistribution: {
      first: 60,
      second: 30,
      third: 10
    },

    // Novos campos
    seasons,
    currentSeasonId: 'season_2',
    totalParticipants: 1024,

    // Informações do usuário atual
    currentPlayerNextMatch: {
      id: 'next_match_1',
      round: 1,
      player1: {
        id: currentUserId,
        name: 'Seu Usuário',
        avatar: 'https://i.pravatar.cc/150?img=1'
      },
      player2: {
        id: 'opponent',
        name: 'Jogador Oponente',
        avatar: 'https://i.pravatar.cc/150?img=2'
      },
      scheduledTime: new Date(Date.now() + 1000 * 60 * 60 * 24).toISOString(), // 1 dia no futuro
      status: 'scheduled'
    },

    userBracket: {
      seasonId: 'season_2',
      seasonName: 'Chaves das Oitavas',
      bracketId: 'bracket_season_2_0',
      bracketName: 'Grupo A',
      participants: 32,
      userPosition: 5,
      status: 'in_progress'
    }
  };
};

// Função para obter um torneio pelo ID
export const getTournamentById = (tournamentId: string): Tournament => {
  // Para demonstração, retornamos o torneio grande mockado
  return getMockLargeTournament(tournamentId);
};

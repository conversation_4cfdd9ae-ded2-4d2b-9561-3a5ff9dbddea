import { apiService } from './apiService';
import { authService } from './authService';

// Tipos para dados do usuário
export interface UserProfile {
  id: string;
  username: string;
  email: string;
  display_name: string;
  avatar_url?: string;
  status: string;
  country?: string;
  created_at: string;
  last_login_at?: string;
  user_profiles?: {
    bio?: string;
    favorite_games?: string[];
    gaming_experience?: string;
    preferred_game_modes?: string[];
    social_links?: Record<string, string>;
    privacy_settings?: Record<string, any>;
  };
  user_stats?: {
    total_matches: number;
    total_wins: number;
    total_losses: number;
    total_draws: number;
    win_rate: number;
    total_earnings: number;
    current_streak: number;
    best_streak: number;
    ranking_points: number;
    level: number;
    experience_points: number;
    achievements_count: number;
  };
}

export interface UserStats {
  total_matches: number;
  total_wins: number;
  total_losses: number;
  total_draws: number;
  win_rate: number;
  total_earnings: number;
  current_streak: number;
  best_streak: number;
  ranking_points: number;
  level: number;
  experience_points: number;
  achievements_count: number;
}

export interface Friend {
  id: string;
  username: string;
  display_name: string;
  avatar_url?: string;
  status: 'online' | 'offline' | 'playing';
  game?: string;
  last_active?: string;
}

class RealUserService {

  // Obtém perfil do usuário atual
  async getCurrentUser(): Promise<UserProfile | null> {
    try {
      console.log('🔍 Buscando perfil do usuário atual...');

      // Primeiro verifica se há um usuário logado
      if (!authService.isAuthenticated()) {
        console.log('❌ Usuário não está autenticado');
        return null;
      }

      const response = await apiService.get<{ user: UserProfile }>('/auth/profile');

      if (response.success && response.data && response.data.user) {
        console.log('✅ Perfil do usuário carregado:', response.data.user.username);
        return response.data.user;
      } else {
        console.warn('⚠️ Erro ao carregar perfil:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao buscar perfil do usuário:', error);
      return null;
    }
  }

  // Obtém perfil de um usuário específico
  async getUserById(userId: string): Promise<UserProfile | null> {
    try {
      console.log(`🔍 Buscando perfil do usuário ${userId}...`);

      const response = await apiService.get<UserProfile>(`/users/${userId}`);

      if (response.success && response.data) {
        console.log(`✅ Perfil do usuário ${userId} carregado`);
        return response.data;
      } else {
        console.warn(`⚠️ Erro ao carregar perfil do usuário ${userId}:`, response.error);
        return null;
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar perfil do usuário ${userId}:`, error);
      return null;
    }
  }

  // Atualiza perfil do usuário
  async updateProfile(profileData: Partial<UserProfile>): Promise<UserProfile | null> {
    try {
      console.log('🔄 Atualizando perfil do usuário...');

      const response = await apiService.put<UserProfile>('/auth/profile', profileData);

      if (response.success && response.data) {
        console.log('✅ Perfil atualizado com sucesso');
        return response.data;
      } else {
        console.warn('⚠️ Erro ao atualizar perfil:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao atualizar perfil:', error);
      return null;
    }
  }

  // Obtém estatísticas do usuário
  async getUserStats(userId?: string): Promise<UserStats | null> {
    try {
      const endpoint = userId ? `/users/${userId}/stats` : '/auth/stats';
      console.log(`🔍 Buscando estatísticas do usuário...`);

      const response = await apiService.get<UserStats>(endpoint);

      if (response.success && response.data) {
        console.log('✅ Estatísticas carregadas');
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar estatísticas:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao buscar estatísticas:', error);
      return null;
    }
  }

  // Obtém lista de amigos
  async getFriends(): Promise<Friend[]> {
    try {
      console.log('🔍 Buscando lista de amigos...');

      const response = await apiService.get<Friend[]>('/social/friends');

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} amigos carregados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar amigos:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar amigos:', error);
      return [];
    }
  }

  // Adiciona amigo
  async addFriend(userId: string): Promise<boolean> {
    try {
      console.log(`🤝 Adicionando amigo ${userId}...`);

      const response = await apiService.post(`/social/friends/${userId}`);

      if (response.success) {
        console.log(`✅ Amigo ${userId} adicionado`);
        return true;
      } else {
        console.warn(`⚠️ Erro ao adicionar amigo ${userId}:`, response.error);
        return false;
      }
    } catch (error) {
      console.error(`❌ Erro ao adicionar amigo ${userId}:`, error);
      return false;
    }
  }

  // Remove amigo
  async removeFriend(userId: string): Promise<boolean> {
    try {
      console.log(`💔 Removendo amigo ${userId}...`);

      const response = await apiService.delete(`/social/friends/${userId}`);

      if (response.success) {
        console.log(`✅ Amigo ${userId} removido`);
        return true;
      } else {
        console.warn(`⚠️ Erro ao remover amigo ${userId}:`, response.error);
        return false;
      }
    } catch (error) {
      console.error(`❌ Erro ao remover amigo ${userId}:`, error);
      return false;
    }
  }

  // Upload de avatar
  async uploadAvatar(file: File): Promise<string | null> {
    try {
      console.log('📸 Fazendo upload do avatar...');

      const response = await apiService.upload<{ avatar_url: string }>('/auth/avatar', file);

      if (response.success && response.data) {
        console.log('✅ Avatar atualizado com sucesso');
        return response.data.avatar_url;
      } else {
        console.warn('⚠️ Erro ao fazer upload do avatar:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao fazer upload do avatar:', error);
      return null;
    }
  }

  // Busca usuários
  async searchUsers(query: string): Promise<UserProfile[]> {
    try {
      console.log(`🔍 Buscando usuários: "${query}"...`);

      const response = await apiService.get<UserProfile[]>('/users/search', {
        params: { q: query }
      });

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} usuários encontrados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao buscar usuários:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar usuários:', error);
      return [];
    }
  }

  // Obtém ranking de usuários
  async getUserRanking(page: number = 1, limit: number = 50): Promise<UserProfile[]> {
    try {
      console.log(`🏆 Buscando ranking de usuários (página ${page})...`);

      const response = await apiService.get<UserProfile[]>('/users/ranking', {
        params: { page, limit }
      });

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} usuários no ranking carregados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar ranking:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar ranking:', error);
      return [];
    }
  }
}

// Instância singleton
export const realUserService = new RealUserService();

// Funções de compatibilidade com o serviço antigo
export const getCurrentUser = realUserService.getCurrentUser.bind(realUserService);
export const getUserById = realUserService.getUserById.bind(realUserService);
export const updateProfile = realUserService.updateProfile.bind(realUserService);
export const getUserStats = realUserService.getUserStats.bind(realUserService);
export const getFriends = realUserService.getFriends.bind(realUserService);

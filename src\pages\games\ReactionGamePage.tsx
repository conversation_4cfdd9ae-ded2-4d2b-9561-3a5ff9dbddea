import React from 'react';
import { MousePointer } from 'lucide-react';
import GamePage from './GamePage';

export default function ReactionGamePage() {
  const gameInfo = {
    id: 'reaction',
    title: 'REAÇÃO RÁPIDA',
    description: 'Teste seus reflexos ao máximo! Clique nos alvos assim que eles aparecerem para ganhar pontos. Quanto mais rápido você for, maior será sua pontuação.',
    imageUrl: 'https://images.unsplash.com/photo-1550745165-9bc0b252726f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    players: 523,
    icon: MousePointer,
    prizePool: 1800
  };

  return <GamePage gameInfo={gameInfo} />;
}

import { apiService } from './apiService';
import { Tournament, TournamentSeason, TournamentBracket } from '../types/tournament';
import { ENABLE_MOCK_DATA } from '../config/api';

// Importa o serviço mock como fallback
import { getMockLargeTournament } from './tournamentService';

export interface TournamentListResponse {
  success: boolean;
  data: Tournament[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface TournamentDetailResponse {
  success: boolean;
  data: Tournament;
}

class RealTournamentService {
  
  // Obtém lista de torneios
  async getTournaments(page: number = 1, limit: number = 10): Promise<Tournament[]> {
    try {
      // Se mock está habilitado, usa dados mock
      if (ENABLE_MOCK_DATA) {
        console.log('🎭 Usando dados mock para torneios');
        return [getMockLargeTournament('tournament_1')];
      }

      console.log('🌐 Buscando torneios da API...');
      
      const response = await apiService.get<Tournament[]>('/tournaments', {
        params: { page, limit }
      });

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} torneios carregados da API`);
        return response.data;
      } else {
        console.warn('⚠️ API retornou erro, usando dados mock como fallback');
        return [getMockLargeTournament('tournament_1')];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar torneios da API:', error);
      console.log('🎭 Usando dados mock como fallback');
      return [getMockLargeTournament('tournament_1')];
    }
  }

  // Obtém detalhes de um torneio específico
  async getTournamentById(tournamentId: string): Promise<Tournament> {
    try {
      // Se mock está habilitado, usa dados mock
      if (ENABLE_MOCK_DATA) {
        console.log(`🎭 Usando dados mock para torneio ${tournamentId}`);
        return getMockLargeTournament(tournamentId);
      }

      console.log(`🌐 Buscando torneio ${tournamentId} da API...`);
      
      const response = await apiService.get<Tournament>(`/tournaments/${tournamentId}`);

      if (response.success && response.data) {
        console.log(`✅ Torneio ${tournamentId} carregado da API`);
        return response.data;
      } else {
        console.warn('⚠️ API retornou erro, usando dados mock como fallback');
        return getMockLargeTournament(tournamentId);
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar torneio ${tournamentId} da API:`, error);
      console.log('🎭 Usando dados mock como fallback');
      return getMockLargeTournament(tournamentId);
    }
  }

  // Inscreve usuário em um torneio
  async joinTournament(tournamentId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`🎯 Tentando inscrever no torneio ${tournamentId}...`);
      
      const response = await apiService.post(`/tournaments/${tournamentId}/join`);

      if (response.success) {
        console.log(`✅ Inscrito no torneio ${tournamentId} com sucesso`);
        return {
          success: true,
          message: 'Inscrição realizada com sucesso!'
        };
      } else {
        console.warn('⚠️ Erro na inscrição:', response.error);
        return {
          success: false,
          message: response.error || 'Erro na inscrição'
        };
      }
    } catch (error) {
      console.error(`❌ Erro ao se inscrever no torneio ${tournamentId}:`, error);
      return {
        success: false,
        message: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Remove inscrição de um torneio
  async leaveTournament(tournamentId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`🚪 Tentando sair do torneio ${tournamentId}...`);
      
      const response = await apiService.delete(`/tournaments/${tournamentId}/leave`);

      if (response.success) {
        console.log(`✅ Saiu do torneio ${tournamentId} com sucesso`);
        return {
          success: true,
          message: 'Inscrição cancelada com sucesso!'
        };
      } else {
        console.warn('⚠️ Erro ao cancelar inscrição:', response.error);
        return {
          success: false,
          message: response.error || 'Erro ao cancelar inscrição'
        };
      }
    } catch (error) {
      console.error(`❌ Erro ao sair do torneio ${tournamentId}:`, error);
      return {
        success: false,
        message: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Obtém torneios do usuário atual
  async getUserTournaments(): Promise<Tournament[]> {
    try {
      console.log('🌐 Buscando torneios do usuário...');
      
      const response = await apiService.get<Tournament[]>('/tournaments/my-tournaments');

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} torneios do usuário carregados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao buscar torneios do usuário');
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar torneios do usuário:', error);
      return [];
    }
  }

  // Cria um novo torneio (admin)
  async createTournament(tournamentData: Partial<Tournament>): Promise<{ success: boolean; data?: Tournament; error?: string }> {
    try {
      console.log('🏗️ Criando novo torneio...');
      
      const response = await apiService.post<Tournament>('/tournaments', tournamentData);

      if (response.success && response.data) {
        console.log(`✅ Torneio ${response.data.id} criado com sucesso`);
        return {
          success: true,
          data: response.data
        };
      } else {
        console.warn('⚠️ Erro ao criar torneio:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao criar torneio'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao criar torneio:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Atualiza um torneio (admin)
  async updateTournament(tournamentId: string, tournamentData: Partial<Tournament>): Promise<{ success: boolean; data?: Tournament; error?: string }> {
    try {
      console.log(`🔄 Atualizando torneio ${tournamentId}...`);
      
      const response = await apiService.put<Tournament>(`/tournaments/${tournamentId}`, tournamentData);

      if (response.success && response.data) {
        console.log(`✅ Torneio ${tournamentId} atualizado com sucesso`);
        return {
          success: true,
          data: response.data
        };
      } else {
        console.warn('⚠️ Erro ao atualizar torneio:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao atualizar torneio'
        };
      }
    } catch (error) {
      console.error(`❌ Erro ao atualizar torneio ${tournamentId}:`, error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Deleta um torneio (admin)
  async deleteTournament(tournamentId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`🗑️ Deletando torneio ${tournamentId}...`);
      
      const response = await apiService.delete(`/tournaments/${tournamentId}`);

      if (response.success) {
        console.log(`✅ Torneio ${tournamentId} deletado com sucesso`);
        return {
          success: true,
          message: 'Torneio deletado com sucesso!'
        };
      } else {
        console.warn('⚠️ Erro ao deletar torneio:', response.error);
        return {
          success: false,
          message: response.error || 'Erro ao deletar torneio'
        };
      }
    } catch (error) {
      console.error(`❌ Erro ao deletar torneio ${tournamentId}:`, error);
      return {
        success: false,
        message: 'Erro de conexão. Tente novamente.'
      };
    }
  }
}

// Instância singleton
export const realTournamentService = new RealTournamentService();

// Exporta as funções principais para compatibilidade
export const getTournaments = realTournamentService.getTournaments.bind(realTournamentService);
export const getTournamentById = realTournamentService.getTournamentById.bind(realTournamentService);

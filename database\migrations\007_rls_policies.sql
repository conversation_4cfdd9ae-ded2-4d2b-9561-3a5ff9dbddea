-- Migration 007: Row Level Security (RLS) Policies
-- Created: 2024-01-01
-- Description: Enables RLS and creates security policies for all tables

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE games ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_disputes ENABLE ROW LEVEL SECURITY;
ALTER TABLE matchmaking_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE tournaments ENABLE ROW LEVEL SECURITY;
ALTER TABLE tournament_seasons ENABLE ROW LEVEL SECURITY;
ALTER TABLE tournament_brackets ENABLE ROW LEVEL SECURITY;
ALTER TABLE tournament_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tournament_match_schedule ENABLE ROW LEVEL SECURITY;
ALTER TABLE tournament_prizes ENABLE ROW LEVEL SECURITY;
ALTER TABLE tournament_announcements ENABLE ROW LEVEL SECURITY;
ALTER TABLE friendships ENABLE ROW LEVEL SECURITY;
ALTER TABLE clubs ENABLE ROW LEVEL SECURITY;
ALTER TABLE club_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE club_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE private_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE private_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE club_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE friend_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE affiliate_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE commission_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE affiliate_payouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE global_rankings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_season_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE streams ENABLE ROW LEVEL SECURITY;
ALTER TABLE stream_viewers ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Public user profiles are viewable" ON users
    FOR SELECT USING (status = 'active');

-- User profiles policies
CREATE POLICY "Users can view their own profile details" ON user_profiles
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Public profiles are viewable" ON user_profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = user_profiles.user_id 
            AND users.status = 'active'
        )
    );

-- User stats policies
CREATE POLICY "Users can view their own stats" ON user_stats
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Public stats are viewable" ON user_stats
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = user_stats.user_id 
            AND users.status = 'active'
        )
    );

-- Wallet policies
CREATE POLICY "Users can only access their own wallet" ON wallets
    FOR ALL USING (auth.uid() = user_id);

-- Transaction policies
CREATE POLICY "Users can only view their own transactions" ON transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own transactions" ON transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Games policies (public read access)
CREATE POLICY "Games are publicly viewable" ON games
    FOR SELECT USING (is_active = true);

-- Game rooms policies
CREATE POLICY "Public rooms are viewable" ON game_rooms
    FOR SELECT USING (is_public = true OR auth.uid() = host_user_id);

CREATE POLICY "Users can create rooms" ON game_rooms
    FOR INSERT WITH CHECK (auth.uid() = host_user_id);

CREATE POLICY "Room hosts can update their rooms" ON game_rooms
    FOR UPDATE USING (auth.uid() = host_user_id);

CREATE POLICY "Room hosts can delete their rooms" ON game_rooms
    FOR DELETE USING (auth.uid() = host_user_id);

-- Room participants policies
CREATE POLICY "Room participants can view room members" ON room_participants
    FOR SELECT USING (
        auth.uid() = user_id OR 
        EXISTS (
            SELECT 1 FROM game_rooms 
            WHERE game_rooms.id = room_participants.room_id 
            AND (game_rooms.is_public = true OR game_rooms.host_user_id = auth.uid())
        )
    );

CREATE POLICY "Users can join rooms" ON room_participants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave rooms" ON room_participants
    FOR UPDATE USING (auth.uid() = user_id);

-- Matches policies
CREATE POLICY "Match participants can view their matches" ON matches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM match_participants 
            WHERE match_participants.match_id = matches.id 
            AND match_participants.user_id = auth.uid()
        )
    );

CREATE POLICY "Public tournament matches are viewable" ON matches
    FOR SELECT USING (
        tournament_id IS NOT NULL AND 
        EXISTS (
            SELECT 1 FROM tournaments 
            WHERE tournaments.id = matches.tournament_id 
            AND tournaments.is_public = true
        )
    );

-- Match participants policies
CREATE POLICY "Match participants can view match details" ON match_participants
    FOR SELECT USING (
        auth.uid() = user_id OR 
        EXISTS (
            SELECT 1 FROM match_participants mp2 
            WHERE mp2.match_id = match_participants.match_id 
            AND mp2.user_id = auth.uid()
        )
    );

-- Friendships policies
CREATE POLICY "Users can view their friendships" ON friendships
    FOR SELECT USING (
        auth.uid() = requester_user_id OR 
        auth.uid() = addressee_user_id
    );

CREATE POLICY "Users can create friend requests" ON friendships
    FOR INSERT WITH CHECK (auth.uid() = requester_user_id);

CREATE POLICY "Users can respond to friend requests" ON friendships
    FOR UPDATE USING (auth.uid() = addressee_user_id);

-- Clubs policies
CREATE POLICY "Public clubs are viewable" ON clubs
    FOR SELECT USING (privacy_type = 'public');

CREATE POLICY "Club members can view their clubs" ON clubs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM club_members 
            WHERE club_members.club_id = clubs.id 
            AND club_members.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create clubs" ON clubs
    FOR INSERT WITH CHECK (auth.uid() = owner_user_id);

CREATE POLICY "Club owners can update their clubs" ON clubs
    FOR UPDATE USING (auth.uid() = owner_user_id);

-- Club members policies
CREATE POLICY "Club members can view club membership" ON club_members
    FOR SELECT USING (
        auth.uid() = user_id OR 
        EXISTS (
            SELECT 1 FROM club_members cm2 
            WHERE cm2.club_id = club_members.club_id 
            AND cm2.user_id = auth.uid()
        )
    );

-- Private conversations policies
CREATE POLICY "Users can view their conversations" ON private_conversations
    FOR ALL USING (
        auth.uid() = participant1_user_id OR 
        auth.uid() = participant2_user_id
    );

-- Private messages policies
CREATE POLICY "Users can view their private messages" ON private_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM private_conversations 
            WHERE private_conversations.id = private_messages.conversation_id 
            AND (
                private_conversations.participant1_user_id = auth.uid() OR 
                private_conversations.participant2_user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can send private messages" ON private_messages
    FOR INSERT WITH CHECK (auth.uid() = sender_user_id);

-- Notifications policies
CREATE POLICY "Users can only view their own notifications" ON notifications
    FOR ALL USING (auth.uid() = user_id);

-- Affiliate program policies
CREATE POLICY "Users can view their own affiliate data" ON affiliate_programs
    FOR ALL USING (auth.uid() = user_id);

-- User referrals policies
CREATE POLICY "Referrers can view their referrals" ON user_referrals
    FOR SELECT USING (auth.uid() = referrer_user_id);

-- Commission transactions policies
CREATE POLICY "Affiliates can view their commissions" ON commission_transactions
    FOR SELECT USING (auth.uid() = affiliate_user_id);

-- User achievements policies
CREATE POLICY "Users can view their own achievements" ON user_achievements
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Public achievements are viewable" ON user_achievements
    FOR SELECT USING (is_unlocked = true);

-- Global rankings policies (public read access)
CREATE POLICY "Rankings are publicly viewable" ON global_rankings
    FOR SELECT USING (true);

-- User season stats policies
CREATE POLICY "Users can view their own season stats" ON user_season_stats
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Public season stats are viewable" ON user_season_stats
    FOR SELECT USING (true);

-- Streams policies
CREATE POLICY "Public streams are viewable" ON streams
    FOR SELECT USING (true);

CREATE POLICY "Streamers can manage their streams" ON streams
    FOR ALL USING (auth.uid() = streamer_user_id);

-- User activity logs policies
CREATE POLICY "Users can view their own activity logs" ON user_activity_logs
    FOR SELECT USING (auth.uid() = user_id);

import React from 'react';
import { LucideIcon } from 'lucide-react';

interface AchievementCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  progress: number;
  isCompleted?: boolean;
}

export default function AchievementCard({ 
  icon: Icon, 
  title, 
  description, 
  progress, 
  isCompleted = false 
}: AchievementCardProps) {
  return (
    <div className="bg-gray-800 rounded-xl p-4">
      <div className="flex items-start gap-3 mb-3">
        <div className={`p-2 rounded-lg ${isCompleted ? 'bg-yellow-400' : 'bg-gray-700'}`}>
          <Icon className={`w-6 h-6 ${isCompleted ? 'text-black' : 'text-yellow-400'}`} />
        </div>
        <div>
          <h3 className="text-white font-semibold">{title}</h3>
          <p className="text-gray-400 text-sm">{description}</p>
        </div>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-2">
        <div 
          className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
      </div>
    </div>
  );
}
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyFix() {
  console.log('🔧 Aplicando correção do trigger de wallet...\n');

  try {
    // Executar comandos SQL diretamente
    console.log('1. Removendo trigger existente...');
    
    // Comando 1: Drop existing trigger
    try {
      const { error: dropError } = await supabase.rpc('exec_sql', { 
        sql: 'DROP TRIGGER IF EXISTS update_wallet_balance_trigger ON transactions' 
      });
      if (dropError) {
        console.error('❌ Erro ao remover trigger:', dropError);
      } else {
        console.log('✅ Trigger antigo removido');
      }
    } catch (e) {
      console.log('⚠️ Trigger antigo não existia ou erro ao remover');
    }

    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('2. Criando nova função...');
    
    // Comando 2: Create new function
    const newFunction = `
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle UPDATE operations (status change from non-completed to completed)
    IF TG_OP = 'UPDATE' THEN
        IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
            IF NEW.type IN ('deposit', 'win', 'commission', 'refund') THEN
                -- Add to balance
                UPDATE wallets 
                SET balance = balance + NEW.amount,
                    updated_at = NOW()
                WHERE user_id = NEW.user_id;
                
                -- Update total deposited for deposits
                IF NEW.type = 'deposit' THEN
                    UPDATE wallets 
                    SET total_deposited = total_deposited + NEW.amount
                    WHERE user_id = NEW.user_id;
                END IF;
                
            ELSIF NEW.type IN ('withdrawal', 'bet') THEN
                -- Subtract from balance
                UPDATE wallets 
                SET balance = balance - NEW.amount,
                    updated_at = NOW()
                WHERE user_id = NEW.user_id;
                
                -- Update total withdrawn for withdrawals
                IF NEW.type = 'withdrawal' THEN
                    UPDATE wallets 
                    SET total_withdrawn = total_withdrawn + NEW.amount
                    WHERE user_id = NEW.user_id;
                END IF;
            END IF;
        END IF;
        RETURN NEW;
    END IF;
    
    -- Handle INSERT operations (transaction created directly with 'completed' status)
    IF TG_OP = 'INSERT' THEN
        IF NEW.status = 'completed' THEN
            IF NEW.type IN ('deposit', 'win', 'commission', 'refund') THEN
                -- Add to balance
                UPDATE wallets 
                SET balance = balance + NEW.amount,
                    updated_at = NOW()
                WHERE user_id = NEW.user_id;
                
                -- Update total deposited for deposits
                IF NEW.type = 'deposit' THEN
                    UPDATE wallets 
                    SET total_deposited = total_deposited + NEW.amount
                    WHERE user_id = NEW.user_id;
                END IF;
                
            ELSIF NEW.type IN ('withdrawal', 'bet') THEN
                -- Subtract from balance
                UPDATE wallets 
                SET balance = balance - NEW.amount,
                    updated_at = NOW()
                WHERE user_id = NEW.user_id;
                
                -- Update total withdrawn for withdrawals
                IF NEW.type = 'withdrawal' THEN
                    UPDATE wallets 
                    SET total_withdrawn = total_withdrawn + NEW.amount
                    WHERE user_id = NEW.user_id;
                END IF;
            END IF;
        END IF;
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER`;

    const { error: funcError } = await supabase.rpc('exec_sql', { sql: newFunction });
    if (funcError) {
      console.error('❌ Erro ao criar função:', funcError);
      return;
    } else {
      console.log('✅ Nova função criada');
    }

    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('3. Criando triggers...');
    
    // Comando 3: Create INSERT trigger
    const insertTrigger = `
CREATE TRIGGER update_wallet_balance_insert_trigger
    AFTER INSERT ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_wallet_balance()`;

    const { error: insertError } = await supabase.rpc('exec_sql', { sql: insertTrigger });
    if (insertError) {
      console.error('❌ Erro ao criar trigger INSERT:', insertError);
    } else {
      console.log('✅ Trigger INSERT criado');
    }

    await new Promise(resolve => setTimeout(resolve, 1000));

    // Comando 4: Create UPDATE trigger
    const updateTrigger = `
CREATE TRIGGER update_wallet_balance_update_trigger
    AFTER UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_wallet_balance()`;

    const { error: updateError } = await supabase.rpc('exec_sql', { sql: updateTrigger });
    if (updateError) {
      console.error('❌ Erro ao criar trigger UPDATE:', updateError);
    } else {
      console.log('✅ Trigger UPDATE criado');
    }

    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('\n🧪 Testando a correção...');
    
    // Testar a correção
    const { data: testUser, error: userError } = await supabase
      .from('users')
      .select('id, username')
      .limit(1)
      .single();

    if (userError || !testUser) {
      console.error('❌ Erro ao buscar usuário para teste:', userError);
      return;
    }

    console.log(`👤 Testando com usuário: ${testUser.username}`);
    
    // Buscar wallet
    const { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .select('id, balance')
      .eq('user_id', testUser.id)
      .single();

    if (walletError || !wallet) {
      console.error('❌ Erro ao buscar wallet:', walletError);
      return;
    }

    console.log(`💰 Saldo inicial: R$ ${wallet.balance}`);

    // Teste: Criar transação diretamente com status completed
    const testAmount = 12.50;
    const { data: directTransaction, error: directError } = await supabase
      .from('transactions')
      .insert({
        user_id: testUser.id,
        wallet_id: wallet.id,
        type: 'win',
        amount: testAmount,
        description: 'Teste correção - transação direta completed',
        status: 'completed'
      })
      .select()
      .single();

    if (directError) {
      console.error('❌ Erro ao criar transação direta:', directError);
    } else {
      console.log(`📝 Transação direta criada: ${directTransaction.id}`);
      
      // Aguardar trigger processar
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Verificar saldo
      const { data: walletAfterDirect, error: walletDirectError } = await supabase
        .from('wallets')
        .select('balance, updated_at')
        .eq('user_id', testUser.id)
        .single();

      if (walletDirectError) {
        console.error('❌ Erro ao verificar saldo após transação direta:', walletDirectError);
      } else {
        const expectedBalance = wallet.balance + testAmount;
        console.log(`💰 Saldo após transação direta: R$ ${walletAfterDirect.balance}`);
        console.log(`💰 Saldo esperado: R$ ${expectedBalance}`);
        console.log(`🕐 Wallet atualizada: ${walletAfterDirect.updated_at}`);
        
        if (Math.abs(walletAfterDirect.balance - expectedBalance) < 0.01) {
          console.log('🎉 CORREÇÃO APLICADA COM SUCESSO!');
          console.log('✅ Transações de prêmio agora funcionam corretamente!');
        } else {
          console.log('❌ CORREÇÃO FALHOU: Transação direta não atualizou o saldo');
        }
      }
      
      // Limpar transação de teste
      await supabase.from('transactions').delete().eq('id', directTransaction.id);
      
      // Reverter saldo
      await supabase
        .from('wallets')
        .update({ balance: wallet.balance })
        .eq('user_id', testUser.id);
      
      console.log('🔄 Transação de teste removida e saldo revertido');
    }

  } catch (error) {
    console.error('❌ Erro durante aplicação da correção:', error);
  }
}

// Executar correção
applyFix()
  .then(() => {
    console.log('\n✅ Processo de correção concluído!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });

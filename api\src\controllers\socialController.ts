import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, FriendRequestData, ClubCreationData, ChallengeData, MessageData } from '../types/api';
import { logger, logBusinessEvent, logSecurityEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';

/**
 * Get user's friends list
 */
export const getFriends = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);

    const offset = (page - 1) * limit;

    const { data: friendships, error, count } = await supabaseAdmin
      .from('friendships')
      .select(`
        id, created_at,
        requester:users!friendships_requester_user_id_fkey (
          id, username, display_name, avatar_url, country
        ),
        addressee:users!friendships_addressee_user_id_fkey (
          id, username, display_name, avatar_url, country
        )
      `, { count: 'exact' })
      .or(`requester_user_id.eq.${user.id},addressee_user_id.eq.${user.id}`)
      .eq('status', 'accepted')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get friends:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve friends list'
      };
      res.status(500).json(response);
      return;
    }

    // Format friends list to show the other user
    const friends = (friendships || []).map((friendship: any) => {
      const friend = friendship.requester.id === user.id ? friendship.addressee : friendship.requester;
      return {
        id: friendship.id,
        friend,
        friendship_date: friendship.created_at
      };
    });

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        friends
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get friends error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Send friend request
 */
export const sendFriendRequest = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { addressee_user_id }: FriendRequestData = req.body;

    // Check if trying to add themselves
    if (addressee_user_id === user.id) {
      const response: ApiResponse = {
        success: false,
        error: 'Cannot send friend request to yourself'
      };
      res.status(400).json(response);
      return;
    }

    // Check if target user exists
    const { data: targetUser, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, username, status')
      .eq('id', addressee_user_id)
      .eq('status', 'active')
      .single();

    if (userError || !targetUser) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if friendship already exists
    const { data: existingFriendship } = await supabaseAdmin
      .from('friendships')
      .select('id, status')
      .or(`and(requester_user_id.eq.${user.id},addressee_user_id.eq.${addressee_user_id}),and(requester_user_id.eq.${addressee_user_id},addressee_user_id.eq.${user.id})`)
      .single();

    if (existingFriendship) {
      let message = 'Friend request already exists';
      if (existingFriendship.status === 'accepted') {
        message = 'You are already friends with this user';
      } else if (existingFriendship.status === 'declined') {
        message = 'Friend request was previously declined';
      }

      const response: ApiResponse = {
        success: false,
        error: message
      };
      res.status(400).json(response);
      return;
    }

    // Create friend request
    const { data: friendRequest, error: requestError } = await supabaseAdmin
      .from('friendships')
      .insert({
        requester_user_id: user.id,
        addressee_user_id: addressee_user_id
      })
      .select('*')
      .single();

    if (requestError || !friendRequest) {
      logger.error('Failed to create friend request:', requestError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to send friend request'
      };
      res.status(500).json(response);
      return;
    }

    // Create notification for target user
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id: addressee_user_id,
        type: 'friend_request',
        title: 'Nova solicitação de amizade',
        message: `${user.username} enviou uma solicitação de amizade`,
        data: {
          friendship_id: friendRequest.id,
          requester_user_id: user.id,
          requester_username: user.username
        }
      });

    logBusinessEvent('friend_request_sent', user.id, {
      addressee_user_id,
      friendship_id: friendRequest.id
    });

    const response: ApiResponse = {
      success: true,
      message: 'Friend request sent successfully',
      data: {
        friendship_id: friendRequest.id
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Send friend request error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Accept friend request
 */
export const acceptFriendRequest = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { requestId } = req.params;

    // Get friend request
    const { data: friendship, error: friendshipError } = await supabaseAdmin
      .from('friendships')
      .select(`
        id, requester_user_id, addressee_user_id, status,
        requester:users!friendships_requester_user_id_fkey (username)
      `)
      .eq('id', requestId)
      .single();

    if (friendshipError || !friendship) {
      const response: ApiResponse = {
        success: false,
        error: 'Friend request not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if user is the addressee
    if (friendship.addressee_user_id !== user.id) {
      const response: ApiResponse = {
        success: false,
        error: 'You can only accept friend requests sent to you'
      };
      res.status(403).json(response);
      return;
    }

    // Check if request is still pending
    if (friendship.status !== 'pending') {
      const response: ApiResponse = {
        success: false,
        error: 'Friend request is no longer pending'
      };
      res.status(400).json(response);
      return;
    }

    // Accept friend request
    const { error: updateError } = await supabaseAdmin
      .from('friendships')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString()
      })
      .eq('id', requestId);

    if (updateError) {
      logger.error('Failed to accept friend request:', updateError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to accept friend request'
      };
      res.status(500).json(response);
      return;
    }

    // Create notification for requester
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id: friendship.requester_user_id,
        type: 'friend_request',
        title: 'Solicitação de amizade aceita',
        message: `${user.username} aceitou sua solicitação de amizade`,
        data: {
          friendship_id: requestId,
          accepter_user_id: user.id,
          accepter_username: user.username
        }
      });

    logBusinessEvent('friend_request_accepted', user.id, {
      requester_user_id: friendship.requester_user_id,
      friendship_id: requestId
    });

    const response: ApiResponse = {
      success: true,
      message: 'Friend request accepted successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Accept friend request error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Decline friend request
 */
export const declineFriendRequest = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { requestId } = req.params;

    // Get friend request
    const { data: friendship, error: friendshipError } = await supabaseAdmin
      .from('friendships')
      .select('id, requester_user_id, addressee_user_id, status')
      .eq('id', requestId)
      .single();

    if (friendshipError || !friendship) {
      const response: ApiResponse = {
        success: false,
        error: 'Friend request not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if user is the addressee
    if (friendship.addressee_user_id !== user.id) {
      const response: ApiResponse = {
        success: false,
        error: 'You can only decline friend requests sent to you'
      };
      res.status(403).json(response);
      return;
    }

    // Check if request is still pending
    if (friendship.status !== 'pending') {
      const response: ApiResponse = {
        success: false,
        error: 'Friend request is no longer pending'
      };
      res.status(400).json(response);
      return;
    }

    // Decline friend request
    const { error: updateError } = await supabaseAdmin
      .from('friendships')
      .update({
        status: 'declined',
        declined_at: new Date().toISOString()
      })
      .eq('id', requestId);

    if (updateError) {
      logger.error('Failed to decline friend request:', updateError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to decline friend request'
      };
      res.status(500).json(response);
      return;
    }

    logBusinessEvent('friend_request_declined', user.id, {
      requester_user_id: friendship.requester_user_id,
      friendship_id: requestId
    });

    const response: ApiResponse = {
      success: true,
      message: 'Friend request declined successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Decline friend request error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Remove friend
 */
export const removeFriend = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { friendId } = req.params;

    // Find friendship
    const { data: friendship, error: friendshipError } = await supabaseAdmin
      .from('friendships')
      .select('id, requester_user_id, addressee_user_id, status')
      .or(`and(requester_user_id.eq.${user.id},addressee_user_id.eq.${friendId}),and(requester_user_id.eq.${friendId},addressee_user_id.eq.${user.id})`)
      .eq('status', 'accepted')
      .single();

    if (friendshipError || !friendship) {
      const response: ApiResponse = {
        success: false,
        error: 'Friendship not found'
      };
      res.status(404).json(response);
      return;
    }

    // Remove friendship
    const { error: deleteError } = await supabaseAdmin
      .from('friendships')
      .delete()
      .eq('id', friendship.id);

    if (deleteError) {
      logger.error('Failed to remove friend:', deleteError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to remove friend'
      };
      res.status(500).json(response);
      return;
    }

    logBusinessEvent('friend_removed', user.id, {
      removed_friend_id: friendId,
      friendship_id: friendship.id
    });

    const response: ApiResponse = {
      success: true,
      message: 'Friend removed successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Remove friend error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get pending friend requests
 */
export const getFriendRequests = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);

    const offset = (page - 1) * limit;

    const { data: requests, error, count } = await supabaseAdmin
      .from('friendships')
      .select(`
        id, created_at,
        requester:users!friendships_requester_user_id_fkey (
          id, username, display_name, avatar_url, country
        )
      `, { count: 'exact' })
      .eq('addressee_user_id', user.id)
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get friend requests:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve friend requests'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        requests: requests || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get friend requests error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get friend suggestions
 */
export const getFriendSuggestions = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);

    // Get users who are not friends and have similar interests
    const offset = (page - 1) * limit;

    const { data: suggestions, error, count } = await supabaseAdmin
      .from('users')
      .select(`
        id, username, display_name, avatar_url, country,
        user_stats (ranking_points, total_matches, win_rate)
      `, { count: 'exact' })
      .neq('id', user.id)
      .eq('status', 'active')
      .not('id', 'in', `(
        SELECT CASE
          WHEN requester_user_id = '${user.id}' THEN addressee_user_id
          ELSE requester_user_id
        END
        FROM friendships
        WHERE (requester_user_id = '${user.id}' OR addressee_user_id = '${user.id}')
        AND status IN ('accepted', 'pending')
      )`)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Failed to get friend suggestions:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve friend suggestions'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        suggestions: suggestions || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get friend suggestions error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Challenge friend to match
 */
export const challengeFriend = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const challengeData: ChallengeData = req.body;

    // Check if users are friends
    const { data: friendship } = await supabaseAdmin
      .from('friendships')
      .select('id')
      .or(`and(requester_user_id.eq.${user.id},addressee_user_id.eq.${challengeData.challenged_user_id}),and(requester_user_id.eq.${challengeData.challenged_user_id},addressee_user_id.eq.${user.id})`)
      .eq('status', 'accepted')
      .single();

    if (!friendship) {
      const response: ApiResponse = {
        success: false,
        error: 'You can only challenge friends'
      };
      res.status(400).json(response);
      return;
    }

    // Check if user has sufficient balance
    if (challengeData.bet_amount > 0) {
      const { data: wallet } = await supabaseAdmin
        .from('wallets')
        .select('balance, frozen_balance')
        .eq('user_id', user.id)
        .single();

      if (!wallet || (wallet.balance - wallet.frozen_balance) < challengeData.bet_amount) {
        const response: ApiResponse = {
          success: false,
          error: 'Insufficient balance'
        };
        res.status(400).json(response);
        return;
      }
    }

    // Create challenge
    const { data: challenge, error: challengeError } = await supabaseAdmin
      .from('challenges')
      .insert({
        challenger_user_id: user.id,
        challenged_user_id: challengeData.challenged_user_id,
        game_id: challengeData.game_id,
        bet_amount: challengeData.bet_amount,
        game_settings: challengeData.game_settings || {},
        message: challengeData.message
      })
      .select('*')
      .single();

    if (challengeError || !challenge) {
      logger.error('Failed to create challenge:', challengeError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create challenge'
      };
      res.status(500).json(response);
      return;
    }

    // Create notification
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id: challengeData.challenged_user_id,
        type: 'challenge',
        title: 'Novo desafio recebido',
        message: `${user.username} te desafiou para uma partida`,
        data: {
          challenge_id: challenge.id,
          challenger_user_id: user.id,
          challenger_username: user.username,
          bet_amount: challengeData.bet_amount
        }
      });

    logBusinessEvent('friend_challenged', user.id, {
      challenged_user_id: challengeData.challenged_user_id,
      challenge_id: challenge.id,
      bet_amount: challengeData.bet_amount
    });

    const response: ApiResponse = {
      success: true,
      message: 'Challenge sent successfully',
      data: {
        challenge: {
          id: challenge.id,
          bet_amount: challenge.bet_amount,
          status: challenge.status,
          created_at: challenge.created_at
        }
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Challenge friend error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get clubs
 */
export const getClubs = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { page, limit } = extractPagination(req.query);
    const { privacy_type, game_id, region } = req.query;

    let query = supabaseAdmin
      .from('clubs')
      .select(`
        id, name, description, privacy_type, max_members, current_members,
        region, tags, created_at,
        games (id, name, slug, icon_url),
        users (username, display_name, avatar_url)
      `, { count: 'exact' });

    // Apply filters
    if (privacy_type) {
      query = query.eq('privacy_type', privacy_type);
    } else {
      // Default to public clubs only
      query = query.eq('privacy_type', 'public');
    }

    if (game_id) {
      query = query.eq('game_id', game_id);
    }
    if (region) {
      query = query.eq('region', region);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: clubs, error, count } = await query;

    if (error) {
      logger.error('Failed to get clubs:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve clubs'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        clubs: clubs || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get clubs error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

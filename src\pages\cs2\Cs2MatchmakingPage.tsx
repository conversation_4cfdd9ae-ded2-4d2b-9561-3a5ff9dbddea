import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Trophy,
  Users,
  Timer,
  Settings,
  Gamepad2,
  Shield,
  X,
  Copy,
  MessageCircle,
  Wallet,
  AlertCircle,
  Check,
  Target,
  Shield as ShieldIcon,
  Loader2
} from 'lucide-react';
import { realMatchService } from '../../services/realMatchService';

interface MatchSettings {
  format: '1v1' | '5v5';
  map: 'dust2' | 'mirage' | 'aim_map';
  rounds: 15 | 30;
  roundTime: 115;
  startMoney: 800 | 16000;
  allowedWeapons: {
    awp: boolean;
  };
  armor: boolean;
  grenades: {
    flashbang: boolean;
  };
  overtime: boolean;
  betAmount: number;
}

interface Player {
  id: string;
  name: string;
  avatar: string;
  platform: string;
  platformId: string;
  rating: number;
  status: 'searching' | 'ready' | 'offline' | 'waiting_payment' | 'paid';
}

interface Message {
  id: string;
  playerId: string;
  text: string;
  timestamp: Date;
}

const MAPS = {
  dust2: 'Dust 2',
  mirage: 'Mirage',
  aim_map: 'Aim Map'
};

export default function Cs2MatchmakingPage() {
  const navigate = useNavigate();
  const [matchSettings, setMatchSettings] = useState<MatchSettings>({
    format: '1v1',
    map: 'dust2',
    rounds: 15,
    roundTime: 115,
    startMoney: 800,
    allowedWeapons: {
      awp: true
    },
    armor: true,
    grenades: {
      flashbang: true
    },
    overtime: true,
    betAmount: 10
  });

  const [isMatchmaking, setIsMatchmaking] = useState(false);
  const [foundMatch, setFoundMatch] = useState(false);
  const [opponent, setOpponent] = useState<Player | null>(null);
  const [showChat, setShowChat] = useState(false);
  const [searchTime, setSearchTime] = useState(0);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [currentPlayer, setCurrentPlayer] = useState<Player>(() => {
    // Usar nome do cache ou fallback
    const cachedName = localStorage.getItem('playerName') || 'Jogador';
    return {
      id: '1',
      name: cachedName,
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      platform: 'steam',
      platformId: `${cachedName.toLowerCase().replace(/\s+/g, '_')}_steam`,
      rating: 1200,
      status: 'ready'
    };
  });

  const handleStartMatchmaking = async () => {
    try {
      setIsMatchmaking(true);
      setCurrentPlayer(prev => ({ ...prev, status: 'searching' }));

      console.log('🔄 Iniciando matchmaking CS2...');

      // Tentar usar API real primeiro
      const matchmakingRequest = {
        game_id: 'cs2',
        bet_amount: matchSettings.betAmount,
        preferred_rating_range: {
          min: currentPlayer.rating - 200,
          max: currentPlayer.rating + 200
        },
        game_settings: {
          format: matchSettings.format,
          map: matchSettings.map,
          rounds: matchSettings.rounds,
          startMoney: matchSettings.startMoney,
          allowedWeapons: matchSettings.allowedWeapons,
          armor: matchSettings.armor,
          grenades: matchSettings.grenades,
          overtime: matchSettings.overtime
        }
      };

      const result = await realMatchService.startMatchmaking(matchmakingRequest);

      if (result.success) {
        console.log('✅ Matchmaking iniciado via API:', result.matchmaking_id);
        // Simular encontrar oponente após criar match
        setTimeout(() => {
          const simulatedOpponent: Player = {
            id: '2',
            name: 'Ana Silva',
            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            platform: 'steam',
            platformId: 'ana_silva_steam',
            rating: 1250,
            status: 'waiting_payment'
          };
          setOpponent(simulatedOpponent);
          setFoundMatch(true);
          setCurrentPlayer(prev => ({ ...prev, status: 'waiting_payment' }));
        }, 3000);
      } else {
        throw new Error('Falha ao criar match');
      }

    } catch (error) {
      console.error('❌ Erro no matchmaking, usando simulação:', error);

      // Fallback para simulação
      setTimeout(() => {
        const simulatedOpponent: Player = {
          id: '2',
          name: 'Ana Silva',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          platform: 'steam',
          platformId: 'ana_silva_steam',
          rating: 1250,
          status: 'waiting_payment'
        };
        setOpponent(simulatedOpponent);
        setFoundMatch(true);
        setCurrentPlayer(prev => ({ ...prev, status: 'waiting_payment' }));
      }, 5000);
    }
  };

  const handlePayment = (playerId: string) => {
    if (playerId === currentPlayer.id) {
      setCurrentPlayer(prev => ({ ...prev, status: 'paid' }));
    } else if (opponent) {
      setOpponent(prev => prev ? { ...prev, status: 'paid' } : null);
    }
  };

  const handleSendMessage = (text: string) => {
    const newMsg: Message = {
      id: Date.now().toString(),
      playerId: currentPlayer.id,
      text,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newMsg]);
    setNewMessage('');

    // Simulate opponent response
    if (text.toLowerCase().includes('oi') || text.toLowerCase().includes('olá')) {
      setTimeout(() => {
        const response: Message = {
          id: (Date.now() + 1).toString(),
          playerId: opponent?.id || '2',
          text: 'Oi! Pronto para jogar?',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, response]);
      }, 1500);
    }
  };

  const handleCopyPlatformId = (id: string) => {
    navigator.clipboard.writeText(id);
  };

  // Update search time
  useEffect(() => {
    let interval: number;
    if (isMatchmaking && !foundMatch) {
      interval = setInterval(() => {
        setSearchTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isMatchmaking, foundMatch]);

  // Check if both players have paid
  useEffect(() => {
    if (currentPlayer.status === 'paid' && opponent?.status === 'paid') {
      // Both players have paid, show game instructions
      const systemMsg: Message = {
        id: Date.now().toString(),
        playerId: 'system',
        text: 'Ambos os jogadores pagaram. Vocês podem adicionar um ao outro e começar a partida!',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, systemMsg]);
    }
  }, [currentPlayer.status, opponent?.status]);

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-2xl mx-auto space-y-6">
        {!isMatchmaking && (
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">Counter-Strike 2</h1>
              <p className="text-gray-400">Encontre oponentes e jogue partidas 1v1</p>
            </div>
            <button
              onClick={() => navigate('/external-games/cs2')}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        )}

        {!isMatchmaking ? (
          <>
            {/* Match Settings */}
            <div className="bg-gray-800 rounded-xl p-6 space-y-6">
              <div className="flex items-center gap-2">
                <Settings className="w-5 h-5 text-rose-400" />
                <h2 className="text-white font-bold">Configurações da Partida</h2>
              </div>

              {/* Format */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Formato</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, format: '1v1' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.format === '1v1'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    1v1 (aim map)
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, format: '5v5' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.format === '5v5'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    5v5
                  </button>
                </div>
              </div>

              {/* Map Selection */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Mapa</label>
                <div className="grid grid-cols-3 gap-2">
                  {Object.entries(MAPS).map(([key, name]) => (
                    <button
                      key={key}
                      onClick={() => setMatchSettings(prev => ({ ...prev, map: key as MatchSettings['map'] }))}
                      className={`p-3 rounded-lg text-center ${
                        matchSettings.map === key
                          ? 'bg-rose-400 text-white'
                          : 'bg-gray-700 text-gray-300'
                      }`}
                    >
                      {name}
                    </button>
                  ))}
                </div>
              </div>

              {/* Rounds */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Rounds</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, rounds: 15 }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.rounds === 15
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    MR15 (Melhor de 30)
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, rounds: 30 }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.rounds === 30
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    MR30 (Melhor de 60)
                  </button>
                </div>
              </div>

              {/* Start Money */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Dinheiro Inicial</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, startMoney: 800 }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.startMoney === 800
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    $800
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, startMoney: 16000 }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.startMoney === 16000
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    $16000
                  </button>
                </div>
              </div>

              {/* Allowed Weapons */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">AWP</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({
                      ...prev,
                      allowedWeapons: { ...prev.allowedWeapons, awp: true }
                    }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.allowedWeapons.awp
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Permitida
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({
                      ...prev,
                      allowedWeapons: { ...prev.allowedWeapons, awp: false }
                    }))}
                    className={`p-3 rounded-lg text-center ${
                      !matchSettings.allowedWeapons.awp
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Proibida
                  </button>
                </div>
              </div>

              {/* Armor */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Colete/Capacete</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, armor: true }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.armor
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Permitido
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, armor: false }))}
                    className={`p-3 rounded-lg text-center ${
                      !matchSettings.armor
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Proibido
                  </button>
                </div>
              </div>

              {/* Grenades */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Flashbang</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({
                      ...prev,
                      grenades: { ...prev.grenades, flashbang: true }
                    }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.grenades.flashbang
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Permitida
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({
                      ...prev,
                      grenades: { ...prev.grenades, flashbang: false }
                    }))}
                    className={`p-3 rounded-lg text-center ${
                      !matchSettings.grenades.flashbang
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Proibida
                  </button>
                </div>
              </div>

              {/* Overtime */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Overtime</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, overtime: true }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.overtime
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Ativado
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, overtime: false }))}
                    className={`p-3 rounded-lg text-center ${
                      !matchSettings.overtime
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Desativado
                  </button>
                </div>
              </div>

              {/* Bet Amount */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Valor da Aposta</label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">R$</span>
                  <input
                    type="number"
                    min="10"
                    step="5"
                    value={matchSettings.betAmount}
                    onChange={(e) => setMatchSettings(prev => ({ ...prev, betAmount: Number(e.target.value) }))}
                    className="w-full bg-gray-700 text-white rounded-lg py-3 pl-10 pr-3 focus:outline-none focus:ring-2 focus:ring-rose-400"
                  />
                </div>
              </div>
            </div>

            {/* Start Button */}
            <button
              onClick={handleStartMatchmaking}
              className="w-full bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold py-4 rounded-xl flex items-center justify-center gap-2"
            >
              <Users className="w-5 h-5" />
              Encontrar Oponente
            </button>
          </>
        ) : foundMatch ? (
          <div className="bg-gray-800 rounded-xl p-6 space-y-6">
            <div className="text-center">
              <Trophy className="w-12 h-12 text-rose-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-2">Oponente Encontrado!</h2>
              <p className="text-gray-400">Faça o depósito para iniciar a partida</p>
            </div>

            <div className="grid grid-cols-2 gap-8">
              {/* Current Player */}
              <div className="text-center space-y-3">
                <img
                  src={currentPlayer.avatar}
                  alt={currentPlayer.name}
                  className="w-20 h-20 rounded-full mx-auto"
                />
                <div>
                  <h3 className="text-white font-bold">{currentPlayer.name}</h3>
                  <p className="text-gray-400 text-sm">Rating: {currentPlayer.rating}</p>
                  <div className={`mt-2 text-sm ${
                    currentPlayer.status === 'paid' ? 'text-green-400' : 'text-yellow-400'
                  }`}>
                    {currentPlayer.status === 'paid' ? 'Pago' : 'Aguardando Pagamento'}
                  </div>
                </div>
                <div className="space-y-2">
                  <button
                    onClick={() => handleCopyPlatformId(currentPlayer.platformId)}
                    className="w-full bg-gray-700 text-white rounded-lg py-2 px-3 flex items-center justify-center gap-2 hover:bg-gray-600"
                  >
                    <Copy className="w-4 h-4" />
                    {currentPlayer.platformId}
                  </button>
                  {currentPlayer.status === 'waiting_payment' && (
                    <button
                      onClick={() => handlePayment(currentPlayer.id)}
                      className="w-full bg-gradient-to-r from-rose-400 to-pink-400 text-white rounded-lg py-2 px-3 flex items-center justify-center gap-2"
                    >
                      <Wallet className="w-4 h-4" />
                      Pagar R$ {matchSettings.betAmount}
                    </button>
                  )}
                </div>
              </div>

              {/* Opponent */}
              {opponent && (
                <div className="text-center space-y-3">
                  <img
                    src={opponent.avatar}
                    alt={opponent.name}
                    className="w-20 h-20 rounded-full mx-auto"
                  />
                  <div>
                    <h3 className="text-white font-bold">{opponent.name}</h3>
                    <p className="text-gray-400 text-sm">Rating: {opponent.rating}</p>
                    <div className={`mt-2 text-sm ${
                      opponent.status === 'paid' ? 'text-green-400' : 'text-yellow-400'
                    }`}>
                      {opponent.status === 'paid' ? 'Pago' : 'Aguardando Pagamento'}
                    </div>
                  </div>
                  <button
                    onClick={() => handleCopyPlatformId(opponent.platformId)}
                    className="w-full bg-gray-700 text-white rounded-lg py-2 px-3 flex items-center justify-center gap-2 hover:bg-gray-600"
                  >
                    <Copy className="w-4 h-4" />
                    {opponent.platformId}
                  </button>
                </div>
              )}
            </div>

            {/* Match Info */}
            <div className="bg-gray-700 rounded-lg p-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Formato:</span>
                <span className="text-white">{matchSettings.format}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Mapa:</span>
                <span className="text-white">{MAPS[matchSettings.map]}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Rounds:</span>
                <span className="text-white">MR{matchSettings.rounds}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Dinheiro Inicial:</span>
                <span className="text-white">${matchSettings.startMoney}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">AWP:</span>
                <span className="text-white">
                  {matchSettings.allowedWeapons.awp ? 'Permitida' : 'Proibida'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Colete/Capacete:</span>
                <span className="text-white">
                  {matchSettings.armor ? 'Permitido' : 'Proibido'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Flashbang:</span>
                <span className="text-white">
                  {matchSettings.grenades.flashbang ? 'Permitida' : 'Proibida'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Overtime:</span>
                <span className="text-white">
                  {matchSettings.overtime ? 'Ativado' : 'Desativado'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Valor da Aposta:</span>
                <span className="text-rose-400 font-bold">R$ {matchSettings.betAmount}</span>
              </div>
            </div>

            {/* Chat Section */}
            {showChat ? (
              <div className="space-y-4">
                <div className="h-60 bg-gray-700 rounded-lg p-4 overflow-y-auto space-y-3">
                  {messages.map((msg) => (
                    <div
                      key={msg.id}
                      className={`flex ${msg.playerId === currentPlayer.id ? 'justify-end' : 'justify-start'}`}
                    >
                      {msg.playerId === 'system' ? (
                        <div className="bg-gray-600/50 text-gray-300 text-sm py-2 px-4 rounded-lg max-w-[80%] flex items-center gap-2">
                          <AlertCircle className="w-4 h-4 text-yellow-400" />
                          {msg.text}
                        </div>
                      ) : (
                        <div className={`${
                          msg.playerId === currentPlayer.id
                            ? 'bg-rose-400 text-white'
                            : 'bg-gray-600 text-gray-200'
                        } py-2 px-4 rounded-lg max-w-[80%]`}>
                          {msg.text}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Digite sua mensagem..."
                    className="flex-1 bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-rose-400"
                  />
                  <button
                    onClick={() => handleSendMessage(newMessage)}
                    disabled={!newMessage.trim()}
                    className="bg-rose-400 text-white px-4 rounded-lg disabled:opacity-50"
                  >
                    <MessageCircle className="w-5 h-5" />
                  </button>
                </div>
                <button
                  onClick={() => setShowChat(false)}
                  className="w-full bg-gray-700 text-white py-2 rounded-lg"
                >
                  Fechar Chat
                </button>
              </div>
            ) : (
              <button
                onClick={() => setShowChat(true)}
                className="w-full bg-gray-700 text-white rounded-lg py-3 flex items-center justify-center gap-2 hover:bg-gray-600"
              >
                <MessageCircle className="w-5 h-5" />
                Abrir Chat
              </button>
            )}

            {currentPlayer.status === 'paid' && opponent?.status === 'paid' && (
              <div className="bg-green-400/20 rounded-lg p-4 flex items-start gap-3">
                <Check className="w-5 h-5 text-green-400 flex-shrink-0 mt-1" />
                <div>
                  <p className="text-green-400 font-semibold">Pronto para Jogar!</p>
                  <p className="text-gray-300 text-sm">
                    1. Adicione seu oponente usando o ID da plataforma<br />
                    2. Crie uma partida privada com as configurações combinadas<br />
                    3. Após a partida, envie um print do resultado
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-gray-800 rounded-xl p-6">
            <div className="text-center space-y-6">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-rose-400 border-t-transparent mx-auto" />

              <div>
                <h2 className="text-2xl font-bold text-white mb-2">Procurando Partida</h2>
                <p className="text-gray-400">Aguarde enquanto encontramos o melhor oponente</p>
              </div>

              <div className="flex items-center justify-center gap-6 text-gray-400">
                <div className="flex items-center gap-2">
                  <Timer className="w-5 h-5" />
                  <span>{Math.floor(searchTime / 60)}:{(searchTime % 60).toString().padStart(2, '0')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  <span>Rating: {currentPlayer.rating}</span>
                </div>
              </div>

              <button
                onClick={() => {
                  setIsMatchmaking(false);
                  navigate('/external-games/cs2');
                }}
                className="bg-gray-700 text-white px-6 py-3 rounded-xl font-semibold flex items-center gap-2 mx-auto hover:bg-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
                Cancelar Busca
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
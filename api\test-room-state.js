const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testRoomState() {
  try {
    console.log('🧪 Testando estado da sala...');

    // Buscar a sala Teste2
    const { data: rooms, error: roomsError } = await supabase
      .from('game_rooms')
      .select('*')
      .eq('name', 'Teste2');

    if (roomsError || !rooms || rooms.length === 0) {
      console.log('❌ Sala "Teste2" não encontrada');
      return;
    }

    const room = rooms[0];
    console.log('📋 Estado atual da sala:');
    console.log(`  Status: ${room.status}`);
    console.log(`  Iniciada em: ${room.started_at || 'Não iniciada'}`);

    // Resetar estado da sala para teste
    console.log('\n🔄 Resetando estado da sala para "waiting"...');

    const { error: resetError } = await supabase
      .from('game_rooms')
      .update({
        status: 'waiting',
        started_at: null,
        finished_at: null
      })
      .eq('id', room.id);

    if (resetError) {
      console.error('❌ Erro ao resetar sala:', resetError);
      return;
    }

    // Resetar status de pronto dos participantes
    const { error: resetReadyError } = await supabase
      .from('room_participants')
      .update({ is_ready: false })
      .eq('room_id', room.id);

    if (resetReadyError) {
      console.error('❌ Erro ao resetar status de pronto:', resetReadyError);
      return;
    }

    // Limpar mensagens de chat antigas
    const { error: clearChatError } = await supabase
      .from('room_chat_messages')
      .delete()
      .eq('room_id', room.id);

    if (clearChatError) {
      console.warn('⚠️ Erro ao limpar chat:', clearChatError);
    } else {
      console.log('🧹 Chat limpo');
    }

    // Remover matches antigos
    const { error: clearMatchesError } = await supabase
      .from('matches')
      .delete()
      .eq('room_id', room.id);

    if (clearMatchesError) {
      console.warn('⚠️ Erro ao limpar matches:', clearMatchesError);
    } else {
      console.log('🧹 Matches limpos');
    }

    console.log('✅ Sala resetada com sucesso!');
    console.log('📝 Agora você pode testar:');
    console.log('  1. Faça logout e login com matt2');
    console.log('  2. Entre na sala Teste2');
    console.log('  3. Faça logout e login com matt3');
    console.log('  4. Entre na sala Teste2');
    console.log('  5. Clique em "Ficar Pronto" com ambos');
    console.log('  6. O countdown deve iniciar automaticamente');
    console.log('  7. A partida deve ser criada no banco');
    console.log('  8. Se um usuário sair e voltar, deve ver o estado correto');
    console.log('');
    console.log('🔧 Correções implementadas:');
    console.log('  - WebSocket força reconexão a cada entrada na sala');
    console.log('  - Token sempre atualizado para o usuário correto');
    console.log('  - Estado da partida persistido no banco de dados');

  } catch (error) {
    console.error('❌ Erro:', error);
  }
}

testRoomState();

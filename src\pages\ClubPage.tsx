import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  Users,
  Trophy,
  Settings,
  Plus,
  MessageSquare,
  Calendar,
  Star,
  ChevronRight,
  UserPlus,
  Shield,
  Bell,
  Home,
  Award,
  Clock,
  ArrowLeft,
  Share2,
  Info
} from 'lucide-react';

interface Member {
  id: string;
  name: string;
  avatar: string;
  role: 'owner' | 'admin' | 'member';
  status: 'online' | 'offline' | 'playing';
  game?: string;
  joinDate: string;
}

interface ClubDetails {
  id: string;
  name: string;
  logo: string;
  game: string;
  region: string;
  members: number;
  level: number;
  xp: number;
  maxXp: number;
  isPublic: boolean;
  description?: string;
  tags?: string[];
  createdAt: string;
  userRole?: 'owner' | 'admin' | 'member';
}

interface Match {
  id: string;
  opponent: string;
  opponentLogo: string;
  result: 'victory' | 'defeat';
  score: string;
  date: string;
}

export default function ClubPage() {
  const { clubId } = useParams();
  const [activeTab, setActiveTab] = useState<'feed' | 'lobbies' | 'members' | 'matches' | 'events' | 'achievements'>('feed');
  const [showInviteModal, setShowInviteModal] = useState(false);

  // Mock data
  const club: ClubDetails = {
    id: clubId || '1',
    name: 'CS Masters Brasil',
    logo: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    game: 'Counter-Strike 2',
    region: 'América do Sul',
    members: 250,
    level: 42,
    xp: 8500,
    maxXp: 10000,
    isPublic: true,
    description: 'Clube competitivo de CS2 com foco em torneios nacionais e treinamento de alto nível para jogadores dedicados.',
    tags: ['Competitivo', 'Torneios', 'Treinos diários'],
    createdAt: '2023-01-01',
    userRole: 'admin'
  };

  const members: Member[] = [
    {
      id: '1',
      name: 'Ana Silva',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      role: 'owner',
      status: 'online',
      joinDate: '2023-01-15'
    },
    {
      id: '2',
      name: 'Lucas Oliveira',
      avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      role: 'admin',
      status: 'playing',
      game: 'Counter-Strike 2',
      joinDate: '2023-02-20'
    }
  ];

  const matches: Match[] = [
    {
      id: '1',
      opponent: 'Furia Academy',
      opponentLogo: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      result: 'victory',
      score: '16-12',
      date: '2024-03-15'
    },
    {
      id: '2',
      opponent: 'Imperial E-sports',
      opponentLogo: 'https://images.unsplash.com/photo-1594122230689-45899d9e6f69?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      result: 'defeat',
      score: '13-16',
      date: '2024-03-10'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <div className="mb-4">
        <Link
          to="/social"
          className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Voltar para Social</span>
        </Link>
      </div>

      {/* Club Header */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden">
        {/* Cover Image/Banner */}
        <div className="h-32 md:h-48 bg-gradient-to-r from-indigo-500/30 via-purple-500/30 to-pink-500/30 relative">
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900/80 to-transparent"></div>

          {/* Club Actions */}
          <div className="absolute top-4 right-4 flex gap-2">
            <button className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-full p-2 text-gray-300 hover:text-white transition-colors">
              <Share2 className="w-5 h-5" />
            </button>
            {club.userRole === 'owner' || club.userRole === 'admin' ? (
              <button className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-full p-2 text-gray-300 hover:text-white transition-colors">
                <Settings className="w-5 h-5" />
              </button>
            ) : (
              <button className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-full p-2 text-gray-300 hover:text-white transition-colors">
                <Bell className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        <div className="p-6">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Club Logo and Basic Info */}
            <div className="flex items-start gap-4">
              <div className="relative -mt-16">
                <img
                  src={club.logo}
                  alt={club.name}
                  className="w-24 h-24 rounded-xl object-cover border-4 border-gray-800 shadow-lg"
                />
                {club.userRole && (
                  <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full p-1">
                    {club.userRole === 'owner' && <Shield className="w-4 h-4 text-white" />}
                    {club.userRole === 'admin' && <Star className="w-4 h-4 text-white" />}
                    {club.userRole === 'member' && <Users className="w-4 h-4 text-white" />}
                  </div>
                )}
              </div>

              <div className="flex-1">
                <h1 className="text-2xl font-bold text-white">{club.name}</h1>
                <div className="flex items-center gap-2 text-gray-400 mt-1">
                  <span>{club.game}</span>
                  <span>•</span>
                  <span>{club.region}</span>
                </div>

                {club.tags && club.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {club.tags.map((tag, index) => (
                      <span key={index} className="text-xs bg-gray-700/50 text-gray-300 px-2 py-0.5 rounded-full">
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Club Stats */}
            <div className="flex flex-wrap gap-4 mt-4 md:mt-0 md:ml-auto">
              <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-lg px-4 py-2 flex items-center gap-2">
                <Users className="w-5 h-5 text-indigo-400" />
                <div>
                  <span className="text-white font-medium">{club.members}</span>
                  <span className="text-gray-400 text-sm ml-1">membros</span>
                </div>
              </div>

              <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-lg px-4 py-2 flex items-center gap-2">
                <Trophy className="w-5 h-5 text-indigo-400" />
                <div>
                  <span className="text-white font-medium">Nível {club.level}</span>
                </div>
              </div>

              <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-lg px-4 py-2 flex items-center gap-2">
                <Clock className="w-5 h-5 text-indigo-400" />
                <div>
                  <span className="text-white font-medium">Criado em</span>
                  <span className="text-gray-400 text-sm ml-1">{new Date(club.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Club Description */}
          {club.description && (
            <div className="mt-4 text-gray-300 text-sm">
              {club.description}
            </div>
          )}

          {/* XP Progress Bar */}
          <div className="mt-6">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="text-gray-400">Progresso para Nível {club.level + 1}</span>
              <span className="text-indigo-400">{club.xp}/{club.maxXp} XP</span>
            </div>
            <div className="w-full bg-gray-700/50 backdrop-blur-sm rounded-full h-2">
              <div
                className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 h-2 rounded-full transition-all"
                style={{ width: `${(club.xp / club.maxXp) * 100}%` }}
              />
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex flex-wrap gap-3 mt-6">
            <button className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-3 px-6 rounded-xl flex items-center justify-center gap-2 hover:opacity-90 transition-opacity shadow-lg">
              <Plus className="w-5 h-5" />
              Criar Lobby
            </button>
            <button className="bg-gray-800/70 backdrop-blur-sm border border-white/10 text-white font-bold py-3 px-6 rounded-xl flex items-center justify-center gap-2 hover:bg-gray-700/50 transition-colors">
              <MessageSquare className="w-5 h-5" />
              Chat do Clube
            </button>
            <button className="bg-gray-800/70 backdrop-blur-sm border border-white/10 text-white font-bold py-3 px-6 rounded-xl flex items-center justify-center gap-2 hover:bg-gray-700/50 transition-colors">
              <UserPlus className="w-5 h-5" />
              Convidar
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden">
        {/* Desktop Tabs */}
        <div className="hidden md:flex border-b border-gray-700/50">
          <button
            onClick={() => setActiveTab('feed')}
            className={`px-6 py-4 font-semibold transition-all ${
              activeTab === 'feed'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
            }`}
          >
            <div className="flex items-center gap-2">
              <Home className="w-4 h-4" />
              <span>Feed</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('lobbies')}
            className={`px-6 py-4 font-semibold transition-all ${
              activeTab === 'lobbies'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
            }`}
          >
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span>Lobbies</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('members')}
            className={`px-6 py-4 font-semibold transition-all ${
              activeTab === 'members'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
            }`}
          >
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span>Membros</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('matches')}
            className={`px-6 py-4 font-semibold transition-all ${
              activeTab === 'matches'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
            }`}
          >
            <div className="flex items-center gap-2">
              <Trophy className="w-4 h-4" />
              <span>Partidas</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('events')}
            className={`px-6 py-4 font-semibold transition-all ${
              activeTab === 'events'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
            }`}
          >
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              <span>Eventos</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('achievements')}
            className={`px-6 py-4 font-semibold transition-all ${
              activeTab === 'achievements'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
            }`}
          >
            <div className="flex items-center gap-2">
              <Award className="w-4 h-4" />
              <span>Conquistas</span>
            </div>
          </button>
        </div>

        {/* Mobile Tabs - Scrollable */}
        <div className="md:hidden overflow-x-auto flex border-b border-gray-700/50 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent">
          <button
            onClick={() => setActiveTab('feed')}
            className={`px-4 py-3 font-semibold whitespace-nowrap transition-all ${
              activeTab === 'feed'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Home className="w-4 h-4" />
              <span>Feed</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('lobbies')}
            className={`px-4 py-3 font-semibold whitespace-nowrap transition-all ${
              activeTab === 'lobbies'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Users className="w-4 h-4" />
              <span>Lobbies</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('members')}
            className={`px-4 py-3 font-semibold whitespace-nowrap transition-all ${
              activeTab === 'members'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Users className="w-4 h-4" />
              <span>Membros</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('matches')}
            className={`px-4 py-3 font-semibold whitespace-nowrap transition-all ${
              activeTab === 'matches'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Trophy className="w-4 h-4" />
              <span>Partidas</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('events')}
            className={`px-4 py-3 font-semibold whitespace-nowrap transition-all ${
              activeTab === 'events'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Calendar className="w-4 h-4" />
              <span>Eventos</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('achievements')}
            className={`px-4 py-3 font-semibold whitespace-nowrap transition-all ${
              activeTab === 'achievements'
                ? 'text-indigo-400 border-b-2 border-indigo-400 bg-indigo-500/5'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <div className="flex items-center gap-1.5">
              <Award className="w-4 h-4" />
              <span>Conquistas</span>
            </div>
          </button>
        </div>

        {/* Tab Content Container */}
        <div className="p-6">
          {/* Feed Tab Content */}
          {activeTab === 'feed' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-white font-bold text-lg">Feed de Atividades</h2>
              </div>
              <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
                <Home className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400">
                  O feed de atividades será implementado em breve.
                  <br />
                  Aqui você poderá ver as últimas atividades do clube.
                </p>
              </div>
            </div>
          )}

      {/* Content */}
          {/* Lobbies Tab Content */}
          {activeTab === 'lobbies' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-white font-bold text-lg">Lobbies Ativos</h2>
                <button className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity shadow-lg flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Criar Lobby
                </button>
              </div>
              <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
                <Calendar className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400">
                  Nenhum lobby ativo no momento.
                  <br />
                  Crie um novo lobby para jogar com outros membros!
                </p>
              </div>
            </div>
          )}

          {/* Members Tab Content */}
          {activeTab === 'members' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-white font-bold text-lg">Membros ({members.length})</h2>
                <button
                  onClick={() => setShowInviteModal(true)}
                  className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity shadow-lg flex items-center gap-2"
                >
                  <UserPlus className="w-4 h-4" />
                  Convidar
                </button>
              </div>
              <div className="space-y-2">
                {members.map(member => (
                  <div
                    key={member.id}
                    className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl p-4 flex items-center justify-between hover:bg-gray-700/50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <img
                          src={member.avatar}
                          alt={member.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                        <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-gray-800 ${
                          member.status === 'online' ? 'bg-green-500' :
                          member.status === 'playing' ? 'bg-indigo-500' :
                          'bg-gray-500'
                        }`} />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-white font-semibold">{member.name}</h3>
                          {member.role === 'owner' && (
                            <Shield className="w-4 h-4 text-indigo-400" />
                          )}
                          {member.role === 'admin' && (
                            <Star className="w-4 h-4 text-indigo-400" />
                          )}
                        </div>
                        <p className="text-sm text-gray-400">
                          {member.status === 'playing'
                            ? `Jogando ${member.game}`
                            : `Entrou em ${new Date(member.joinDate).toLocaleDateString()}`
                          }
                        </p>
                      </div>
                    </div>
                    <button className="text-gray-400 hover:text-white">
                      <ChevronRight className="w-5 h-5" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Matches Tab Content */}
          {activeTab === 'matches' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-white font-bold text-lg">Histórico de Partidas</h2>
                <button className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity shadow-lg flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  Agendar Partida
                </button>
              </div>
              <div className="space-y-3">
                {matches.map(match => (
                  <div
                    key={match.id}
                    className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl p-4 hover:bg-gray-700/50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <img
                          src={match.opponentLogo}
                          alt={match.opponent}
                          className="w-10 h-10 rounded-lg object-cover"
                        />
                        <div>
                          <h3 className="text-white font-semibold">{match.opponent}</h3>
                          <p className="text-sm text-gray-400">{new Date(match.date).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-lg font-bold ${
                          match.result === 'victory' ? 'text-green-500' : 'text-red-500'
                        }`}>
                          {match.score}
                        </p>
                        <p className="text-sm text-gray-400">
                          {match.result === 'victory' ? 'Vitória' : 'Derrota'}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Events Tab Content */}
          {activeTab === 'events' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-white font-bold text-lg">Eventos</h2>
                <button className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity shadow-lg flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Criar Evento
                </button>
              </div>
              <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
                <Calendar className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400">
                  Nenhum evento agendado no momento.
                  <br />
                  Crie um novo evento para reunir os membros do clube!
                </p>
              </div>
            </div>
          )}

          {/* Achievements Tab Content */}
          {activeTab === 'achievements' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-white font-bold text-lg">Conquistas</h2>
              </div>
              <div className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
                <Award className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400">
                  Nenhuma conquista desbloqueada ainda.
                  <br />
                  Participe de torneios e eventos para ganhar conquistas!
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
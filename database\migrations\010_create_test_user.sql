-- Migration 010: Create Test User
-- Created: 2024-01-01
-- Description: Creates a test user for login testing

-- Insert test user with hashed password
-- Password: "Milhao123#" hashed with bcrypt rounds 12
INSERT INTO users (
    id,
    email, 
    username, 
    display_name, 
    password_hash,
    status,
    timezone,
    language,
    created_at,
    updated_at
) VALUES (
    '550e8400-e29b-41d4-a716-446655440999',
    '<EMAIL>',
    'testuser',
    'Test User',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBMY9f5zIg9S2i', -- Password: Milhao123#
    'active',
    'America/Sao_Paulo',
    'pt-BR',
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Create user profile for test user
INSERT INTO user_profiles (
    user_id,
    bio,
    gaming_experience,
    preferred_game_modes,
    privacy_settings,
    notification_preferences
) VALUES (
    '550e8400-e29b-41d4-a716-446655440999',
    'Test user for platform testing',
    'intermediate',
    ARRAY['1v1', 'tournament'],
    '{"profile_visibility": "public", "show_stats": true, "show_friends": true}',
    '{"email_notifications": true, "push_notifications": true, "match_reminders": true}'
) ON CONFLICT (user_id) DO NOTHING;

-- Create user stats for test user
INSERT INTO user_stats (
    user_id,
    total_matches,
    total_wins,
    total_losses,
    total_draws,
    win_rate,
    total_earnings,
    total_spent,
    current_streak,
    best_streak,
    ranking_points,
    level,
    experience_points,
    achievements_count
) VALUES (
    '550e8400-e29b-41d4-a716-446655440999',
    0,
    0,
    0,
    0,
    0.00,
    0.00,
    0.00,
    0,
    0,
    1000,
    1,
    0,
    0
) ON CONFLICT (user_id) DO NOTHING;

-- Create wallet for test user
INSERT INTO wallets (
    user_id,
    balance,
    total_deposited,
    total_withdrawn,
    total_earned,
    total_spent,
    status
) VALUES (
    '550e8400-e29b-41d4-a716-446655440999',
    100.00,
    100.00,
    0.00,
    0.00,
    0.00,
    'active'
) ON CONFLICT (user_id) DO NOTHING;

-- Add some initial transaction for the test user
INSERT INTO transactions (
    user_id,
    type,
    amount,
    status,
    description,
    metadata
) VALUES (
    '550e8400-e29b-41d4-a716-446655440999',
    'deposit',
    100.00,
    'completed',
    'Initial test deposit',
    '{"method": "test", "test_user": true}'
) ON CONFLICT DO NOTHING;

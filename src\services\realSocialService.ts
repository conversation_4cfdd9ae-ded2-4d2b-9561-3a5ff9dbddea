import { apiService } from './apiService';

// Tipos para funcionalidades sociais
export interface Friend {
  id: string;
  username: string;
  display_name: string;
  avatar_url?: string;
  status: 'online' | 'offline' | 'playing';
  game?: string;
  last_active?: string;
  mutual_friends?: number;
}

export interface FriendRequest {
  id: string;
  from_user_id: string;
  to_user_id: string;
  from_user: {
    id: string;
    username: string;
    display_name: string;
    avatar_url?: string;
  };
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  mutual_friends?: number;
}

export interface Club {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  game?: string;
  region?: string;
  members_count: number;
  max_members: number;
  level: number;
  xp: number;
  is_public: boolean;
  tags?: string[];
  created_at: string;
  user_role?: 'owner' | 'admin' | 'moderator' | 'member';
}

export interface ClubMember {
  id: string;
  user_id: string;
  club_id: string;
  role: 'owner' | 'admin' | 'moderator' | 'member';
  joined_at: string;
  user: {
    id: string;
    username: string;
    display_name: string;
    avatar_url?: string;
    status: 'online' | 'offline' | 'playing';
  };
}

export interface ChatMessage {
  id: string;
  user_id: string;
  content: string;
  type: 'text' | 'image' | 'system';
  created_at: string;
  user: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

class RealSocialService {
  
  // ===== AMIGOS =====
  
  // Obtém lista de amigos
  async getFriends(): Promise<Friend[]> {
    try {
      console.log('👥 Buscando lista de amigos...');
      
      const response = await apiService.get<Friend[]>('/social/friends');
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} amigos carregados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar amigos:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar amigos:', error);
      return [];
    }
  }

  // Obtém solicitações de amizade
  async getFriendRequests(): Promise<FriendRequest[]> {
    try {
      console.log('📨 Buscando solicitações de amizade...');
      
      const response = await apiService.get<FriendRequest[]>('/social/friend-requests');
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} solicitações encontradas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar solicitações:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar solicitações:', error);
      return [];
    }
  }

  // Envia solicitação de amizade
  async sendFriendRequest(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🤝 Enviando solicitação de amizade para ${userId}...`);
      
      const response = await apiService.post(`/social/friends/${userId}/request`);
      
      if (response.success) {
        console.log(`✅ Solicitação enviada para ${userId}`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao enviar solicitação:', response.error);
        return { success: false, error: response.error || 'Erro ao enviar solicitação' };
      }
    } catch (error) {
      console.error('❌ Erro ao enviar solicitação:', error);
      return { success: false, error: 'Erro de conexão. Tente novamente.' };
    }
  }

  // Aceita solicitação de amizade
  async acceptFriendRequest(requestId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`✅ Aceitando solicitação ${requestId}...`);
      
      const response = await apiService.post(`/social/friend-requests/${requestId}/accept`);
      
      if (response.success) {
        console.log(`✅ Solicitação ${requestId} aceita`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao aceitar solicitação:', response.error);
        return { success: false, error: response.error || 'Erro ao aceitar solicitação' };
      }
    } catch (error) {
      console.error('❌ Erro ao aceitar solicitação:', error);
      return { success: false, error: 'Erro de conexão. Tente novamente.' };
    }
  }

  // Rejeita solicitação de amizade
  async rejectFriendRequest(requestId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`❌ Rejeitando solicitação ${requestId}...`);
      
      const response = await apiService.post(`/social/friend-requests/${requestId}/reject`);
      
      if (response.success) {
        console.log(`✅ Solicitação ${requestId} rejeitada`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao rejeitar solicitação:', response.error);
        return { success: false, error: response.error || 'Erro ao rejeitar solicitação' };
      }
    } catch (error) {
      console.error('❌ Erro ao rejeitar solicitação:', error);
      return { success: false, error: 'Erro de conexão. Tente novamente.' };
    }
  }

  // Remove amigo
  async removeFriend(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`💔 Removendo amigo ${userId}...`);
      
      const response = await apiService.delete(`/social/friends/${userId}`);
      
      if (response.success) {
        console.log(`✅ Amigo ${userId} removido`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao remover amigo:', response.error);
        return { success: false, error: response.error || 'Erro ao remover amigo' };
      }
    } catch (error) {
      console.error('❌ Erro ao remover amigo:', error);
      return { success: false, error: 'Erro de conexão. Tente novamente.' };
    }
  }

  // ===== CLUBES =====
  
  // Obtém lista de clubes
  async getClubs(page: number = 1, limit: number = 20, filters?: any): Promise<Club[]> {
    try {
      console.log(`🏛️ Buscando clubes (página ${page})...`);
      
      const params: any = { page, limit };
      if (filters) Object.assign(params, filters);
      
      const response = await apiService.get<Club[]>('/social/clubs', { params });
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} clubes carregados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar clubes:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar clubes:', error);
      return [];
    }
  }

  // Obtém clubes do usuário
  async getUserClubs(): Promise<Club[]> {
    try {
      console.log('🏛️ Buscando clubes do usuário...');
      
      const response = await apiService.get<Club[]>('/social/clubs/my-clubs');
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} clubes do usuário carregados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar clubes do usuário:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar clubes do usuário:', error);
      return [];
    }
  }

  // Obtém detalhes de um clube
  async getClubById(clubId: string): Promise<Club | null> {
    try {
      console.log(`🔍 Buscando detalhes do clube ${clubId}...`);
      
      const response = await apiService.get<Club>(`/social/clubs/${clubId}`);
      
      if (response.success && response.data) {
        console.log(`✅ Clube ${clubId} carregado`);
        return response.data;
      } else {
        console.warn(`⚠️ Erro ao carregar clube ${clubId}:`, response.error);
        return null;
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar clube ${clubId}:`, error);
      return null;
    }
  }

  // Obtém membros de um clube
  async getClubMembers(clubId: string): Promise<ClubMember[]> {
    try {
      console.log(`👥 Buscando membros do clube ${clubId}...`);
      
      const response = await apiService.get<ClubMember[]>(`/social/clubs/${clubId}/members`);
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} membros carregados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar membros:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar membros:', error);
      return [];
    }
  }

  // Entra em um clube
  async joinClub(clubId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🚪 Entrando no clube ${clubId}...`);
      
      const response = await apiService.post(`/social/clubs/${clubId}/join`);
      
      if (response.success) {
        console.log(`✅ Entrou no clube ${clubId}`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao entrar no clube:', response.error);
        return { success: false, error: response.error || 'Erro ao entrar no clube' };
      }
    } catch (error) {
      console.error('❌ Erro ao entrar no clube:', error);
      return { success: false, error: 'Erro de conexão. Tente novamente.' };
    }
  }

  // Sai de um clube
  async leaveClub(clubId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🚪 Saindo do clube ${clubId}...`);
      
      const response = await apiService.post(`/social/clubs/${clubId}/leave`);
      
      if (response.success) {
        console.log(`✅ Saiu do clube ${clubId}`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao sair do clube:', response.error);
        return { success: false, error: response.error || 'Erro ao sair do clube' };
      }
    } catch (error) {
      console.error('❌ Erro ao sair do clube:', error);
      return { success: false, error: 'Erro de conexão. Tente novamente.' };
    }
  }

  // Busca usuários
  async searchUsers(query: string): Promise<Friend[]> {
    try {
      console.log(`🔍 Buscando usuários: "${query}"...`);
      
      const response = await apiService.get<Friend[]>('/social/users/search', {
        params: { q: query }
      });
      
      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} usuários encontrados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao buscar usuários:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar usuários:', error);
      return [];
    }
  }
}

// Instância singleton
export const realSocialService = new RealSocialService();

// Funções de compatibilidade
export const getFriends = realSocialService.getFriends.bind(realSocialService);
export const getFriendRequests = realSocialService.getFriendRequests.bind(realSocialService);
export const getClubs = realSocialService.getClubs.bind(realSocialService);
export const getUserClubs = realSocialService.getUserClubs.bind(realSocialService);

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function recreateUser() {
  try {
    const email = '<EMAIL>';
    const username = 'matt';
    const password = 'Milhao123#';
    const userId = 'dfd3cd04-6f0f-4345-862b-5c11188dd7e2';
    
    console.log(`🔧 Recreating user: ${email}`);
    
    // First, let's try to delete related records that might prevent user deletion
    console.log('\n🗑️ Cleaning up related records...');
    
    // Delete user stats
    const { error: statsError } = await supabase
      .from('user_stats')
      .delete()
      .eq('user_id', userId);
    
    if (statsError) {
      console.log('⚠️ Could not delete user stats:', statsError.message);
    } else {
      console.log('✅ User stats deleted');
    }
    
    // Delete user profiles
    const { error: profileError } = await supabase
      .from('user_profiles')
      .delete()
      .eq('user_id', userId);
    
    if (profileError) {
      console.log('⚠️ Could not delete user profile:', profileError.message);
    } else {
      console.log('✅ User profile deleted');
    }
    
    // Delete wallet
    const { error: walletError } = await supabase
      .from('wallets')
      .delete()
      .eq('user_id', userId);
    
    if (walletError) {
      console.log('⚠️ Could not delete wallet:', walletError.message);
    } else {
      console.log('✅ Wallet deleted');
    }
    
    // Now delete the user
    console.log('\n🗑️ Deleting user...');
    const { error: deleteError } = await supabase
      .from('users')
      .delete()
      .eq('id', userId);
    
    if (deleteError) {
      console.log('⚠️ Could not delete user:', deleteError.message);
      console.log('Proceeding with password update instead...');
      
      // If we can't delete, try to update the password directly
      const hashedPassword = await bcrypt.hash(password, 12);
      
      // Try a direct update with all fields
      const { data: updateData, error: updateError } = await supabase
        .from('users')
        .update({ 
          password_hash: hashedPassword,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select('*');
      
      console.log('Update result:', { data: updateData, error: updateError });
      
    } else {
      console.log('✅ User deleted successfully');
      
      // Now create the user again with proper password
      console.log('\n👤 Creating user with password...');
      
      const hashedPassword = await bcrypt.hash(password, 12);
      
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          id: userId, // Use the same ID
          email: email,
          username: username,
          display_name: username,
          password_hash: hashedPassword,
          status: 'active',
          timezone: 'America/Sao_Paulo',
          language: 'pt-BR'
        })
        .select('*');
      
      if (createError) {
        console.error('❌ Failed to create user:', createError);
      } else {
        console.log('✅ User created successfully:', newUser[0]);
        
        // Create user profile
        const { error: profileCreateError } = await supabase
          .from('user_profiles')
          .insert({ user_id: userId });
        
        // Create user stats
        const { error: statsCreateError } = await supabase
          .from('user_stats')
          .insert({ user_id: userId });
        
        // Create wallet
        const { error: walletCreateError } = await supabase
          .from('wallets')
          .insert({ user_id: userId });
        
        console.log('Related records created:');
        console.log('- Profile:', profileCreateError ? 'FAILED' : 'SUCCESS');
        console.log('- Stats:', statsCreateError ? 'FAILED' : 'SUCCESS');
        console.log('- Wallet:', walletCreateError ? 'FAILED' : 'SUCCESS');
      }
    }
    
    // Verify the final result
    console.log('\n🔍 Verifying final result...');
    const { data: finalUser, error: finalError } = await supabase
      .from('users')
      .select('id, email, username, password_hash')
      .eq('email', email)
      .single();
    
    if (finalError) {
      console.error('❌ User not found after operation:', finalError);
    } else {
      console.log('✅ Final user state:');
      console.log('- ID:', finalUser.id);
      console.log('- Email:', finalUser.email);
      console.log('- Username:', finalUser.username);
      console.log('- Password Hash exists:', !!finalUser.password_hash);
      
      if (finalUser.password_hash) {
        const isValid = await bcrypt.compare(password, finalUser.password_hash);
        console.log('✅ Password verification:', isValid ? 'SUCCESS' : 'FAILED');
      }
    }
    
  } catch (error) {
    console.error('❌ Error recreating user:', error);
  }
}

recreateUser();

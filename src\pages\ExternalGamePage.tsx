import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import CreateRoomModal from '../components/CreateRoomModal';
import CreateSquadModal from '../components/CreateSquadModal';
import ShareSquadLinkModal from '../components/ShareSquadLinkModal';
import { realGameService, GameRoom } from '../services/realGameService';
import { Users, Plus, Crown, Timer, Settings, Wallet, Trophy, Gamepad2, Star, ChevronRight, Search, UserPlus, Share2, CheckCircle, X, UserCircle, MessageSquare, Target, Loader2, AlertTriangle, CreditCard, ArrowRight } from 'lucide-react';

interface Room {
  id: string;
  name: string;
  type: 'tournament' | '1v1';
  entryFee: number;
  prizePool: number;
  players: number;
  maxPlayers: number;
  mode: string;
  map: string;
  status: 'waiting' | 'starting' | 'playing';
  host: {
    name: string;
    avatar: string;
  };
}

interface Friend {
  id: string;
  name: string;
  avatar: string;
  status: 'online' | 'offline' | 'playing';
  game?: string;
  lastActive?: string;
}

interface Squad {
  id: string;
  name: string;
  members: Friend[];
  game: string;
  status: 'open' | 'playing';
  createdAt: string;
}

interface Club {
  id: string;
  name: string;
  logo: string;
  members: number;
  level: number;
  xp: number;
  maxXp: number;
}

export default function ExternalGamePage() {
  const { gameId } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'challenges' | 'squad'>('challenges');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showCreateSquadModal, setShowCreateSquadModal] = useState(false);
  const [showShareSquadLinkModal, setShowShareSquadLinkModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [linkCopied, setLinkCopied] = useState(false);
  const [selectedSquad, setSelectedSquad] = useState<Squad | null>(null);
  const [challengeFilter, setChallengeFilter] = useState<'all' | '1v1' | 'tournament'>('all');

  // Estados para notificações
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'warning';
    message: string;
  } | null>(null);

  // Estado para modal de saldo insuficiente
  const [showInsufficientBalanceModal, setShowInsufficientBalanceModal] = useState(false);
  const [requiredAmount, setRequiredAmount] = useState(0);

  // Estados para dados reais da API
  const [realGameRooms, setRealGameRooms] = useState<GameRoom[]>([]);
  const [realGame, setRealGame] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carregar jogo e salas reais da API
  useEffect(() => {
    const loadGameData = async () => {
      if (!gameId) return;

      try {
        setIsLoading(true);
        setError(null);

        // Primeiro, buscar o jogo real da API
        console.log(`🎮 Carregando jogo ${gameId}...`);
        const game = await realGameService.getGameById(gameId);

        if (game) {
          setRealGame(game);
          console.log(`✅ Jogo carregado: ${game.name}`);

          // Depois, carregar as salas deste jogo
          console.log(`🏠 Carregando salas do jogo ${gameId}...`);
          const rooms = await realGameService.getGameRooms(gameId);
          setRealGameRooms(rooms);
          console.log(`✅ ${rooms.length} salas carregadas`);
        } else {
          setError('Jogo não encontrado');
        }
      } catch (err) {
        console.error('❌ Erro ao carregar dados do jogo:', err);
        setError('Erro ao carregar dados do jogo');
      } finally {
        setIsLoading(false);
      }
    };

    loadGameData();
  }, [gameId]);

  const handleInviteFriend = () => {
    window.navigator.clipboard.writeText(`https://fplaygames.com/games/${gameId}?ref=invite`);
    setLinkCopied(true);
    setTimeout(() => setLinkCopied(false), 3000);
  };

  // Função para mostrar notificações
  const showNotification = (type: 'success' | 'error' | 'warning', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000); // Remove após 5 segundos
  };






  // Filtragem baseada na busca e tipo de desafio
  const filterRooms = <T extends { name: string; type?: '1v1' | 'tournament' }>(items: T[]): T[] => {
    let filteredItems = items;

    // Filtrar por tipo de desafio
    if (challengeFilter !== 'all') {
      filteredItems = filteredItems.filter(item => item.type === challengeFilter);
    }

    // Filtrar por busca
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filteredItems = filteredItems.filter(item => item.name.toLowerCase().includes(query));
    }

    return filteredItems;
  };

  // Converter GameRoom da API para o formato da interface Room
  const convertApiRoomToRoom = (apiRoom: GameRoom): Room => ({
    id: apiRoom.id,
    name: apiRoom.name,
    type: apiRoom.type === 'tournament' ? 'tournament' : '1v1',
    entryFee: apiRoom.entry_fee || 0,
    prizePool: (apiRoom.entry_fee || 0) * apiRoom.max_players,
    players: apiRoom.current_players,
    maxPlayers: apiRoom.max_players,
    mode: apiRoom.game_settings?.mode || 'Competitivo',
    map: apiRoom.game_settings?.map || 'Padrão',
    status: apiRoom.status === 'waiting' ? 'waiting' : 'playing',
    host: {
      name: apiRoom.users?.display_name || apiRoom.users?.username || 'Anônimo',
      avatar: apiRoom.users?.avatar_url || 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
    }
  });

  // Usar apenas dados reais da API - garantir que é um array
  const currentGameRooms = filterRooms(Array.isArray(realGameRooms) ? realGameRooms.map(convertApiRoomToRoom) : []);

  // Game data mapping
  const games = {
    '1': {
      id: '1',
      title: 'Counter-Strike 2 (CS2)',
      description: 'O FPS tático mais popular do mundo agora com gráficos e mecânicas aprimoradas',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      currentPlayers: 850000,
      rating: 4.8,
      genre: 'FPS Tático',
      releaseDate: '2023',
      publisher: 'Valve Corporation',
      features: ['Competitivo', 'Times', 'Ranqueado', 'Customização']
    },
    '2': {
      id: '2',
      title: 'FIFA 24',
      description: 'A mais recente edição do simulador de futebol mais popular do mundo',
      imageUrl: 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80',
      currentPlayers: 25000,
      rating: 4.7,
      genre: 'Esporte',
      releaseDate: '2023',
      publisher: 'EA Sports',
      features: ['Times', 'Competitivo', 'Ultimate Team', 'Carreira']
    },
    '3': {
      id: '3',
      title: 'Mobile Legends: Bang Bang',
      description: 'Arena de batalha móvel com heróis únicos e batalhas intensas',
      imageUrl: 'https://images.unsplash.com/photo-1579373903781-fd5c0c30c4cd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      currentPlayers: 15000,
      rating: 4.5,
      genre: 'MOBA',
      releaseDate: '2023',
      publisher: 'Moonton',
      features: ['Heróis', 'Competitivo', '5v5', 'Ranqueado']
    },
    '4': {
      id: '4',
      title: 'Call of Duty: Mobile',
      description: 'A experiência clássica de Call of Duty otimizada para dispositivos móveis',
      imageUrl: 'https://images.unsplash.com/photo-1509198397868-475647b2a1e5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1947&q=80',
      currentPlayers: 42000,
      rating: 4.6,
      genre: 'FPS',
      releaseDate: '2019',
      publisher: 'Activision',
      features: ['Battle Royale', 'Multiplayer', 'Clãs', 'Eventos']
    }
  };

  // Estados para dados reais de amigos e squads
  const [friends, setFriends] = useState<Friend[]>([]);
  const [squads, setSquads] = useState<Squad[]>([]);
  const [clubs, setClubs] = useState<Club[]>([]);

  // Carregar amigos e squads da API
  useEffect(() => {
    const loadSocialData = async () => {
      try {
        // Por enquanto, deixar vazio até implementar as APIs de social
        setFriends([]);
        setSquads([]);
        setClubs([]);
      } catch (error) {
        console.error('❌ Erro ao carregar dados sociais:', error);
      }
    };

    loadSocialData();
  }, []);

  const handleCreateRoom = async (roomData: any) => {
    console.log('Room created:', roomData);
    setShowCreateModal(false);

    try {
      // Conectar com a API real para criar a sala
      const newRoom = await realGameService.createGameRoom({
        game_id: gameId || '',
        name: roomData.name,
        type: roomData.type,
        entry_fee: roomData.entryFee,
        max_players: roomData.maxPlayers || 2,
        is_public: roomData.isPublic,
        password: roomData.password,
        game_settings: roomData.gameSettings || {}
      });

      if (newRoom) {
        console.log('✅ Sala criada com sucesso:', newRoom);
        showNotification('success', 'Sala criada com sucesso!');
        // Recarregar as salas para mostrar a nova sala
        const rooms = await realGameService.getGameRooms(gameId || '');
        setRealGameRooms(rooms);
        // Navegar para a sala criada
        navigate(`/games/${gameId}/rooms/${newRoom.id}`);
      } else {
        showNotification('error', 'Erro ao criar sala. Tente novamente.');
      }
    } catch (error: any) {
      console.error('❌ Erro ao criar sala:', error);

      // Tratar diferentes tipos de erro
      if (error.message && error.message.includes('Insufficient balance')) {
        // Caso específico de saldo insuficiente - mostrar modal
        setRequiredAmount(roomData.entryFee || 0);
        setShowInsufficientBalanceModal(true);
      } else {
        // Outros erros - mostrar notificação
        let errorMessage = 'Erro ao criar sala. Tente novamente.';

        if (error.message) {
          if (error.message.includes('Invalid game')) {
            errorMessage = 'Jogo inválido ou não encontrado.';
          } else if (error.message.includes('Authentication')) {
            errorMessage = 'Você precisa estar logado para criar uma sala.';
          } else if (error.message.includes('Max players')) {
            errorMessage = 'Número de jogadores inválido para este jogo.';
          } else {
            errorMessage = error.message;
          }
        }

        showNotification('error', errorMessage);
      }
    }
  };

  const handleCreateSquad = (squadData: any) => {
    console.log('Squad created:', squadData);
    setShowCreateSquadModal(false);
    // TODO: Implementar criação real de squad via API
  };

  const handleShareSquadLink = (squadId: string) => {
    const squad = squads.find(s => s.id === squadId);
    if (squad) {
      setSelectedSquad(squad);
      setShowShareSquadLinkModal(true);
    }
  };

  // Usar jogo real da API se disponível, senão usar dados mockados como fallback
  const game = realGame ? {
    id: realGame.id,
    title: realGame.name,
    description: realGame.description || 'Jogo competitivo',
    imageUrl: realGame.image_url || 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    currentPlayers: 1000, // Placeholder
    rating: 4.5, // Placeholder
    genre: realGame.type === 'external' ? 'Externo' : 'Interno',
    releaseDate: '2024',
    publisher: 'Playstrike',
    features: realGame.game_modes || ['Competitivo']
  } : games[gameId as keyof typeof games] || games['1'];

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-xl shadow-lg border backdrop-blur-sm transition-all duration-300 ${
          notification.type === 'success'
            ? 'bg-green-500/10 border-green-500/20 text-green-400'
            : notification.type === 'error'
            ? 'bg-red-500/10 border-red-500/20 text-red-400'
            : 'bg-yellow-500/10 border-yellow-500/20 text-yellow-400'
        }`}>
          <div className="flex items-center gap-3">
            {notification.type === 'success' && <CheckCircle className="w-5 h-5" />}
            {notification.type === 'error' && <X className="w-5 h-5" />}
            {notification.type === 'warning' && <AlertTriangle className="w-5 h-5" />}
            <span className="font-medium">{notification.message}</span>
            <button
              onClick={() => setNotification(null)}
              className="ml-2 hover:opacity-70 transition-opacity"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Game Banner */}
      <div className="relative h-[250px] sm:h-[300px] rounded-xl overflow-hidden">
        <img
          src={game.imageUrl}
          alt={game.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent">
          <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 md:p-8">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-2">{game.title}</h1>
                <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-gray-300">
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold px-4 sm:px-6 md:px-8 py-2 sm:py-3 rounded-xl flex items-center gap-2 hover:opacity-90 shadow-lg transition-all duration-300 hover:scale-105 text-sm sm:text-base"
                  >
                    <Gamepad2 className="w-4 h-4 sm:w-5 sm:h-5" />
                    Criar Sala
                  </button>
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4 sm:w-5 sm:h-5" />
                    <span className="hidden xs:inline">{game.currentPlayers.toLocaleString()} online</span>
                    <span className="xs:hidden">{game.currentPlayers.toLocaleString()}</span>
                  </span>
                  <span className="flex items-center gap-1">
                    <Star className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" />
                    {game.rating} / 5
                  </span>
                  <span className="hidden sm:inline">{game.genre}</span>
                </div>
              </div>
              <button
                onClick={handleInviteFriend}
                className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold px-4 sm:px-6 md:px-8 py-2 sm:py-3 rounded-xl flex items-center gap-2 hover:opacity-90 shadow-lg transition-all duration-300 hover:scale-105 relative overflow-hidden text-sm sm:text-base"
                title="Convidar amigo para jogar"
              >
                {linkCopied ? (
                  <>
                    <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5" />
                    <span>Link copiado!</span>
                    <span className="absolute inset-0 bg-white/20 animate-pulse"></span>
                  </>
                ) : (
                  <>
                    <Share2 className="w-4 h-4 sm:w-5 sm:h-5" />
                    <span className="hidden sm:inline">Convidar amigo para jogar</span>
                    <span className="sm:hidden">Convidar</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex gap-2 border-b border-gray-800 overflow-x-auto hide-scrollbar">
        <button
          onClick={() => {
            setActiveTab('challenges');
            setSearchQuery('');
          }}
          className={`px-3 sm:px-6 py-3 font-semibold whitespace-nowrap ${
            activeTab === 'challenges'
              ? 'text-indigo-400 border-b-2 border-indigo-400'
              : 'text-gray-400 hover:text-white'
          }`}
        >
          <span className="sm:hidden">Desafios</span>
          <span className="hidden sm:inline">Desafios Disponíveis</span>
        </button>
        <button
          onClick={() => {
            setActiveTab('squad');
            setSearchQuery('');
          }}
          className={`px-3 sm:px-6 py-3 font-semibold whitespace-nowrap ${
            activeTab === 'squad'
              ? 'text-indigo-400 border-b-2 border-indigo-400'
              : 'text-gray-400 hover:text-white'
          }`}
        >
          Squad
        </button>
      </div>

      {/* Search, Filter and Create */}
      <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5" />
          <input
            type="text"
            placeholder={
              activeTab === 'challenges' ? 'Buscar desafios...' :
              'Buscar squads...'
            }
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-gray-800/50 text-white rounded-xl py-2 sm:py-3 pl-9 sm:pl-10 pr-9 sm:pr-10 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-indigo-400 backdrop-blur-sm border border-white/10"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          )}
        </div>

        {/* Filtro de tipo de desafio - apenas na aba challenges */}
        {activeTab === 'challenges' && (
          <div className="flex gap-2">
            <button
              onClick={() => setChallengeFilter('all')}
              className={`px-3 py-2 rounded-lg text-sm ${challengeFilter === 'all'
                ? 'bg-gray-700 text-white'
                : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'}`}
            >
              Todos
            </button>
            <button
              onClick={() => setChallengeFilter('1v1')}
              className={`px-3 py-2 rounded-lg text-sm ${challengeFilter === '1v1'
                ? 'bg-rose-400/20 text-rose-400'
                : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'}`}
            >
              1v1
            </button>
            <button
              onClick={() => setChallengeFilter('tournament')}
              className={`px-3 py-2 rounded-lg text-sm ${challengeFilter === 'tournament'
                ? 'bg-purple-500/20 text-purple-400'
                : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'}`}
            >
              Torneios
            </button>
          </div>
        )}

        <button
          onClick={() => activeTab === 'challenges' ? setShowCreateModal(true) : setShowCreateSquadModal(true)}
          className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold px-3 sm:px-6 py-2 sm:py-3 rounded-xl flex items-center gap-1 sm:gap-2 hover:opacity-90 shadow-lg transition-all duration-300 hover:scale-105 text-sm sm:text-base whitespace-nowrap"
        >
          <Plus className="w-4 h-4 sm:w-5 sm:h-5" />
          <span className="hidden sm:inline">{activeTab === 'challenges' ? 'Criar Desafio' : 'Criar Squad'}</span>
          <span className="sm:hidden">Criar</span>
        </button>
      </div>

      {/* Loading State */}
      {isLoading && activeTab === 'challenges' && (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Loader2 className="w-8 h-8 text-indigo-400 animate-spin mx-auto mb-3" />
          <p className="text-gray-400">Carregando salas...</p>
        </div>
      )}

      {/* Error State */}
      {error && activeTab === 'challenges' && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-4">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      {/* Content */}
      {activeTab === 'challenges' && !isLoading && (
        <div className="grid gap-4">
          {currentGameRooms.length > 0 ? (
            currentGameRooms.map(room => (
            <div
              key={room.id}
              className="bg-gray-800 rounded-xl p-4 hover:bg-gray-700 cursor-pointer"
              onClick={() => {
                if (room.type === 'tournament') {
                  // Redirecionar para a página de detalhes do torneio
                  navigate(`/tournaments/${room.id}`);
                } else {
                  // Redirecionar para a sala 1v1 padrão
                  navigate(`/games/${gameId}/rooms/${room.id}`);
                }
              }}
            >
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-white font-semibold">{room.name}</h3>
                    <span className={`text-xs px-2 py-0.5 rounded ${
                      room.type === 'tournament'
                        ? 'bg-purple-500/20 text-purple-400'
                        : 'bg-rose-400/20 text-rose-400'
                    }`}>
                      {room.type === 'tournament' ? 'Desafio Torneio' : 'Desafio 1x1'}
                    </span>
                  </div>

                  {/* Informações comuns */}
                  <div className="flex flex-wrap items-center gap-x-3 gap-y-1 text-sm text-gray-400 mb-2">
                    <span>{room.mode}</span>
                    <span>•</span>
                    <span>{room.map}</span>
                    <span>•</span>
                    <span className="flex items-center gap-1">
                      <Wallet className="w-4 h-4 text-yellow-400" />
                      R$ {room.entryFee}
                    </span>
                    <span>•</span>
                    <span className="flex items-center gap-1">
                      <Trophy className="w-4 h-4 text-green-400" />
                      R$ {room.prizePool}
                    </span>
                  </div>

                  {/* Informações específicas por tipo */}
                  <div className="flex flex-wrap items-center gap-x-3 gap-y-1 text-xs text-gray-400">
                    <span className="flex items-center gap-1">
                      <Users className="w-3 h-3" />
                      {room.players}/{room.maxPlayers} jogadores
                    </span>
                    <span>•</span>
                    <span className="flex items-center gap-1">
                      <Settings className="w-3 h-3" />
                      {room.status === 'waiting' ? 'Aguardando' : 'Em andamento'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 sm:gap-4">
                  <div className="text-right hidden md:block">
                    <p className="text-white text-sm">{room.host.name}</p>
                    <p className="text-gray-400 text-xs">Anfitrião</p>
                  </div>
                  <img
                    src={room.host.avatar}
                    alt={room.host.name}
                    className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover"
                  />
                  <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                </div>
              </div>
            </div>
            ))
          ) : (
            <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
              <p className="text-gray-400 mb-2">Nenhuma sala encontrada</p>
              {searchQuery && (
                <p className="text-sm text-gray-500">Tente uma busca diferente ou crie uma nova sala</p>
              )}
            </div>
          )}
        </div>
      )}

      {activeTab === 'squad' && (
        <div className="space-y-6">
          {/* Squad Section */}
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-white font-bold text-lg">Seu Squad</h2>
              <button
                onClick={() => {
                  if (squads.length > 0) {
                    handleShareSquadLink(squads[0].id);
                  }
                }}
                className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity shadow-lg flex items-center gap-2"
              >
                <UserPlus className="w-4 h-4" />
                Convidar Amigos
              </button>
            </div>

            {squads.length > 0 ? (
              <div className="space-y-4">
                {squads.map(squad => (
                  <div key={squad.id} className="bg-gray-700/50 backdrop-blur-sm border border-white/10 rounded-xl p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="text-white font-semibold">{squad.name}</h3>
                        <div className="flex items-center gap-2 text-sm text-gray-400">
                          <span>{squad.game}</span>
                          <span>•</span>
                          <span className="flex items-center gap-1">
                            <Users className="w-4 h-4" />
                            {squad.members.length} membros
                          </span>
                          <span>•</span>
                          <span className={squad.status === 'open' ? 'text-green-400' : 'text-indigo-400'}>
                            {squad.status === 'open' ? 'Disponível' : 'Jogando'}
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <button className="bg-gray-800/70 backdrop-blur-sm border border-white/10 text-white p-2 rounded-lg hover:bg-gray-700/50 transition-colors">
                          <MessageSquare className="w-5 h-5" />
                        </button>
                        <button className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity shadow-lg">
                          Jogar
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                      {squad.members.map(member => (
                        <div key={member.id} className="bg-gray-800/70 backdrop-blur-sm border border-white/10 rounded-lg p-2 flex items-center gap-2">
                          <div className="relative">
                            <img
                              src={member.avatar}
                              alt={member.name}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                            <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-gray-800 ${
                              member.status === 'online' ? 'bg-green-500' :
                              member.status === 'playing' ? 'bg-indigo-500' :
                              'bg-gray-500'
                            }`} />
                          </div>
                          <div className="overflow-hidden">
                            <p className="text-white text-sm truncate">{member.name}</p>
                            <p className="text-xs text-gray-400 truncate">
                              {member.status === 'playing' ? `Jogando ${member.game}` :
                               member.status === 'offline' ? `Último acesso: ${member.lastActive}` :
                               'Online'}
                            </p>
                          </div>
                        </div>
                      ))}
                      <div
                        onClick={() => handleShareSquadLink(squad.id)}
                        className="bg-gray-800/70 backdrop-blur-sm border border-dashed border-white/20 rounded-lg p-2 flex items-center justify-center cursor-pointer hover:bg-gray-700/50 transition-colors"
                      >
                        <UserPlus className="w-5 h-5 text-gray-400" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-gray-700/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
                <UserCircle className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400">
                  Você ainda não tem um squad.
                  <br />
                  Crie um squad para jogar com seus amigos!
                </p>
                <button
                  onClick={() => setShowCreateSquadModal(true)}
                  className="mt-4 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-semibold px-6 py-2 rounded-lg hover:opacity-90 transition-opacity shadow-lg"
                >
                  Criar Squad
                </button>
              </div>
            )}
          </div>

          {/* Friends Section */}
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <h2 className="text-white font-bold text-lg mb-4">Amigos Online</h2>

            <div className="space-y-2">
              {friends.filter(f => f.status !== 'offline').map(friend => (
                <div key={friend.id} className="bg-gray-700/50 backdrop-blur-sm border border-white/10 rounded-xl p-3 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <img
                        src={friend.avatar}
                        alt={friend.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-gray-800 ${
                        friend.status === 'online' ? 'bg-green-500' :
                        friend.status === 'playing' ? 'bg-indigo-500' :
                        'bg-gray-500'
                      }`} />
                    </div>
                    <div>
                      <p className="text-white font-medium">{friend.name}</p>
                      <p className="text-sm text-gray-400">
                        {friend.status === 'playing' ? `Jogando ${friend.game}` : 'Online'}
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button className="bg-gray-800/70 backdrop-blur-sm border border-white/10 text-white p-2 rounded-lg hover:bg-gray-700/50 transition-colors">
                      <MessageSquare className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => {
                        if (squads.length > 0) {
                          handleShareSquadLink(squads[0].id);
                        } else {
                          setShowCreateSquadModal(true);
                        }
                      }}
                      className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity shadow-lg"
                    >
                      Convidar
                    </button>
                  </div>
                </div>
              ))}

              {friends.filter(f => f.status !== 'offline').length === 0 && (
                <div className="bg-gray-700/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center">
                  <p className="text-gray-400">
                    Nenhum amigo online no momento.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}



      {/* Create Room Modal */}
      {showCreateModal && (
        <CreateRoomModal
          onClose={() => setShowCreateModal(false)}
          onCreate={handleCreateRoom}
          games={[
            { id: gameId || '1', title: game.title }
          ]}
        />
      )}

      {/* Create Squad Modal */}
      {showCreateSquadModal && (
        <CreateSquadModal
          onClose={() => setShowCreateSquadModal(false)}
          onCreateSquad={handleCreateSquad}
          gameId={gameId || '1'}
          gameName={game.title}
        />
      )}

      {/* Share Squad Link Modal */}
      {showShareSquadLinkModal && selectedSquad && (
        <ShareSquadLinkModal
          onClose={() => setShowShareSquadLinkModal(false)}
          squadId={selectedSquad.id}
          squadName={selectedSquad.name}
          gameId={gameId || '1'}
          gameName={game.title}
        />
      )}

      {/* Insufficient Balance Modal */}
      {showInsufficientBalanceModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-xl border border-gray-700 p-6 w-full max-w-md">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Wallet className="w-8 h-8 text-red-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Saldo Insuficiente</h3>
              <p className="text-gray-400">
                Você precisa de <span className="text-white font-semibold">R$ {requiredAmount.toFixed(2)}</span> para criar esta sala.
              </p>
              <p className="text-gray-400 mt-2">
                Adicione fundos à sua carteira para continuar.
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => {
                  setShowInsufficientBalanceModal(false);
                  navigate('/wallet');
                }}
                className="w-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-3 rounded-xl flex items-center justify-center gap-2 hover:opacity-90 transition-opacity"
              >
                <CreditCard className="w-5 h-5" />
                Adicionar Fundos
                <ArrowRight className="w-4 h-4" />
              </button>

              <button
                onClick={() => setShowInsufficientBalanceModal(false)}
                className="w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 rounded-xl transition-colors"
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
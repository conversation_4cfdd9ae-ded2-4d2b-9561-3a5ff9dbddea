-- Migration 002: Matches and Rooms
-- Created: 2024-01-01
-- Description: Creates tables for game rooms, matches, and matchmaking

-- Additional types for matches and rooms
CREATE TYPE room_type AS ENUM ('1v1', 'tournament', 'practice');
CREATE TYPE match_result_status AS ENUM ('pending', 'confirmed', 'disputed', 'auto_confirmed');
CREATE TYPE dispute_status AS ENUM ('open', 'under_review', 'resolved', 'rejected');

-- Game rooms table
CREATE TABLE game_rooms (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
    host_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHA<PERSON>(100) NOT NULL,
    type room_type NOT NULL,
    status room_status DEFAULT 'waiting',
    entry_fee DECIMAL(10,2) NOT NULL CHECK (entry_fee >= 0),
    max_players INTEGER NOT NULL DEFAULT 2,
    current_players INTEGER DEFAULT 1,
    is_public BOOLEAN DEFAULT TRUE,
    password_hash VARCHAR(255), -- For private rooms
    game_settings JSONB DEFAULT '{}',
    room_code VARCHAR(10) UNIQUE, -- Short code for easy joining
    scheduled_start_time TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    finished_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT max_players_valid CHECK (max_players >= 2 AND max_players <= 16),
    CONSTRAINT current_players_valid CHECK (current_players >= 0 AND current_players <= max_players)
);

-- Room participants table
CREATE TABLE room_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    room_id UUID NOT NULL REFERENCES game_rooms(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_ready BOOLEAN DEFAULT FALSE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    UNIQUE(room_id, user_id)
);

-- Matches table
CREATE TABLE matches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
    room_id UUID REFERENCES game_rooms(id) ON DELETE SET NULL,
    tournament_id UUID, -- Will be added in tournament migration
    match_type VARCHAR(20) NOT NULL DEFAULT '1v1', -- '1v1', 'tournament', 'ranked'
    status match_status DEFAULT 'scheduled',
    entry_fee DECIMAL(10,2) NOT NULL CHECK (entry_fee >= 0),
    total_prize DECIMAL(12,2) NOT NULL CHECK (total_prize >= 0),
    game_settings JSONB DEFAULT '{}',
    scheduled_time TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    finished_at TIMESTAMP WITH TIME ZONE,
    winner_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    result_data JSONB DEFAULT '{}', -- Store scores, stats, etc.
    result_status match_result_status DEFAULT 'pending',
    result_image_url TEXT, -- URL to uploaded result screenshot
    analysis_data JSONB DEFAULT '{}', -- OpenAI analysis results
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Match participants table
CREATE TABLE match_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    team_number INTEGER DEFAULT 1, -- For team-based games
    score INTEGER DEFAULT 0,
    placement INTEGER, -- Final placement (1st, 2nd, etc.)
    stats JSONB DEFAULT '{}', -- Game-specific stats
    earnings DECIMAL(10,2) DEFAULT 0.00,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(match_id, user_id),
    CONSTRAINT valid_team_number CHECK (team_number >= 1 AND team_number <= 8),
    CONSTRAINT valid_placement CHECK (placement >= 1)
);

-- Match disputes table
CREATE TABLE match_disputes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
    disputing_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reason TEXT NOT NULL,
    evidence_urls TEXT[], -- Screenshots, videos, etc.
    status dispute_status DEFAULT 'open',
    admin_notes TEXT,
    resolved_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Matchmaking queue table
CREATE TABLE matchmaking_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
    entry_fee DECIMAL(10,2) NOT NULL,
    game_settings JSONB DEFAULT '{}',
    skill_rating INTEGER DEFAULT 1000,
    search_started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    estimated_wait_time INTEGER, -- in seconds
    
    -- Constraints
    UNIQUE(user_id, game_id) -- User can only be in one queue per game
);

-- Room chat messages table
CREATE TABLE room_chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    room_id UUID NOT NULL REFERENCES game_rooms(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text', -- 'text', 'system', 'image'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT message_length CHECK (LENGTH(message) <= 1000)
);

-- Create indexes
CREATE INDEX idx_game_rooms_game_id ON game_rooms(game_id);
CREATE INDEX idx_game_rooms_host_user_id ON game_rooms(host_user_id);
CREATE INDEX idx_game_rooms_status ON game_rooms(status);
CREATE INDEX idx_game_rooms_type ON game_rooms(type);
CREATE INDEX idx_game_rooms_is_public ON game_rooms(is_public);
CREATE INDEX idx_game_rooms_created_at ON game_rooms(created_at);

CREATE INDEX idx_room_participants_room_id ON room_participants(room_id);
CREATE INDEX idx_room_participants_user_id ON room_participants(user_id);

CREATE INDEX idx_matches_game_id ON matches(game_id);
CREATE INDEX idx_matches_status ON matches(status);
CREATE INDEX idx_matches_match_type ON matches(match_type);
CREATE INDEX idx_matches_scheduled_time ON matches(scheduled_time);
CREATE INDEX idx_matches_winner_user_id ON matches(winner_user_id);
CREATE INDEX idx_matches_created_at ON matches(created_at);

CREATE INDEX idx_match_participants_match_id ON match_participants(match_id);
CREATE INDEX idx_match_participants_user_id ON match_participants(user_id);

CREATE INDEX idx_match_disputes_match_id ON match_disputes(match_id);
CREATE INDEX idx_match_disputes_status ON match_disputes(status);

CREATE INDEX idx_matchmaking_queue_user_id ON matchmaking_queue(user_id);
CREATE INDEX idx_matchmaking_queue_game_id ON matchmaking_queue(game_id);
CREATE INDEX idx_matchmaking_queue_skill_rating ON matchmaking_queue(skill_rating);

CREATE INDEX idx_room_chat_messages_room_id ON room_chat_messages(room_id);
CREATE INDEX idx_room_chat_messages_created_at ON room_chat_messages(created_at);

-- Apply updated_at triggers
CREATE TRIGGER update_game_rooms_updated_at BEFORE UPDATE ON game_rooms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_matches_updated_at BEFORE UPDATE ON matches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_match_disputes_updated_at BEFORE UPDATE ON match_disputes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

import React from 'react';
import { ArrowUpRight, ArrowDownLeft, Gamepad2, Trophy, Users, Gift } from 'lucide-react';

interface TransactionCardProps {
  type: 'deposit' | 'withdrawal' | 'game' | 'tournament' | 'affiliate' | 'bonus' | 'win' | 'bet' | 'commission' | 'refund';
  amount: number;
  date: string;
  description: string;
  status: 'completed' | 'pending' | 'failed' | 'cancelled';
}

export default function TransactionCard({
  type,
  amount,
  date,
  description,
  status
}: TransactionCardProps) {
  const icons = {
    deposit: ArrowDownLeft,
    withdrawal: ArrowUpRight,
    game: Gamepad2,
    tournament: Trophy,
    affiliate: Users,
    bonus: Gift,
    win: Trophy,
    bet: Gamepad2,
    commission: Users,
    refund: ArrowDownLeft
  };

  const Icon = icons[type] || Gamepad2;
  const isPositive = ['deposit', 'win', 'commission', 'bonus', 'refund', 'affiliate'].includes(type) || (type === 'game' && amount > 0);

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 hover:bg-gray-700/50 transition-colors">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${
            ['game', 'bet'].includes(type) ? 'bg-purple-500/20' :
            ['tournament', 'win'].includes(type) ? 'bg-yellow-500/20' :
            ['affiliate', 'commission'].includes(type) ? 'bg-blue-500/20' :
            isPositive ? 'bg-green-500/20' : 'bg-red-500/20'
          }`}>
            <Icon className={`w-5 h-5 ${
              ['game', 'bet'].includes(type) ? 'text-purple-500' :
              ['tournament', 'win'].includes(type) ? 'text-yellow-500' :
              ['affiliate', 'commission'].includes(type) ? 'text-blue-500' :
              isPositive ? 'text-green-500' : 'text-red-500'
            }`} />
          </div>
          <div>
            <p className="text-white font-semibold">{description}</p>
            <p className="text-gray-400 text-sm">{date}</p>
          </div>
        </div>
        <div className="text-right">
          <p className={`font-bold ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
            {isPositive ? '+' : '-'} R$ {Math.abs(amount).toFixed(2)}
          </p>
          <p className={`text-sm ${
            status === 'completed' ? 'text-green-500' :
            status === 'pending' ? 'text-yellow-400' :
            status === 'cancelled' ? 'text-gray-400' : 'text-red-500'
          }`}>
            {status === 'completed' ? 'Concluído' :
             status === 'pending' ? 'Pendente' :
             status === 'cancelled' ? 'Cancelado' : 'Falhou'}
          </p>
        </div>
      </div>
    </div>
  );
}
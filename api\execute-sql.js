// Script simples para executar SQL no Supabase
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function executeSQLFile(filename) {
  try {
    console.log(`\n🔄 Executando: ${filename}`);
    
    const filePath = path.join(__dirname, '..', 'database', 'migrations', filename);
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ Arquivo não encontrado: ${filename}`);
      return false;
    }
    
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Executar o SQL completo
    const { data, error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error(`❌ Erro em ${filename}:`, error.message);
      return false;
    }
    
    console.log(`✅ ${filename} executado com sucesso!`);
    return true;
    
  } catch (err) {
    console.error(`❌ Erro ao processar ${filename}:`, err.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Executando Migration 001 - Schema Inicial...');
  
  // Primeiro, vamos tentar criar a função exec_sql se não existir
  try {
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql text)
      RETURNS text
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE sql;
        RETURN 'OK';
      EXCEPTION
        WHEN OTHERS THEN
          RETURN SQLERRM;
      END;
      $$;
    `;
    
    const { error } = await supabase.rpc('exec_sql', { sql: createFunctionSQL });
    if (error) {
      console.log('⚠️ Função exec_sql pode não existir, tentando criar...');
    }
  } catch (err) {
    console.log('⚠️ Tentando abordagem alternativa...');
  }
  
  // Executar apenas a primeira migration para teste
  const success = await executeSQLFile('001_initial_schema.sql');
  
  if (success) {
    console.log('\n🎉 Migration inicial executada com sucesso!');
    console.log('✅ Tabelas básicas criadas no Supabase');
  } else {
    console.log('\n❌ Falha na execução da migration');
  }
  
  process.exit(success ? 0 : 1);
}

main();

import React, { useState } from 'react';
import { apiTestService } from '../services/apiTestService';
import { apiService } from '../services/apiService';
import { authService } from '../services/authService';

const ApiTestPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'disconnected'>('unknown');

  const addLog = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearLogs = () => {
    setTestResults([]);
  };

  const testConnection = async () => {
    setIsLoading(true);
    addLog('🔍 Testando conectividade...');
    
    try {
      const isHealthy = await apiService.healthCheck();
      if (isHealthy) {
        setConnectionStatus('connected');
        addLog('✅ API está respondendo!');
      } else {
        setConnectionStatus('disconnected');
        addLog('❌ API não está respondendo');
      }
    } catch (error) {
      setConnectionStatus('disconnected');
      addLog(`❌ Erro: ${error}`);
    }
    
    setIsLoading(false);
  };

  const testHealthCheck = async () => {
    setIsLoading(true);
    addLog('🔍 Testando health check...');
    
    try {
      const response = await fetch('http://localhost:3001/health');
      const data = await response.json();
      
      if (response.ok) {
        addLog('✅ Health check funcionando!');
        addLog(`🏥 Status: ${data.status}`);
      } else {
        addLog('❌ Health check com erro');
      }
    } catch (error) {
      addLog(`❌ Erro no health check: ${error}`);
    }
    
    setIsLoading(false);
  };

  const testAuth = async () => {
    setIsLoading(true);
    addLog('🔍 Testando autenticação...');
    
    try {
      const response = await authService.login({
        email: '<EMAIL>',
        password: 'password123'
      });
      
      addLog('✅ Login realizado com sucesso!');
      addLog(`👤 Usuário: ${response.user.display_name}`);
      addLog(`🔑 Token: ${response.tokens.access_token.substring(0, 20)}...`);
      
      // Testa perfil
      const profile = await authService.getProfile();
      addLog(`📊 Perfil carregado: ${profile.username}`);
      
    } catch (error) {
      addLog(`❌ Erro na autenticação: ${error}`);
    }
    
    setIsLoading(false);
  };

  const testTournaments = async () => {
    setIsLoading(true);
    addLog('🔍 Testando torneios...');
    
    try {
      const response = await apiService.get('/tournaments');
      
      if (response.success) {
        addLog('✅ Torneios carregados!');
        addLog(`🏆 Encontrados: ${response.data?.length || 0} torneios`);
        
        if (response.data && response.data.length > 0) {
          const tournament = response.data[0];
          addLog(`📋 Primeiro torneio: ${tournament.title}`);
        }
      } else {
        addLog(`❌ Erro ao carregar torneios: ${response.error}`);
      }
    } catch (error) {
      addLog(`❌ Erro: ${error}`);
    }
    
    setIsLoading(false);
  };

  const runAllTests = async () => {
    clearLogs();
    setIsLoading(true);
    addLog('🚀 Iniciando bateria completa de testes...');
    
    try {
      await apiTestService.runAllTests();
      addLog('🎉 Todos os testes concluídos!');
    } catch (error) {
      addLog(`❌ Erro nos testes: ${error}`);
    }
    
    setIsLoading(false);
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-500';
      case 'disconnected': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return '🟢 Conectado';
      case 'disconnected': return '🔴 Desconectado';
      default: return '⚪ Desconhecido';
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">🧪 Teste de API</h1>
        
        {/* Status da Conexão */}
        <div className="bg-gray-800 rounded-lg p-4 mb-6">
          <h2 className="text-xl font-semibold mb-2">Status da Conexão</h2>
          <p className={`text-lg ${getStatusColor()}`}>
            {getStatusText()}
          </p>
        </div>

        {/* Botões de Teste */}
        <div className="bg-gray-800 rounded-lg p-4 mb-6">
          <h2 className="text-xl font-semibold mb-4">Testes Individuais</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={testConnection}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-4 py-2 rounded-lg transition-colors"
            >
              🔗 Conectividade
            </button>
            
            <button
              onClick={testHealthCheck}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 px-4 py-2 rounded-lg transition-colors"
            >
              🏥 Health Check
            </button>
            
            <button
              onClick={testAuth}
              disabled={isLoading}
              className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 px-4 py-2 rounded-lg transition-colors"
            >
              🔐 Autenticação
            </button>
            
            <button
              onClick={testTournaments}
              disabled={isLoading}
              className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 px-4 py-2 rounded-lg transition-colors"
            >
              🏆 Torneios
            </button>
          </div>
          
          <div className="mt-4 flex gap-4">
            <button
              onClick={runAllTests}
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 px-6 py-2 rounded-lg transition-colors"
            >
              🚀 Executar Todos os Testes
            </button>
            
            <button
              onClick={clearLogs}
              disabled={isLoading}
              className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-500 px-6 py-2 rounded-lg transition-colors"
            >
              🗑️ Limpar Logs
            </button>
          </div>
        </div>

        {/* Logs */}
        <div className="bg-gray-800 rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-4">📋 Logs de Teste</h2>
          <div className="bg-black rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
            {testResults.length === 0 ? (
              <p className="text-gray-500">Nenhum teste executado ainda...</p>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))
            )}
            {isLoading && (
              <div className="text-yellow-500">
                ⏳ Executando teste...
              </div>
            )}
          </div>
        </div>

        {/* Informações */}
        <div className="bg-gray-800 rounded-lg p-4 mt-6">
          <h2 className="text-xl font-semibold mb-4">ℹ️ Informações</h2>
          <div className="text-sm text-gray-300 space-y-2">
            <p><strong>API Base URL:</strong> http://localhost:3001/api</p>
            <p><strong>Health Check:</strong> http://localhost:3001/health</p>
            <p><strong>Status:</strong> Servidor mock em execução</p>
            <p><strong>Funcionalidades:</strong> Autenticação, Torneios, Health Check</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiTestPage;

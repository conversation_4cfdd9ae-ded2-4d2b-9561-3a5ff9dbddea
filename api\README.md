# Playstrike API

API backend completa para a plataforma de jogos competitivos Playstrike, desenvolvida com Node.js, TypeScript, Express.js e Supabase.

## 🚀 Funcionalidades

### ✅ Implementadas
- **Autenticação completa** (registro, login, refresh token, verificação de email)
- **Gestão de usuários** (perfis, estatísticas, conquistas, busca)
- **Sistema de carteira** (depósitos, saques, histórico de transações)
- **Jogos e salas** (listagem, criação de salas, rankings)
- **WebSockets em tempo real** (chat, notificações, matchmaking)
- **Sistema de segurança** (rate limiting, validação, autenticação JWT)
- **Logs estruturados** (Winston com diferentes níveis)
- **Middleware robusto** (validação Joi, CORS, Helmet)

### 🔄 Em Desenvolvimento
- **Sistema de partidas** (criação, participação, resultados)
- **Torneios completos** (criação, inscrições, chaves, temporadas)
- **Recursos sociais** (amigos, clubes, chat privado)
- **Programa de afiliados** (comissões, saques, estatísticas)
- **Notificações** (push, email, in-app)
- **Painel administrativo** (gestão de usuários, relatórios)
- **Webhooks** (pagamentos, eventos externos)

## 📋 Pré-requisitos

- Node.js 18+ 
- npm ou yarn
- Conta no Supabase
- Chave da API OpenAI (opcional, para análise de resultados)

## 🛠️ Instalação

1. **Clone o repositório**
```bash
git clone <repository-url>
cd api
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure as variáveis de ambiente**
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

4. **Configure o banco de dados**
- Execute as migrations do Supabase (pasta `database/migrations/`)
- Configure as políticas RLS
- Insira os dados iniciais (seed data)

5. **Inicie o servidor**
```bash
# Desenvolvimento
npm run dev

# Produção
npm run build
npm start
```

## 📚 Documentação da API

### Base URL
```
http://localhost:3001/api
```

### Endpoints Principais

#### Autenticação
- `POST /auth/register` - Registrar usuário
- `POST /auth/login` - Login
- `POST /auth/logout` - Logout
- `POST /auth/refresh` - Renovar token
- `GET /auth/profile` - Perfil do usuário
- `POST /auth/verify-email` - Verificar email

#### Usuários
- `GET /users/search` - Buscar usuários
- `GET /users/:id` - Perfil do usuário
- `PUT /users/:id` - Atualizar perfil
- `GET /users/:id/stats` - Estatísticas do usuário
- `GET /users/:id/achievements` - Conquistas do usuário

#### Carteira
- `GET /wallet` - Informações da carteira
- `POST /wallet/deposit` - Criar depósito
- `POST /wallet/withdraw` - Criar saque
- `GET /wallet/transactions` - Histórico de transações
- `GET /wallet/payment-methods` - Métodos de pagamento

#### Jogos
- `GET /games` - Listar jogos
- `GET /games/:id` - Detalhes do jogo
- `GET /games/:id/rooms` - Salas do jogo
- `POST /games/:id/rooms` - Criar sala
- `GET /games/:id/ranking` - Ranking do jogo

#### WebSockets
- `join_room` - Entrar em sala
- `leave_room` - Sair da sala
- `send_message` - Enviar mensagem
- `player_ready` - Status de pronto
- `start_matchmaking` - Iniciar matchmaking

### Autenticação

A API usa JWT (JSON Web Tokens) para autenticação. Inclua o token no header:

```
Authorization: Bearer <seu-token>
```

### Rate Limiting

- **Geral**: 100 requests por 15 minutos
- **Autenticação**: 5 tentativas por 15 minutos
- **Transações**: 20 por hora
- **Chat**: 30 mensagens por minuto
- **Uploads**: 50 por hora

### Códigos de Status

- `200` - Sucesso
- `201` - Criado com sucesso
- `400` - Erro de validação
- `401` - Não autenticado
- `403` - Não autorizado
- `404` - Não encontrado
- `429` - Rate limit excedido
- `500` - Erro interno do servidor

## 🏗️ Arquitetura

```
src/
├── config/          # Configurações (banco, ambiente)
├── controllers/     # Controladores das rotas
├── middleware/      # Middlewares (auth, validação, rate limiting)
├── routes/          # Definição das rotas
├── sockets/         # Handlers do WebSocket
├── types/           # Tipos TypeScript
├── utils/           # Utilitários (logger, paginação)
└── server.ts        # Servidor principal
```

### Tecnologias Utilizadas

- **Node.js** - Runtime JavaScript
- **TypeScript** - Tipagem estática
- **Express.js** - Framework web
- **Supabase** - Banco de dados PostgreSQL
- **Socket.IO** - WebSockets em tempo real
- **JWT** - Autenticação
- **Joi** - Validação de dados
- **Winston** - Sistema de logs
- **Helmet** - Segurança HTTP
- **bcryptjs** - Hash de senhas

## 🔒 Segurança

### Implementações de Segurança

- **Rate Limiting** - Proteção contra spam e ataques
- **Helmet** - Headers de segurança HTTP
- **CORS** - Controle de origem cruzada
- **JWT** - Tokens seguros com expiração
- **Validação** - Validação rigorosa de entrada
- **RLS** - Row Level Security no Supabase
- **Logs** - Auditoria completa de ações

### Boas Práticas

- Senhas hasheadas com bcrypt (12 rounds)
- Tokens JWT com expiração
- Validação de entrada em todas as rotas
- Logs estruturados para auditoria
- Políticas de privacidade respeitadas
- Rate limiting por usuário e IP

## 📊 Monitoramento

### Health Check
```
GET /health
```

Retorna status dos serviços:
- Banco de dados
- APIs externas
- Tempo de resposta
- Uptime do servidor

### Logs

Os logs são salvos em:
- `logs/combined.log` - Todos os logs
- `logs/error.log` - Apenas erros
- Console (desenvolvimento)

Níveis de log:
- `error` - Erros críticos
- `warn` - Avisos importantes
- `info` - Informações gerais
- `http` - Requests HTTP
- `debug` - Debug detalhado

## 🧪 Testes

```bash
# Executar testes
npm test

# Testes em modo watch
npm run test:watch

# Coverage
npm run test:coverage
```

## 🚀 Deploy

### Desenvolvimento
```bash
npm run dev
```

### Produção
```bash
npm run build
npm start
```

### Docker (Opcional)
```bash
docker build -t playstrike-api .
docker run -p 3001:3001 playstrike-api
```

### Variáveis de Ambiente Obrigatórias

```env
NODE_ENV=production
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-key
JWT_SECRET=your-jwt-secret
OPENAI_API_KEY=your-openai-key
```

## 📈 Performance

### Otimizações Implementadas

- **Compressão gzip** - Reduz tamanho das respostas
- **Paginação** - Limita resultados de queries
- **Índices de banco** - Queries otimizadas
- **Rate limiting** - Previne sobrecarga
- **Logs estruturados** - Performance de logging
- **Validação eficiente** - Joi com abort early

### Métricas Importantes

- Tempo de resposta médio: < 200ms
- Rate limit: 100 req/15min por usuário
- Uptime target: 99.9%
- Logs retention: 30 dias

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

### Padrões de Código

- Use TypeScript para tipagem
- Siga as convenções do ESLint
- Escreva testes para novas funcionalidades
- Documente APIs com comentários JSDoc
- Use commits semânticos

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 Suporte

- Email: <EMAIL>
- Discord: [Servidor da Comunidade]
- Documentação: https://docs.playstrike.com

## 🗺️ Roadmap

### Próximas Funcionalidades

- [ ] Sistema de partidas completo
- [ ] Torneios com chaves automáticas
- [ ] Integração com jogos externos
- [ ] Sistema de streaming
- [ ] Mobile app API
- [ ] Analytics avançado
- [ ] Sistema de moderação
- [ ] Marketplace de itens

### Melhorias Técnicas

- [ ] Cache com Redis
- [ ] Queue system com Bull
- [ ] Microserviços
- [ ] GraphQL API
- [ ] Testes E2E
- [ ] CI/CD pipeline
- [ ] Monitoring com Prometheus
- [ ] Load balancing

import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, UserRegistrationData, UserLoginData, AuthenticatedRequest } from '../types/api';
import { generateToken, generateRefreshToken, verifyRefreshToken } from '../middleware/auth';
import { logger, logAuthEvent, logSecurityEvent } from '../utils/logger';
import config from '../config/environment';

/**
 * Register a new user
 */
export const register = async (req: Request, res: Response): Promise<void> => {
  try {
    const userData: UserRegistrationData = req.body;
    const clientIp = req.ip;

    // Check if user already exists
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('id, email, username')
      .or(`email.eq.${userData.email},username.eq.${userData.username}`)
      .single();

    if (existingUser) {
      logSecurityEvent('registration_attempt_duplicate', 'low', {
        email: userData.email,
        username: userData.username,
        ip: clientIp
      });

      const response: ApiResponse = {
        success: false,
        error: existingUser.email === userData.email
          ? 'Email already registered'
          : 'Username already taken'
      };
      res.status(409).json(response);
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, config.BCRYPT_ROUNDS);

    // Create user with password hash in a single operation
    const { data: newUser, error: userError } = await supabaseAdmin
      .from('users')
      .insert({
        email: userData.email,
        username: userData.username,
        display_name: userData.display_name,
        password_hash: hashedPassword,
        birth_date: userData.birth_date,
        country: userData.country,
        timezone: userData.timezone || 'America/Sao_Paulo',
        language: userData.language || 'pt-BR'
      })
      .select('id, email, username, display_name, status, created_at')
      .single();

    if (userError || !newUser) {
      logger.error('Failed to create user:', userError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create user account'
      };
      res.status(500).json(response);
      return;
    }

    // Generate tokens
    const accessToken = generateToken(newUser);
    const refreshToken = generateRefreshToken(newUser);

    // Process referral code if provided
    if (userData.referral_code) {
      await processReferralCode(newUser.id, userData.referral_code, clientIp);
    }

    // Log successful registration
    logAuthEvent('user_registered', newUser.id, newUser.username, clientIp, true);

    const response: ApiResponse = {
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: newUser.id,
          email: newUser.email,
          username: newUser.username,
          display_name: newUser.display_name,
          status: newUser.status,
          created_at: newUser.created_at
        },
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_in: config.JWT_EXPIRES_IN
        }
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Registration error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error during registration'
    };
    res.status(500).json(response);
  }
};

/**
 * Login user
 */
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const loginData: UserLoginData = req.body;
    const clientIp = req.ip;

    // Get user by email including password hash
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, email, username, display_name, status, password_hash')
      .eq('email', loginData.email)
      .single();

    if (userError || !user) {
      logAuthEvent('login_attempt_invalid_email', undefined, undefined, clientIp, false);

      const response: ApiResponse = {
        success: false,
        error: 'Invalid email or password'
      };
      res.status(401).json(response);
      return;
    }

    // Check user status
    if (user.status !== 'active') {
      logSecurityEvent('login_attempt_inactive_user', 'medium', {
        userId: user.id,
        username: user.username,
        status: user.status,
        ip: clientIp
      });

      const response: ApiResponse = {
        success: false,
        error: `Account is ${user.status}. Please contact support.`
      };
      res.status(403).json(response);
      return;
    }

    // Check if password hash exists
    if (!user.password_hash) {
      logger.error('User has no password hash:', { userId: user.id, email: user.email });

      const response: ApiResponse = {
        success: false,
        error: 'Invalid email or password'
      };
      res.status(401).json(response);
      return;
    }

    // Verify password using bcrypt
    const isPasswordValid = await bcrypt.compare(loginData.password, user.password_hash);

    if (!isPasswordValid) {
      logAuthEvent('login_attempt_invalid_password', user.id, user.username, clientIp, false);

      const response: ApiResponse = {
        success: false,
        error: 'Invalid email or password'
      };
      res.status(401).json(response);
      return;
    }

    // Update last login time
    await supabaseAdmin
      .from('users')
      .update({ last_login_at: new Date().toISOString() })
      .eq('id', user.id);

    // Generate tokens
    const accessToken = generateToken(user);
    const refreshToken = generateRefreshToken(user);

    // Log successful login
    logAuthEvent('user_logged_in', user.id, user.username, clientIp, true);

    const response: ApiResponse = {
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          display_name: user.display_name,
          status: user.status
        },
        tokens: {
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_in: config.JWT_EXPIRES_IN
        }
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Login error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error during login'
    };
    res.status(500).json(response);
  }
};

/**
 * Refresh access token
 */
export const refreshToken = async (req: Request, res: Response): Promise<void> => {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      const response: ApiResponse = {
        success: false,
        error: 'Refresh token is required'
      };
      res.status(400).json(response);
      return;
    }

    // Verify refresh token
    const payload = verifyRefreshToken(refresh_token);

    if (!payload) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid or expired refresh token'
      };
      res.status(401).json(response);
      return;
    }

    // Get user
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, email, username, display_name, status')
      .eq('id', payload.sub)
      .eq('status', 'active')
      .single();

    if (userError || !user) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found or inactive'
      };
      res.status(404).json(response);
      return;
    }

    // Generate new tokens
    const newAccessToken = generateToken(user);
    const newRefreshToken = generateRefreshToken(user);

    logAuthEvent('token_refreshed', user.id, user.username, req.ip, true);

    const response: ApiResponse = {
      success: true,
      message: 'Token refreshed successfully',
      data: {
        tokens: {
          access_token: newAccessToken,
          refresh_token: newRefreshToken,
          expires_in: config.JWT_EXPIRES_IN
        }
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Token refresh error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error during token refresh'
    };
    res.status(500).json(response);
  }
};

/**
 * Logout user
 */
export const logout = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    // In a production environment, you might want to blacklist the token
    // For now, we'll just log the logout event
    logAuthEvent('user_logged_out', user.id, user.username, req.ip, true);

    const response: ApiResponse = {
      success: true,
      message: 'Logout successful'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Logout error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error during logout'
    };
    res.status(500).json(response);
  }
};

/**
 * Get current user profile
 */
export const getProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    // Get user with profile and stats
    const { data: userData, error } = await supabaseAdmin
      .from('users')
      .select(`
        id, email, username, display_name, avatar_url, status,
        email_verified, phone, birth_date, country, timezone, language,
        created_at, updated_at, last_login_at,
        user_profiles (
          bio, favorite_games, gaming_experience, preferred_game_modes,
          social_links, privacy_settings, notification_preferences
        ),
        user_stats (
          total_matches, total_wins, total_losses, total_draws, win_rate,
          total_earnings, total_spent, current_streak, best_streak,
          ranking_points, level, experience_points, achievements_count
        )
      `)
      .eq('id', user.id)
      .single();

    if (error || !userData) {
      logger.error('Failed to get user profile:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve user profile'
      };
      res.status(404).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        user: userData
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get profile error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error while retrieving profile'
    };
    res.status(500).json(response);
  }
};

/**
 * Verify email
 */
export const verifyEmail = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token } = req.body;

    if (!token) {
      const response: ApiResponse = {
        success: false,
        error: 'Verification token is required'
      };
      res.status(400).json(response);
      return;
    }

    // Verify email using Supabase auth
    const { data, error } = await supabaseAdmin.auth.verifyOtp({
      token_hash: token,
      type: 'email'
    });

    if (error || !data.user) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid or expired verification token'
      };
      res.status(400).json(response);
      return;
    }

    // Update user email verification status
    await supabaseAdmin
      .from('users')
      .update({ email_verified: true })
      .eq('email', data.user.email);

    logAuthEvent('email_verified', data.user.id, undefined, req.ip, true);

    const response: ApiResponse = {
      success: true,
      message: 'Email verified successfully'
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Email verification error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error during email verification'
    };
    res.status(500).json(response);
  }
};

/**
 * Process referral code during registration
 */
async function processReferralCode(newUserId: string, referralCode: string, clientIp?: string): Promise<void> {
  try {
    logger.info(`Processing referral code: ${referralCode} for user: ${newUserId}`);

    // Find the referrer by affiliate code
    const { data: affiliate, error: affiliateError } = await supabaseAdmin
      .from('affiliate_programs')
      .select('user_id, affiliate_code')
      .eq('affiliate_code', referralCode)
      .eq('is_active', true)
      .single();

    if (affiliateError || !affiliate) {
      logger.warn(`Invalid referral code: ${referralCode}`);
      return;
    }

    // Don't allow self-referral
    if (affiliate.user_id === newUserId) {
      logger.warn(`Self-referral attempt: ${newUserId}`);
      return;
    }

    // Create referral record
    const { error: referralError } = await supabaseAdmin
      .from('user_referrals')
      .insert({
        referrer_user_id: affiliate.user_id,
        referred_user_id: newUserId,
        affiliate_code: referralCode,
        referral_source: 'direct_link',
        ip_address: clientIp,
        referred_at: new Date().toISOString(),
        is_active: true
      });

    if (referralError) {
      logger.error('Failed to create referral record:', referralError);
      return;
    }

    // Update affiliate stats using RPC function
    const { data: newCount, error: rpcError } = await supabaseAdmin
      .rpc('increment_total_referrals', { affiliate_user_id: affiliate.user_id });

    if (!rpcError && newCount) {
      await supabaseAdmin
        .from('affiliate_programs')
        .update({
          total_referrals: newCount,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', affiliate.user_id);
    }

    logger.info(`✅ Referral processed successfully: ${referralCode} -> ${newUserId}`);
  } catch (error: any) {
    logger.error('Error processing referral code:', error);
  }
}

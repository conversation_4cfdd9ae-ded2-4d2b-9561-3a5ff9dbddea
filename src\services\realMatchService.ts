import { apiService } from './apiService';

// Tipos para partidas
export interface Player {
  id: string;
  username: string;
  display_name: string;
  avatar_url?: string;
  rating?: number;
  status?: string;
}

export interface Match {
  id: string;
  tournament_id?: string;
  tournament_name?: string;
  game_id: string;
  game_name: string;
  round?: number;
  player1: Player;
  player2: Player;
  scheduled_time: string;
  started_at?: string;
  ended_at?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  winner_id?: string;
  score?: {
    player1_score: number;
    player2_score: number;
  };
  bet_amount: number;
  prize_amount: number;
  room_id?: string;
  metadata?: Record<string, any>;
}

export interface MatchResult {
  match_id: string;
  winner_id: string;
  score: {
    player1_score: number;
    player2_score: number;
  };
  evidence?: {
    screenshots?: string[];
    video_url?: string;
    description?: string;
  };
}

export interface MatchmakingRequest {
  game_id: string;
  bet_amount: number;
  preferred_rating_range?: {
    min: number;
    max: number;
  };
  game_settings?: Record<string, any>;
}

class RealMatchService {

  // Obtém partidas do usuário
  async getUserMatches(userId?: string, status?: string, page: number = 1, limit: number = 20): Promise<Match[]> {
    try {
      const endpoint = userId ? `/users/${userId}/matches` : '/matches/my-matches';
      console.log(`🎮 Buscando partidas do usuário (página ${page})...`);

      const params: any = { page, limit };
      if (status) params.status = status;

      const response = await apiService.get<Match[]>(endpoint, { params });

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} partidas carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar partidas:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar partidas:', error);
      return [];
    }
  }

  // Obtém detalhes de uma partida com retry mechanism
  async getMatchById(matchId: string, retries: number = 3, delay: number = 1000): Promise<Match | null> {
    try {
      console.log(`🔍 Buscando detalhes da partida ${matchId}... (tentativa ${4 - retries})`);

      const response = await apiService.get<Match>(`/matches/${matchId}`);

      if (response.success && response.data) {
        console.log(`✅ Partida ${matchId} carregada`);
        // A API retorna { success: true, data: { match: {...} } }
        return (response.data as any).match || response.data;
      } else {
        console.warn(`⚠️ Erro ao carregar partida ${matchId}:`, response.error);

        // Se ainda há tentativas restantes e o erro é 404, tenta novamente
        if (retries > 0 && response.error?.includes('not found')) {
          console.log(`🔄 Tentando novamente em ${delay}ms... (${retries} tentativas restantes)`);
          await new Promise(resolve => setTimeout(resolve, delay));
          return this.getMatchById(matchId, retries - 1, delay * 1.5); // Aumenta o delay progressivamente
        }

        return null;
      }
    } catch (error: any) {
      console.error(`❌ Erro ao buscar partida ${matchId}:`, error);

      // Se ainda há tentativas restantes e o erro é 404, tenta novamente
      if (retries > 0 && (error.message?.includes('404') || error.message?.includes('not found'))) {
        console.log(`🔄 Tentando novamente em ${delay}ms... (${retries} tentativas restantes)`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.getMatchById(matchId, retries - 1, delay * 1.5);
      }

      return null;
    }
  }

  // Inicia matchmaking
  async startMatchmaking(request: MatchmakingRequest): Promise<{ success: boolean; matchmaking_id?: string; error?: string }> {
    try {
      console.log(`🔍 Iniciando matchmaking para ${request.game_id} (R$ ${request.bet_amount})...`);

      const response = await apiService.post<{ matchmaking_id: string }>('/matches/matchmaking', request);

      if (response.success && response.data) {
        console.log(`✅ Matchmaking iniciado: ${response.data.matchmaking_id}`);
        return {
          success: true,
          matchmaking_id: response.data.matchmaking_id
        };
      } else {
        console.warn('⚠️ Erro ao iniciar matchmaking:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao iniciar matchmaking'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao iniciar matchmaking:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Cancela matchmaking
  async cancelMatchmaking(matchmakingId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`❌ Cancelando matchmaking ${matchmakingId}...`);

      const response = await apiService.delete(`/matches/matchmaking/${matchmakingId}`);

      if (response.success) {
        console.log(`✅ Matchmaking ${matchmakingId} cancelado`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao cancelar matchmaking:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao cancelar matchmaking'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao cancelar matchmaking:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Aceita uma partida encontrada
  async acceptMatch(matchId: string): Promise<{ success: boolean; room_id?: string; error?: string }> {
    try {
      console.log(`✅ Aceitando partida ${matchId}...`);

      const response = await apiService.post<{ room_id: string }>(`/matches/${matchId}/accept`);

      if (response.success && response.data) {
        console.log(`✅ Partida ${matchId} aceita, sala: ${response.data.room_id}`);
        return {
          success: true,
          room_id: response.data.room_id
        };
      } else {
        console.warn('⚠️ Erro ao aceitar partida:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao aceitar partida'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao aceitar partida:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Rejeita uma partida encontrada
  async rejectMatch(matchId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`❌ Rejeitando partida ${matchId}...`);

      const response = await apiService.post(`/matches/${matchId}/reject`);

      if (response.success) {
        console.log(`✅ Partida ${matchId} rejeitada`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao rejeitar partida:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao rejeitar partida'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao rejeitar partida:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Submete resultado da partida
  async submitMatchResult(result: MatchResult): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`📊 Submetendo resultado da partida ${result.match_id}...`);

      const response = await apiService.post(`/matches/${result.match_id}/result`, result);

      if (response.success) {
        console.log(`✅ Resultado da partida ${result.match_id} submetido`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao submeter resultado:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao submeter resultado'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao submeter resultado:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Contesta resultado da partida
  async disputeMatchResult(matchId: string, reason: string, evidence?: any): Promise<{ success: boolean; dispute_id?: string; error?: string }> {
    try {
      console.log(`⚖️ Contestando resultado da partida ${matchId}...`);

      const response = await apiService.post<{ dispute_id: string }>(`/matches/${matchId}/dispute`, {
        reason,
        evidence
      });

      if (response.success && response.data) {
        console.log(`✅ Contestação criada: ${response.data.dispute_id}`);
        return {
          success: true,
          dispute_id: response.data.dispute_id
        };
      } else {
        console.warn('⚠️ Erro ao contestar resultado:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao contestar resultado'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao contestar resultado:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Obtém histórico de partidas
  async getMatchHistory(gameId?: string, page: number = 1, limit: number = 20): Promise<Match[]> {
    try {
      console.log(`📋 Buscando histórico de partidas (página ${page})...`);

      const params: any = { page, limit };
      if (gameId) params.game_id = gameId;

      const response = await apiService.get<Match[]>('/matches/history', { params });

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} partidas no histórico carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar histórico:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar histórico:', error);
      return [];
    }
  }

  // Obtém partidas ativas
  async getActiveMatches(): Promise<Match[]> {
    try {
      console.log('🎮 Buscando partidas ativas...');

      const response = await apiService.get<Match[]>('/matches/active');

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} partidas ativas carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar partidas ativas:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar partidas ativas:', error);
      return [];
    }
  }

  // Obtém próximas partidas
  async getUpcomingMatches(): Promise<Match[]> {
    try {
      console.log('⏰ Buscando próximas partidas...');

      const response = await apiService.get<Match[]>('/matches/upcoming');

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} próximas partidas carregadas`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar próximas partidas:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar próximas partidas:', error);
      return [];
    }
  }
}

// Instância singleton
export const realMatchService = new RealMatchService();

// Funções de compatibilidade
export const getUserMatches = realMatchService.getUserMatches.bind(realMatchService);
export const getMatchById = realMatchService.getMatchById.bind(realMatchService);
export const startMatchmaking = realMatchService.startMatchmaking.bind(realMatchService);
export const getMatchHistory = realMatchService.getMatchHistory.bind(realMatchService);

import React, { useState } from 'react';
import { Search, X, Gamepad, Users } from 'lucide-react';

interface SearchOverlayProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SearchOverlay({ isOpen, onClose }: SearchOverlayProps) {
  const [searchQuery, setSearchQuery] = useState('');

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50">
      <div className="container max-w-2xl mx-auto px-4 pt-20">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            autoFocus
            type="text"
            placeholder="Buscar jogos ou jogadores..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-gray-800/50 text-white rounded-xl py-4 pl-12 pr-12 focus:outline-none focus:ring-2 focus:ring-indigo-400 backdrop-blur-sm border border-white/10"
          />
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            )}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors ml-2"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="mt-6 space-y-6">
          <div>
            <h3 className="text-white font-semibold mb-3">Jogos Populares</h3>
            <div className="space-y-2">
              {['Foguete Espacial', 'Quiz Master', 'Tetris Battle'].map((game) => (
                <button key={game} className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800">
                  <div className="bg-gray-700 p-2 rounded-lg">
                    <Gamepad className="w-5 h-5 text-indigo-400" />
                  </div>
                  <span className="text-white">{game}</span>
                </button>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-3">Jogadores Online</h3>
            <div className="space-y-2">
              {[
                { name: 'Ana Silva', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' },
                { name: 'Lucas Oliveira', avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80' }
              ].map((player) => (
                <button key={player.name} className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800">
                  <img src={player.avatar} alt={player.name} className="w-10 h-10 rounded-full object-cover" />
                  <span className="text-white">{player.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
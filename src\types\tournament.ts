// Tipos existentes (adaptados conforme necessário)
export interface Participant {
  id: string;
  name: string;
  avatar: string;
  status: 'registered' | 'confirmed' | 'eliminated';
  position?: number;
  score?: number;
}

export interface Match {
  id: string;
  round: number;
  position: number;
  player1?: {
    id: string;
    name: string;
    avatar: string;
    score?: number;
  };
  player2?: {
    id: string;
    name: string;
    avatar: string;
    score?: number;
  };
  winner?: string;
  status: 'scheduled' | 'in_progress' | 'completed';
  scheduledTime?: string;
}

// Novos tipos para o sistema de subchaves e temporadas
export interface TournamentBracket {
  id: string;
  name: string; // Ex: "Grupo A", "Grupo B", "Chave Principal", etc.
  matches: Match[];
  participants: Participant[];
  winnerId?: string; // ID do vencedor que avança para a próxima temporada
  status: 'upcoming' | 'in_progress' | 'completed';
}

export interface TournamentSeason {
  id: string;
  name: string; // Ex: "Fase Inicial", "Oitavas de Final", "Quartas de Final", etc.
  order: number; // Para ordenação
  brackets: TournamentBracket[];
  status: 'upcoming' | 'in_progress' | 'completed';
  startDate?: string;
  endDate?: string;
}

export interface Tournament {
  id: string;
  title: string;
  game: string;
  gameIcon: string;
  format: 'elimination' | 'points';
  status: 'upcoming' | 'in_progress' | 'completed';
  startDate: string;
  endDate: string;
  registrationDeadline: string;
  entryFee: number;
  prizePool: number;
  participants: number;
  maxParticipants: number;
  description: string;
  rules: string;
  organizer: {
    name: string;
    avatar: string;
  };
  prizeDistribution: {
    first: number;
    second: number;
    third: number;
  };
  
  // Novos campos
  seasons: TournamentSeason[];
  currentSeasonId: string;
  totalParticipants: number;
  
  // Campo para a próxima partida do jogador atual (se houver)
  currentPlayerNextMatch?: {
    id: string;
    round: number;
    player1: {
      id: string;
      name: string;
      avatar: string;
    };
    player2: {
      id: string;
      name: string;
      avatar: string;
    };
    scheduledTime: string;
    status: 'scheduled';
  };
  
  // Informações sobre a subchave do usuário atual
  userBracket?: {
    seasonId: string;
    seasonName: string;
    bracketId: string;
    bracketName: string;
    participants: number;
    userPosition: number;
    status: 'upcoming' | 'in_progress' | 'completed';
  };
}

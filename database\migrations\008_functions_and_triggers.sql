-- Migration 008: Functions and Triggers
-- Created: 2024-01-01
-- Description: Creates database functions and triggers for business logic

-- Function to create user profile and wallet when user is created
CREATE OR REPLACE FUNCTION create_user_profile_and_wallet()
RETURNS TRIGGER AS $$
BEGIN
    -- Create user profile
    INSERT INTO user_profiles (user_id)
    VALUES (NEW.id);
    
    -- Create user stats
    INSERT INTO user_stats (user_id)
    VALUES (NEW.id);
    
    -- Create wallet
    INSERT INTO wallets (user_id)
    VALUES (NEW.id);
    
    -- Create affiliate program
    INSERT INTO affiliate_programs (user_id, affiliate_code)
    VALUES (NEW.id, LOWER(NEW.username) || '_' || EXTRACT(EPOCH FROM NOW())::INTEGER);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile and wallet on user creation
CREATE TRIGGER create_user_profile_and_wallet_trigger
    AFTER INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION create_user_profile_and_wallet();

-- Function to update wallet balance
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        IF NEW.type IN ('deposit', 'win', 'commission', 'refund') THEN
            -- Add to balance
            UPDATE wallets 
            SET balance = balance + NEW.amount,
                updated_at = NOW()
            WHERE user_id = NEW.user_id;
            
            -- Update total deposited for deposits
            IF NEW.type = 'deposit' THEN
                UPDATE wallets 
                SET total_deposited = total_deposited + NEW.amount
                WHERE user_id = NEW.user_id;
            END IF;
            
        ELSIF NEW.type IN ('withdrawal', 'bet') THEN
            -- Subtract from balance
            UPDATE wallets 
            SET balance = balance - NEW.amount,
                updated_at = NOW()
            WHERE user_id = NEW.user_id;
            
            -- Update total withdrawn for withdrawals
            IF NEW.type = 'withdrawal' THEN
                UPDATE wallets 
                SET total_withdrawn = total_withdrawn + NEW.amount
                WHERE user_id = NEW.user_id;
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update wallet balance on transaction status change
CREATE TRIGGER update_wallet_balance_trigger
    AFTER UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_wallet_balance();

-- Function to update user stats after match completion
CREATE OR REPLACE FUNCTION update_user_stats_after_match()
RETURNS TRIGGER AS $$
DECLARE
    participant RECORD;
    total_matches INTEGER;
    total_wins INTEGER;
    total_losses INTEGER;
    total_draws INTEGER;
    new_win_rate DECIMAL(5,2);
BEGIN
    -- Only process when match status changes to completed
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        -- Update stats for each participant
        FOR participant IN 
            SELECT user_id, placement, earnings 
            FROM match_participants 
            WHERE match_id = NEW.id
        LOOP
            -- Update match counts
            UPDATE user_stats 
            SET total_matches = total_matches + 1,
                total_earnings = total_earnings + participant.earnings,
                updated_at = NOW()
            WHERE user_id = participant.user_id;
            
            -- Update wins/losses based on placement
            IF participant.placement = 1 THEN
                UPDATE user_stats 
                SET total_wins = total_wins + 1,
                    current_streak = current_streak + 1,
                    best_streak = GREATEST(best_streak, current_streak + 1)
                WHERE user_id = participant.user_id;
            ELSIF participant.placement > 1 THEN
                UPDATE user_stats 
                SET total_losses = total_losses + 1,
                    current_streak = 0
                WHERE user_id = participant.user_id;
            END IF;
            
            -- Recalculate win rate
            SELECT us.total_matches, us.total_wins, us.total_losses, us.total_draws
            INTO total_matches, total_wins, total_losses, total_draws
            FROM user_stats us
            WHERE us.user_id = participant.user_id;
            
            IF total_matches > 0 THEN
                new_win_rate := (total_wins::DECIMAL / total_matches::DECIMAL) * 100;
                UPDATE user_stats 
                SET win_rate = new_win_rate
                WHERE user_id = participant.user_id;
            END IF;
        END LOOP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update user stats after match completion
CREATE TRIGGER update_user_stats_after_match_trigger
    AFTER UPDATE ON matches
    FOR EACH ROW
    EXECUTE FUNCTION update_user_stats_after_match();

-- Function to create commission transaction when referred user makes a transaction
CREATE OR REPLACE FUNCTION create_commission_transaction()
RETURNS TRIGGER AS $$
DECLARE
    referral_record RECORD;
    affiliate_record RECORD;
    commission_amount DECIMAL(12,2);
BEGIN
    -- Only process completed transactions that generate commissions
    IF NEW.status = 'completed' AND OLD.status != 'completed' AND NEW.type IN ('bet', 'deposit') THEN
        -- Check if user was referred
        SELECT * INTO referral_record
        FROM user_referrals 
        WHERE referred_user_id = NEW.user_id AND is_active = true;
        
        IF FOUND THEN
            -- Get affiliate program details
            SELECT * INTO affiliate_record
            FROM affiliate_programs 
            WHERE user_id = referral_record.referrer_user_id AND is_active = true;
            
            IF FOUND THEN
                -- Calculate commission
                commission_amount := NEW.amount * affiliate_record.commission_rate;
                
                -- Create commission transaction
                INSERT INTO commission_transactions (
                    affiliate_user_id,
                    referred_user_id,
                    source_transaction_id,
                    commission_rate,
                    base_amount,
                    commission_amount,
                    status,
                    description
                ) VALUES (
                    referral_record.referrer_user_id,
                    NEW.user_id,
                    NEW.id,
                    affiliate_record.commission_rate,
                    NEW.amount,
                    commission_amount,
                    'pending',
                    'Commission from ' || NEW.type || ' transaction'
                );
                
                -- Update affiliate program stats
                UPDATE affiliate_programs 
                SET pending_earnings = pending_earnings + commission_amount,
                    updated_at = NOW()
                WHERE user_id = referral_record.referrer_user_id;
                
                -- Update referral stats
                UPDATE user_referrals 
                SET total_spent = total_spent + NEW.amount,
                    total_commission_generated = total_commission_generated + commission_amount
                WHERE id = referral_record.id;
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create commission transactions
CREATE TRIGGER create_commission_transaction_trigger
    AFTER UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION create_commission_transaction();

-- Function to update room participant count
CREATE OR REPLACE FUNCTION update_room_participant_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE game_rooms 
        SET current_players = current_players + 1,
            updated_at = NOW()
        WHERE id = NEW.room_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE game_rooms 
        SET current_players = current_players - 1,
            updated_at = NOW()
        WHERE id = OLD.room_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Triggers for room participant count
CREATE TRIGGER update_room_participant_count_insert_trigger
    AFTER INSERT ON room_participants
    FOR EACH ROW
    EXECUTE FUNCTION update_room_participant_count();

CREATE TRIGGER update_room_participant_count_delete_trigger
    AFTER DELETE ON room_participants
    FOR EACH ROW
    EXECUTE FUNCTION update_room_participant_count();

-- Function to update tournament participant count
CREATE OR REPLACE FUNCTION update_tournament_participant_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE tournaments 
        SET current_participants = current_participants + 1,
            updated_at = NOW()
        WHERE id = NEW.tournament_id;
        
        -- Also update bracket count if applicable
        IF NEW.bracket_id IS NOT NULL THEN
            UPDATE tournament_brackets 
            SET current_participants = current_participants + 1,
                updated_at = NOW()
            WHERE id = NEW.bracket_id;
        END IF;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE tournaments 
        SET current_participants = current_participants - 1,
            updated_at = NOW()
        WHERE id = OLD.tournament_id;
        
        -- Also update bracket count if applicable
        IF OLD.bracket_id IS NOT NULL THEN
            UPDATE tournament_brackets 
            SET current_participants = current_participants - 1,
                updated_at = NOW()
            WHERE id = OLD.bracket_id;
        END IF;
        
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Triggers for tournament participant count
CREATE TRIGGER update_tournament_participant_count_insert_trigger
    AFTER INSERT ON tournament_participants
    FOR EACH ROW
    EXECUTE FUNCTION update_tournament_participant_count();

CREATE TRIGGER update_tournament_participant_count_delete_trigger
    AFTER DELETE ON tournament_participants
    FOR EACH ROW
    EXECUTE FUNCTION update_tournament_participant_count();

-- Function to update club member count
CREATE OR REPLACE FUNCTION update_club_member_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE clubs 
        SET current_members = current_members + 1,
            updated_at = NOW()
        WHERE id = NEW.club_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE clubs 
        SET current_members = current_members - 1,
            updated_at = NOW()
        WHERE id = OLD.club_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Triggers for club member count
CREATE TRIGGER update_club_member_count_insert_trigger
    AFTER INSERT ON club_members
    FOR EACH ROW
    EXECUTE FUNCTION update_club_member_count();

CREATE TRIGGER update_club_member_count_delete_trigger
    AFTER DELETE ON club_members
    FOR EACH ROW
    EXECUTE FUNCTION update_club_member_count();

-- Migration 005: Affiliate Program
-- Created: 2024-01-01
-- Description: Creates tables for affiliate program, referrals, and commissions

-- Affiliate program table
CREATE TABLE affiliate_programs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    affiliate_code VARCHAR(20) UNIQUE NOT NULL,
    commission_rate DECIMAL(5,4) DEFAULT 0.15, -- 15% default commission
    total_referrals INTEGER DEFAULT 0,
    total_earnings DECIMAL(12,2) DEFAULT 0.00,
    pending_earnings DECIMAL(12,2) DEFAULT 0.00,
    total_withdrawn DECIMAL(12,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    tier_level INTEGER DEFAULT 1, -- Affiliate tier (1-5)
    tier_benefits JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User referrals table
CREATE TABLE user_referrals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    referrer_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    referred_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    affiliate_code VARCHAR(20) NOT NULL,
    referral_source VARCHAR(50), -- 'direct_link', 'social_media', 'email', etc.
    ip_address INET,
    user_agent TEXT,
    referred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    first_deposit_at TIMESTAMP WITH TIME ZONE,
    first_deposit_amount DECIMAL(10,2),
    total_spent DECIMAL(12,2) DEFAULT 0.00,
    total_commission_generated DECIMAL(12,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Constraints
    UNIQUE(referred_user_id), -- A user can only be referred once
    CONSTRAINT no_self_referral CHECK (referrer_user_id != referred_user_id)
);

-- Commission transactions table
CREATE TABLE commission_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    affiliate_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    referred_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    source_transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
    commission_rate DECIMAL(5,4) NOT NULL,
    base_amount DECIMAL(12,2) NOT NULL, -- Amount that commission is calculated from
    commission_amount DECIMAL(12,2) NOT NULL,
    status transaction_status DEFAULT 'pending',
    description TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT positive_commission CHECK (commission_amount > 0),
    CONSTRAINT valid_commission_rate CHECK (commission_rate > 0 AND commission_rate <= 1)
);

-- Affiliate tier system table
CREATE TABLE affiliate_tiers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tier_level INTEGER UNIQUE NOT NULL,
    tier_name VARCHAR(50) NOT NULL,
    min_referrals INTEGER NOT NULL,
    min_earnings DECIMAL(12,2) NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    benefits JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_tier_level CHECK (tier_level >= 1 AND tier_level <= 10),
    CONSTRAINT valid_commission_rate_tier CHECK (commission_rate > 0 AND commission_rate <= 1)
);

-- Affiliate payouts table
CREATE TABLE affiliate_payouts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    affiliate_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),
    payment_method payment_method NOT NULL,
    payment_details JSONB DEFAULT '{}', -- PIX key, bank details, etc.
    status transaction_status DEFAULT 'pending',
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    external_transaction_id VARCHAR(255),
    admin_notes TEXT,
    
    -- Constraints
    CONSTRAINT minimum_payout CHECK (amount >= 10.00) -- Minimum payout of R$ 10
);

-- Affiliate marketing campaigns table
CREATE TABLE affiliate_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    campaign_code VARCHAR(50) UNIQUE NOT NULL,
    bonus_commission_rate DECIMAL(5,4) DEFAULT 0.00, -- Additional commission for this campaign
    target_audience JSONB DEFAULT '{}',
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Affiliate campaign participants table
CREATE TABLE affiliate_campaign_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES affiliate_campaigns(id) ON DELETE CASCADE,
    affiliate_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_referrals INTEGER DEFAULT 0,
    total_earnings DECIMAL(12,2) DEFAULT 0.00,
    
    -- Constraints
    UNIQUE(campaign_id, affiliate_user_id)
);

-- Referral tracking links table
CREATE TABLE referral_tracking_links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    affiliate_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    campaign_id UUID REFERENCES affiliate_campaigns(id) ON DELETE SET NULL,
    link_code VARCHAR(50) UNIQUE NOT NULL,
    original_url TEXT NOT NULL,
    tracking_url TEXT NOT NULL,
    click_count INTEGER DEFAULT 0,
    conversion_count INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,4) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Link click tracking table
CREATE TABLE link_click_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tracking_link_id UUID NOT NULL REFERENCES referral_tracking_links(id) ON DELETE CASCADE,
    ip_address INET,
    user_agent TEXT,
    referrer_url TEXT,
    clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    converted BOOLEAN DEFAULT FALSE,
    converted_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    converted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes
CREATE INDEX idx_affiliate_programs_user_id ON affiliate_programs(user_id);
CREATE INDEX idx_affiliate_programs_affiliate_code ON affiliate_programs(affiliate_code);
CREATE INDEX idx_affiliate_programs_is_active ON affiliate_programs(is_active);
CREATE INDEX idx_affiliate_programs_tier_level ON affiliate_programs(tier_level);

CREATE INDEX idx_user_referrals_referrer_user_id ON user_referrals(referrer_user_id);
CREATE INDEX idx_user_referrals_referred_user_id ON user_referrals(referred_user_id);
CREATE INDEX idx_user_referrals_affiliate_code ON user_referrals(affiliate_code);
CREATE INDEX idx_user_referrals_referred_at ON user_referrals(referred_at);

CREATE INDEX idx_commission_transactions_affiliate_user_id ON commission_transactions(affiliate_user_id);
CREATE INDEX idx_commission_transactions_referred_user_id ON commission_transactions(referred_user_id);
CREATE INDEX idx_commission_transactions_status ON commission_transactions(status);
CREATE INDEX idx_commission_transactions_created_at ON commission_transactions(created_at);

CREATE INDEX idx_affiliate_tiers_tier_level ON affiliate_tiers(tier_level);
CREATE INDEX idx_affiliate_tiers_is_active ON affiliate_tiers(is_active);

CREATE INDEX idx_affiliate_payouts_affiliate_user_id ON affiliate_payouts(affiliate_user_id);
CREATE INDEX idx_affiliate_payouts_status ON affiliate_payouts(status);
CREATE INDEX idx_affiliate_payouts_requested_at ON affiliate_payouts(requested_at);

CREATE INDEX idx_affiliate_campaigns_campaign_code ON affiliate_campaigns(campaign_code);
CREATE INDEX idx_affiliate_campaigns_is_active ON affiliate_campaigns(is_active);
CREATE INDEX idx_affiliate_campaigns_start_date ON affiliate_campaigns(start_date);
CREATE INDEX idx_affiliate_campaigns_end_date ON affiliate_campaigns(end_date);

CREATE INDEX idx_affiliate_campaign_participants_campaign_id ON affiliate_campaign_participants(campaign_id);
CREATE INDEX idx_affiliate_campaign_participants_affiliate_user_id ON affiliate_campaign_participants(affiliate_user_id);

CREATE INDEX idx_referral_tracking_links_affiliate_user_id ON referral_tracking_links(affiliate_user_id);
CREATE INDEX idx_referral_tracking_links_link_code ON referral_tracking_links(link_code);
CREATE INDEX idx_referral_tracking_links_is_active ON referral_tracking_links(is_active);

CREATE INDEX idx_link_click_tracking_tracking_link_id ON link_click_tracking(tracking_link_id);
CREATE INDEX idx_link_click_tracking_clicked_at ON link_click_tracking(clicked_at);
CREATE INDEX idx_link_click_tracking_converted ON link_click_tracking(converted);

-- Apply updated_at triggers
CREATE TRIGGER update_affiliate_programs_updated_at BEFORE UPDATE ON affiliate_programs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_affiliate_campaigns_updated_at BEFORE UPDATE ON affiliate_campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_referral_tracking_links_updated_at BEFORE UPDATE ON referral_tracking_links FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

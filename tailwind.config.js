/** @type {import('tailwindcss').Config} */
import { tailwindTheme } from './src/theme/index';

export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      ...tailwindTheme.theme?.extend,
      animation: {
        fadeIn: 'fadeIn 0.3s ease-out forwards',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: 0, transform: 'translateY(10px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
      },
    },
  },
  plugins: [
    function({ addBase }) {
      addBase({
        'html': { fontSize: '14px' },
        '@screen sm': {
          'html': { fontSize: '16px' },
        },
      })
    },
  ],
};

import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest } from '../types/api';
import { logger, logBusinessEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';
import config from '../config/environment';

/**
 * Generate affiliate code from username
 */
function generateAffiliateCode(username: string): string {
  const timestamp = Date.now().toString(36);
  const cleanUsername = username.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  return `${cleanUsername}_${timestamp}`.substring(0, 20);
}

/**
 * Get affiliate statistics
 */
export const getAffiliateStats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    // Get or create affiliate record
    let { data: affiliate, error: affiliateError } = await supabaseAdmin
      .from('affiliate_programs')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (affiliateError && affiliateError.code === 'PGRST116') {
      // Create affiliate record if doesn't exist
      const { data: newAffiliate, error: createError } = await supabaseAdmin
        .from('affiliate_programs')
        .insert({
          user_id: user.id,
          affiliate_code: generateAffiliateCode(user.username),
          commission_rate: config.DEFAULT_COMMISSION_RATE || 0.15
        })
        .select('*')
        .single();

      if (createError || !newAffiliate) {
        logger.error('Failed to create affiliate record:', createError);
        const response: ApiResponse = {
          success: false,
          error: 'Failed to create affiliate record'
        };
        res.status(500).json(response);
        return;
      }

      affiliate = newAffiliate;
    } else if (affiliateError) {
      logger.error('Failed to get affiliate record:', affiliateError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve affiliate data'
      };
      res.status(500).json(response);
      return;
    }

    // Get referral statistics
    const { data: referralStats } = await supabaseAdmin
      .from('user_referrals')
      .select('is_active')
      .eq('referrer_user_id', user.id);

    const totalReferrals = referralStats?.length || 0;
    const activeReferrals = referralStats?.filter(r => r.is_active === true).length || 0;

    // Get commission statistics
    const { data: commissionStats } = await supabaseAdmin
      .from('commission_transactions')
      .select('commission_amount, status')
      .eq('affiliate_user_id', user.id);

    const totalEarnings = commissionStats?.reduce((sum, c) => sum + c.commission_amount, 0) || 0;
    const pendingEarnings = commissionStats?.filter(c => c.status === 'pending').reduce((sum, c) => sum + c.commission_amount, 0) || 0;
    const paidEarnings = commissionStats?.filter(c => c.status === 'completed').reduce((sum, c) => sum + c.commission_amount, 0) || 0;

    // Get recent activity
    const { data: recentCommissions } = await supabaseAdmin
      .from('commission_transactions')
      .select(`
        commission_amount, status, created_at, description,
        referred_user_id
      `)
      .eq('affiliate_user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(10);

    // Generate affiliate link
    const baseUrl = config.FRONTEND_URL || 'https://playstrike.com';
    const affiliateLink = `${baseUrl}/register?ref=${affiliate.affiliate_code}`;

    const response: ApiResponse = {
      success: true,
      data: {
        affiliate_code: affiliate.affiliate_code,
        affiliate_link: affiliateLink,
        commission_rate: affiliate.commission_rate,
        total_referrals: totalReferrals,
        active_referrals: activeReferrals,
        total_earnings: totalEarnings,
        pending_earnings: pendingEarnings,
        paid_earnings: paidEarnings,
        recent_activity: recentCommissions || []
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get affiliate stats error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get referrals list
 */
export const getReferrals = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);
    const { status } = req.query;

    // Get affiliate record
    const { data: affiliate, error: affiliateError } = await supabaseAdmin
      .from('affiliate_programs')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (affiliateError || !affiliate) {
      const response: ApiResponse = {
        success: false,
        error: 'Affiliate record not found'
      };
      res.status(404).json(response);
      return;
    }

    let query = supabaseAdmin
      .from('user_referrals')
      .select(`
        id, is_active, referred_at, total_spent, total_commission_generated,
        referred_user:users!referred_user_id (username, display_name, avatar_url, created_at)
      `, { count: 'exact' })
      .eq('referrer_user_id', user.id);

    // Apply filters
    if (status) {
      query = query.eq('is_active', status === 'active');
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('referred_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: referrals, error, count } = await query;

    if (error) {
      logger.error('Failed to get referrals:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve referrals'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        referrals: referrals || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get referrals error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get earnings history
 */
export const getEarnings = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { page, limit } = extractPagination(req.query);
    const { status } = req.query;

    // Get affiliate record
    const { data: affiliate, error: affiliateError } = await supabaseAdmin
      .from('affiliate_programs')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (affiliateError || !affiliate) {
      const response: ApiResponse = {
        success: false,
        error: 'Affiliate record not found'
      };
      res.status(404).json(response);
      return;
    }

    let query = supabaseAdmin
      .from('commission_transactions')
      .select(`
        id, commission_amount, status, created_at, processed_at, description,
        referred_user:users!referred_user_id (username, display_name)
      `, { count: 'exact' })
      .eq('affiliate_user_id', user.id);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: earnings, error, count } = await query;

    if (error) {
      logger.error('Failed to get earnings:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve earnings'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        earnings: earnings || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get earnings error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get affiliate link
 */
export const getAffiliateLink = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    // Get affiliate record
    const { data: affiliate, error: affiliateError } = await supabaseAdmin
      .from('affiliate_programs')
      .select('affiliate_code')
      .eq('user_id', user.id)
      .single();

    if (affiliateError || !affiliate) {
      const response: ApiResponse = {
        success: false,
        error: 'Affiliate record not found'
      };
      res.status(404).json(response);
      return;
    }

    const baseUrl = config.FRONTEND_URL || 'https://playstrike.com';
    const affiliateLink = `${baseUrl}/register?ref=${affiliate.affiliate_code}`;

    const response: ApiResponse = {
      success: true,
      data: {
        affiliate_code: affiliate.affiliate_code,
        affiliate_link: affiliateLink,
        qr_code_url: `${baseUrl}/api/affiliate/qr/${affiliate.affiliate_code}`
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get affiliate link error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Withdraw affiliate commissions
 */
export const withdrawCommissions = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { amount } = req.body;

    // Get affiliate record
    const { data: affiliate, error: affiliateError } = await supabaseAdmin
      .from('affiliate_programs')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (affiliateError || !affiliate) {
      const response: ApiResponse = {
        success: false,
        error: 'Affiliate record not found'
      };
      res.status(404).json(response);
      return;
    }

    // Calculate available balance from pending earnings
    const availableBalance = affiliate.pending_earnings || 0;

    // Validate withdrawal amount
    const minPayout = config.MIN_PAYOUT_AMOUNT || 10.00;
    if (amount < minPayout) {
      const response: ApiResponse = {
        success: false,
        error: `Minimum withdrawal amount is R$ ${minPayout}`
      };
      res.status(400).json(response);
      return;
    }

    if (amount > availableBalance) {
      const response: ApiResponse = {
        success: false,
        error: 'Insufficient available balance'
      };
      res.status(400).json(response);
      return;
    }

    // Create withdrawal request
    const { data: withdrawal, error: withdrawalError } = await supabaseAdmin
      .from('affiliate_payouts')
      .insert({
        affiliate_user_id: user.id,
        amount,
        status: 'pending',
        payment_method: 'pix' // Default to PIX for now
      })
      .select('*')
      .single();

    if (withdrawalError || !withdrawal) {
      logger.error('Failed to create withdrawal request:', withdrawalError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create withdrawal request'
      };
      res.status(500).json(response);
      return;
    }

    // Update affiliate balance
    await supabaseAdmin
      .from('affiliate_programs')
      .update({
        pending_earnings: availableBalance - amount,
        total_withdrawn: (affiliate.total_withdrawn || 0) + amount
      })
      .eq('id', affiliate.id);

    logBusinessEvent('affiliate_withdrawal_requested', user.id, {
      withdrawal_id: withdrawal.id,
      amount
    });

    const response: ApiResponse = {
      success: true,
      message: 'Withdrawal request submitted successfully',
      data: {
        withdrawal: {
          id: withdrawal.id,
          amount: withdrawal.amount,
          status: withdrawal.status,
          created_at: withdrawal.created_at
        }
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Withdraw commissions error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get current commission rate
 */
export const getCommissionRate = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;

    // Get affiliate record
    const { data: affiliate, error: affiliateError } = await supabaseAdmin
      .from('affiliate_programs')
      .select('commission_rate, tier_level')
      .eq('user_id', user.id)
      .single();

    if (affiliateError || !affiliate) {
      const response: ApiResponse = {
        success: false,
        error: 'Affiliate record not found'
      };
      res.status(404).json(response);
      return;
    }

    // Get tier information
    const tiers = [
      { name: 'Bronze', min_referrals: 0, commission_rate: 0.15, level: 1 },
      { name: 'Silver', min_referrals: 10, commission_rate: 0.18, level: 2 },
      { name: 'Gold', min_referrals: 25, commission_rate: 0.20, level: 3 },
      { name: 'Platinum', min_referrals: 50, commission_rate: 0.25, level: 4 }
    ];

    const currentTier = tiers.find(t => t.level === (affiliate.tier_level || 1)) || tiers[0];

    const response: ApiResponse = {
      success: true,
      data: {
        current_rate: affiliate.commission_rate,
        current_tier: currentTier.name,
        current_tier_level: affiliate.tier_level || 1,
        tiers
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get commission rate error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};



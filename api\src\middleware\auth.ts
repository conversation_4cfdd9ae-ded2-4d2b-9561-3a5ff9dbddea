import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { supabaseAdmin, createUserClient } from '../config/database';
import { AuthenticatedRequest, ApiResponse } from '../types/api';
import config from '../config/environment';
import { logger } from '../utils/logger';

// JWT payload interface
interface JwtPayload {
  sub: string; // user ID
  email: string;
  username: string;
  role?: string;
  iat: number;
  exp: number;
}

// Extract token from request headers
const extractToken = (req: Request): string | null => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return null;
  }

  // Support both "Bearer token" and "token" formats
  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return authHeader;
};

// Verify JWT token
const verifyToken = (token: string): JwtPayload | null => {
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET) as JwtPayload;
    return decoded;
  } catch (error) {
    logger.warn('Invalid JWT token:', error);
    return null;
  }
};

// Get user from database
const getUserFromDatabase = async (userId: string) => {
  try {
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('id, email, username, display_name, status')
      .eq('id', userId)
      .eq('status', 'active')
      .single();

    if (error || !user) {
      return null;
    }

    return user;
  } catch (error) {
    logger.error('Error fetching user from database:', error);
    return null;
  }
};

// Main authentication middleware
export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);

    if (!token) {
      const response: ApiResponse = {
        success: false,
        error: 'Authentication token required'
      };
      res.status(401).json(response);
      return;
    }

    // Verify JWT token
    const payload = verifyToken(token);

    if (!payload) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid or expired token'
      };
      res.status(401).json(response);
      return;
    }

    // Get user from database to ensure they still exist and are active
    const user = await getUserFromDatabase(payload.sub);

    if (!user) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found or inactive'
      };
      res.status(401).json(response);
      return;
    }

    // Add user information to request
    req.user = {
      id: user.id,
      email: user.email,
      username: user.username,
      role: payload.role || 'user'
    };

    // Log authentication for audit
    logger.info(`User authenticated: ${user.username} (${user.id})`);

    next();
  } catch (error) {
    logger.error('Authentication middleware error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Authentication failed'
    };
    res.status(500).json(response);
  }
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuthenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);

    if (!token) {
      next();
      return;
    }

    const payload = verifyToken(token);

    if (payload) {
      const user = await getUserFromDatabase(payload.sub);

      if (user) {
        req.user = {
          id: user.id,
          email: user.email,
          username: user.username,
          role: payload.role || 'user'
        };
      }
    }

    next();
  } catch (error) {
    logger.error('Optional authentication middleware error:', error);
    next(); // Continue without authentication
  }
};

// Admin role middleware
export const requireAdmin = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    const response: ApiResponse = {
      success: false,
      error: 'Authentication required'
    };
    res.status(401).json(response);
    return;
  }

  if (req.user.role !== 'admin') {
    const response: ApiResponse = {
      success: false,
      error: 'Admin access required'
    };
    res.status(403).json(response);
    return;
  }

  next();
};

// Moderator role middleware (admin or moderator)
export const requireModerator = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    const response: ApiResponse = {
      success: false,
      error: 'Authentication required'
    };
    res.status(401).json(response);
    return;
  }

  if (!['admin', 'moderator'].includes(req.user.role || '')) {
    const response: ApiResponse = {
      success: false,
      error: 'Moderator access required'
    };
    res.status(403).json(response);
    return;
  }

  next();
};

// Check if user owns resource
export const requireOwnership = (resourceUserIdField: string = 'user_id') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      const response: ApiResponse = {
        success: false,
        error: 'Authentication required'
      };
      res.status(401).json(response);
      return;
    }

    const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];

    if (!resourceUserId) {
      const response: ApiResponse = {
        success: false,
        error: 'Resource user ID not found'
      };
      res.status(400).json(response);
      return;
    }

    if (req.user.id !== resourceUserId && req.user.role !== 'admin') {
      const response: ApiResponse = {
        success: false,
        error: 'Access denied: You can only access your own resources'
      };
      res.status(403).json(response);
      return;
    }

    next();
  };
};

// Generate JWT token
export const generateToken = (user: any): string => {
  const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
    sub: user.id,
    email: user.email,
    username: user.username,
    role: user.role || 'user'
  };

  return jwt.sign(payload, config.JWT_SECRET, {
    expiresIn: config.JWT_EXPIRES_IN
  } as jwt.SignOptions);
};

// Generate refresh token
export const generateRefreshToken = (user: any): string => {
  const payload = {
    sub: user.id,
    type: 'refresh'
  };

  return jwt.sign(payload, config.JWT_SECRET, {
    expiresIn: config.REFRESH_TOKEN_EXPIRES_IN
  } as jwt.SignOptions);
};

// Verify refresh token
export const verifyRefreshToken = (token: string): { sub: string } | null => {
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET) as any;

    if (decoded.type !== 'refresh') {
      return null;
    }

    return { sub: decoded.sub };
  } catch (error) {
    logger.warn('Invalid refresh token:', error);
    return null;
  }
};

// Middleware to check if user is banned or suspended
export const checkUserStatus = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  if (!req.user) {
    next();
    return;
  }

  try {
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('status')
      .eq('id', req.user.id)
      .single();

    if (error || !user) {
      const response: ApiResponse = {
        success: false,
        error: 'User not found'
      };
      res.status(404).json(response);
      return;
    }

    if (user.status === 'banned') {
      const response: ApiResponse = {
        success: false,
        error: 'Account has been banned'
      };
      res.status(403).json(response);
      return;
    }

    if (user.status === 'suspended') {
      const response: ApiResponse = {
        success: false,
        error: 'Account has been suspended'
      };
      res.status(403).json(response);
      return;
    }

    if (user.status === 'inactive') {
      const response: ApiResponse = {
        success: false,
        error: 'Account is inactive'
      };
      res.status(403).json(response);
      return;
    }

    next();
  } catch (error) {
    logger.error('Error checking user status:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to verify user status'
    };
    res.status(500).json(response);
  }
};

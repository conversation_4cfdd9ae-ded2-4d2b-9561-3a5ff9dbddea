import { apiService } from './apiService';

// Tipos para carteira
export interface WalletBalance {
  balance: number;
  currency: string;
  last_updated: string;
}

export interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'game' | 'tournament' | 'affiliate' | 'bonus' | 'win' | 'bet' | 'commission' | 'refund';
  amount: number;
  description: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  date: string; // Formatted date for display
  created_at: string;
  updated_at: string;
  reference_id?: string;
  metadata?: Record<string, any>;
}

export interface DepositRequest {
  amount: number;
  method: 'pix' | 'credit_card' | 'bank_transfer';
  metadata?: Record<string, any>;
}

export interface WithdrawalRequest {
  amount: number;
  method: 'pix' | 'bank_transfer';
  pix_key?: string;
  bank_details?: {
    bank_code: string;
    agency: string;
    account: string;
    account_type: 'checking' | 'savings';
  };
}

export interface PaymentMethod {
  id: string;
  type: 'pix' | 'credit_card' | 'bank_transfer';
  name: string;
  details: Record<string, any>;
  is_default: boolean;
  created_at: string;
}

class RealWalletService {

  // Helper method to format transaction date
  private formatTransactionDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

      if (diffInHours < 1) {
        return 'Agora';
      } else if (diffInHours < 24) {
        return `${Math.floor(diffInHours)}h atrás`;
      } else if (diffInHours < 48) {
        return 'Ontem';
      } else {
        return date.toLocaleDateString('pt-BR', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        });
      }
    } catch (error) {
      return 'Data inválida';
    }
  }

  // Obtém saldo da carteira
  async getBalance(): Promise<WalletBalance | null> {
    try {
      console.log('💰 Buscando saldo da carteira...');

      const response = await apiService.get<{ wallet: WalletBalance }>('/wallet/balance');

      if (response.success && response.data && response.data.wallet) {
        console.log(`✅ Saldo: R$ ${response.data.wallet.balance.toFixed(2)}`);
        return response.data.wallet;
      } else {
        console.warn('⚠️ Erro ao carregar saldo:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao buscar saldo:', error);
      return null;
    }
  }

  // Obtém histórico de transações
  async getTransactions(page: number = 1, limit: number = 20): Promise<Transaction[]> {
    try {
      console.log(`📋 Buscando transações (página ${page})...`);

      const response = await apiService.get<{ transactions: Transaction[] }>('/wallet/transactions', {
        params: { page, limit }
      });

      if (response.success && response.data && response.data.transactions) {
        console.log(`✅ ${response.data.transactions.length} transações carregadas`);

        // Process transactions to add formatted date
        const processedTransactions = response.data.transactions.map((transaction: any) => ({
          ...transaction,
          date: this.formatTransactionDate(transaction.created_at)
        }));

        return processedTransactions;
      } else {
        console.warn('⚠️ Erro ao carregar transações:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar transações:', error);
      return [];
    }
  }

  // Solicita depósito
  async requestDeposit(depositData: DepositRequest): Promise<{ success: boolean; transaction_id?: string; payment_url?: string; error?: string }> {
    try {
      console.log(`💳 Solicitando depósito de R$ ${depositData.amount} via ${depositData.method}...`);

      const response = await apiService.post<{ transaction_id: string; payment_url?: string }>('/wallet/deposit', depositData);

      if (response.success && response.data) {
        console.log(`✅ Depósito solicitado: ${response.data.transaction_id}`);
        return {
          success: true,
          transaction_id: response.data.transaction_id,
          payment_url: response.data.payment_url
        };
      } else {
        console.warn('⚠️ Erro ao solicitar depósito:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao processar depósito'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao solicitar depósito:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Solicita saque
  async requestWithdrawal(withdrawalData: WithdrawalRequest): Promise<{ success: boolean; transaction_id?: string; error?: string }> {
    try {
      console.log(`💸 Solicitando saque de R$ ${withdrawalData.amount} via ${withdrawalData.method}...`);

      const response = await apiService.post<{ transaction_id: string }>('/wallet/withdrawal', withdrawalData);

      if (response.success && response.data) {
        console.log(`✅ Saque solicitado: ${response.data.transaction_id}`);
        return {
          success: true,
          transaction_id: response.data.transaction_id
        };
      } else {
        console.warn('⚠️ Erro ao solicitar saque:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao processar saque'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao solicitar saque:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Obtém métodos de pagamento
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      console.log('💳 Buscando métodos de pagamento...');

      const response = await apiService.get<PaymentMethod[]>('/wallet/payment-methods');

      if (response.success && response.data) {
        console.log(`✅ ${response.data.length} métodos de pagamento carregados`);
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar métodos de pagamento:', response.error);
        return [];
      }
    } catch (error) {
      console.error('❌ Erro ao buscar métodos de pagamento:', error);
      return [];
    }
  }

  // Adiciona método de pagamento
  async addPaymentMethod(methodData: Partial<PaymentMethod>): Promise<{ success: boolean; method_id?: string; error?: string }> {
    try {
      console.log(`💳 Adicionando método de pagamento ${methodData.type}...`);

      const response = await apiService.post<{ method_id: string }>('/wallet/payment-methods', methodData);

      if (response.success && response.data) {
        console.log(`✅ Método de pagamento adicionado: ${response.data.method_id}`);
        return {
          success: true,
          method_id: response.data.method_id
        };
      } else {
        console.warn('⚠️ Erro ao adicionar método de pagamento:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao adicionar método de pagamento'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao adicionar método de pagamento:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Remove método de pagamento
  async removePaymentMethod(methodId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🗑️ Removendo método de pagamento ${methodId}...`);

      const response = await apiService.delete(`/wallet/payment-methods/${methodId}`);

      if (response.success) {
        console.log(`✅ Método de pagamento ${methodId} removido`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao remover método de pagamento:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao remover método de pagamento'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao remover método de pagamento:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Obtém detalhes de uma transação
  async getTransactionDetails(transactionId: string): Promise<Transaction | null> {
    try {
      console.log(`🔍 Buscando detalhes da transação ${transactionId}...`);

      const response = await apiService.get<Transaction>(`/wallet/transactions/${transactionId}`);

      if (response.success && response.data) {
        console.log(`✅ Detalhes da transação ${transactionId} carregados`);
        return {
          ...response.data,
          date: this.formatTransactionDate(response.data.created_at)
        };
      } else {
        console.warn(`⚠️ Erro ao carregar transação ${transactionId}:`, response.error);
        return null;
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar transação ${transactionId}:`, error);
      return null;
    }
  }

  // Cancela uma transação pendente
  async cancelTransaction(transactionId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`❌ Cancelando transação ${transactionId}...`);

      const response = await apiService.post(`/wallet/transactions/${transactionId}/cancel`);

      if (response.success) {
        console.log(`✅ Transação ${transactionId} cancelada`);
        return { success: true };
      } else {
        console.warn('⚠️ Erro ao cancelar transação:', response.error);
        return {
          success: false,
          error: response.error || 'Erro ao cancelar transação'
        };
      }
    } catch (error) {
      console.error('❌ Erro ao cancelar transação:', error);
      return {
        success: false,
        error: 'Erro de conexão. Tente novamente.'
      };
    }
  }

  // Obtém limites da carteira
  async getWalletLimits(): Promise<{ daily_limit: number; monthly_limit: number; min_withdrawal: number; max_withdrawal: number } | null> {
    try {
      console.log('📊 Buscando limites da carteira...');

      const response = await apiService.get<{ daily_limit: number; monthly_limit: number; min_withdrawal: number; max_withdrawal: number }>('/wallet/limits');

      if (response.success && response.data) {
        console.log('✅ Limites da carteira carregados');
        return response.data;
      } else {
        console.warn('⚠️ Erro ao carregar limites:', response.error);
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao buscar limites:', error);
      return null;
    }
  }
}

// Instância singleton
export const realWalletService = new RealWalletService();

// Funções de compatibilidade
export const getBalance = realWalletService.getBalance.bind(realWalletService);
export const getTransactions = realWalletService.getTransactions.bind(realWalletService);
export const requestDeposit = realWalletService.requestDeposit.bind(realWalletService);
export const requestWithdrawal = realWalletService.requestWithdrawal.bind(realWalletService);

import { Response } from 'express';
import { supabaseAdmin } from '../config/database';
import { ApiResponse, AuthenticatedRequest, GameRoomCreationData } from '../types/api';
import { logger, logBusinessEvent } from '../utils/logger';
import { createPagination, extractPagination } from '../utils/pagination';

/**
 * Get all games
 */
export const getGames = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { type, is_active = 'true' } = req.query;

    let query = supabaseAdmin
      .from('games')
      .select('*')
      .eq('is_active', is_active === 'true');

    if (type) {
      query = query.eq('type', type);
    }

    const { data: games, error } = await query.order('name');

    if (error) {
      logger.error('Failed to get games:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve games'
      };
      res.status(500).json(response);
      return;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        games: games || []
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get games error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get game by ID or slug
 */
export const getGameById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Try to find by ID first, then by slug
    let query = supabaseAdmin
      .from('games')
      .select('*')
      .eq('is_active', true);

    // Check if it's a UUID (ID) or slug
    const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);

    if (isUuid) {
      query = query.eq('id', id);
    } else {
      query = query.eq('slug', id);
    }

    const { data: game, error } = await query.single();

    if (error || !game) {
      const response: ApiResponse = {
        success: false,
        error: 'Game not found'
      };
      res.status(404).json(response);
      return;
    }

    // Get game statistics
    const { data: stats } = await supabaseAdmin
      .from('game_statistics')
      .select('*')
      .eq('game_id', game.id)
      .eq('date', new Date().toISOString().split('T')[0])
      .single();

    // Get active rooms count
    const { count: activeRooms } = await supabaseAdmin
      .from('game_rooms')
      .select('*', { count: 'exact', head: true })
      .eq('game_id', game.id)
      .in('status', ['waiting', 'starting']);

    // Get leaderboard (top 10 players)
    const { data: leaderboard } = await supabaseAdmin
      .from('global_rankings')
      .select(`
        position, points,
        users (username, display_name, avatar_url)
      `)
      .eq('game_id', game.id)
      .eq('ranking_type', 'global')
      .order('position')
      .limit(10);

    const response: ApiResponse = {
      success: true,
      data: {
        game,
        statistics: stats || null,
        active_rooms: activeRooms || 0,
        leaderboard: leaderboard || []
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get game by ID error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get game rooms
 */
export const getGameRooms = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { page, limit } = extractPagination(req.query);
    const { status, type, is_public = 'true' } = req.query;

    let query = supabaseAdmin
      .from('game_rooms')
      .select(`
        *,
        games (name, slug),
        users (username, display_name, avatar_url),
        room_participants (
          user_id, is_ready, joined_at,
          users (username, display_name, avatar_url)
        )
      `, { count: 'exact' })
      .eq('game_id', id);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (type) {
      query = query.eq('type', type);
    }
    if (is_public === 'true') {
      query = query.eq('is_public', true);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: rooms, error, count } = await query;

    if (error) {
      logger.error('Failed to get game rooms:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve game rooms'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        rooms: rooms || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get game rooms error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get room by ID
 */
export const getRoomById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { roomId } = req.params;

    const { data: room, error } = await supabaseAdmin
      .from('game_rooms')
      .select(`
        *,
        games (id, name, slug, icon_url),
        users!game_rooms_host_user_id_fkey (username, display_name, avatar_url),
        room_participants (
          user_id, is_ready, joined_at,
          users (username, display_name, avatar_url)
        ),
        room_chat_messages (
          id, message, message_type, created_at,
          users (username, display_name, avatar_url)
        )
      `)
      .eq('id', roomId)
      .single();

    if (error || !room) {
      const response: ApiResponse = {
        success: false,
        error: 'Room not found'
      };
      res.status(404).json(response);
      return;
    }

    // Sort chat messages by creation time
    if (room.room_chat_messages) {
      room.room_chat_messages.sort((a: any, b: any) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
    }

    const response: ApiResponse = {
      success: true,
      data: {
        room
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get room by ID error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Join a room
 */
export const joinRoom = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const { roomId } = req.params;
    const { password } = req.body;

    // Get room details
    const { data: room, error: roomError } = await supabaseAdmin
      .from('game_rooms')
      .select(`
        *,
        room_participants (user_id)
      `)
      .eq('id', roomId)
      .single();

    if (roomError || !room) {
      const response: ApiResponse = {
        success: false,
        error: 'Room not found'
      };
      res.status(404).json(response);
      return;
    }

    // Check if user is already in the room
    const isAlreadyParticipant = room.room_participants?.some((p: any) => p.user_id === user.id);

    if (isAlreadyParticipant) {
      const response: ApiResponse = {
        success: true,
        message: 'Already in room'
      };
      res.status(200).json(response);
      return;
    }

    // Check if room is full using current_players from the room record
    if (room.current_players >= room.max_players) {
      const response: ApiResponse = {
        success: false,
        error: 'Room is full'
      };
      res.status(400).json(response);
      return;
    }

    // Check password if room is private
    if (!room.is_public && room.password_hash) {
      if (!password) {
        const response: ApiResponse = {
          success: false,
          error: 'Password required for private room'
        };
        res.status(400).json(response);
        return;
      }

      // TODO: Verify password hash
      // For now, we'll skip password verification
    }

    // Check if user has enough balance
    const { data: wallet } = await supabaseAdmin
      .from('wallets')
      .select('balance')
      .eq('user_id', user.id)
      .single();

    if (!wallet || wallet.balance < room.entry_fee) {
      const response: ApiResponse = {
        success: false,
        error: 'Insufficient balance'
      };
      res.status(400).json(response);
      return;
    }

    // Deduct entry fee from wallet
    const { error: walletError } = await supabaseAdmin
      .from('wallets')
      .update({
        balance: wallet.balance - room.entry_fee
      })
      .eq('user_id', user.id);

    if (walletError) {
      logger.error('Failed to deduct entry fee:', walletError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to process payment'
      };
      res.status(500).json(response);
      return;
    }

    // Add user to room participants
    const { error: participantError } = await supabaseAdmin
      .from('room_participants')
      .insert({
        room_id: roomId,
        user_id: user.id,
        is_ready: false
      });

    if (participantError) {
      logger.error('Failed to add participant:', participantError);

      // Refund the entry fee
      await supabaseAdmin
        .from('wallets')
        .update({
          balance: wallet.balance
        })
        .eq('user_id', user.id);

      // Check if it's a constraint violation (room full)
      let errorMessage = 'Failed to join room';
      let statusCode = 500;

      if (participantError.code === '23514' && participantError.message?.includes('current_players_valid')) {
        errorMessage = 'Room is full';
        statusCode = 400;
      } else if (participantError.code === '23505') {
        errorMessage = 'You are already in this room';
        statusCode = 400;
      }

      const response: ApiResponse = {
        success: false,
        error: errorMessage
      };
      res.status(statusCode).json(response);
      return;
    }

    // Create transaction record
    const { data: userWallet } = await supabaseAdmin
      .from('wallets')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (userWallet) {
      await supabaseAdmin
        .from('transactions')
        .insert({
          user_id: user.id,
          wallet_id: userWallet.id,
          type: 'bet',
          amount: room.entry_fee,
          status: 'completed',
          description: `Entry fee for room: ${room.name}`
        });
    }

    const response: ApiResponse = {
      success: true,
      data: {
        message: 'Successfully joined room'
      }
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Join room error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Create game room
 */
export const createGameRoom = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = req.user!;
    const roomData: GameRoomCreationData = req.body;

    // Verify game exists and is active
    const { data: game, error: gameError } = await supabaseAdmin
      .from('games')
      .select('id, name, is_active, min_players, max_players')
      .eq('id', roomData.game_id)
      .eq('is_active', true)
      .single();

    if (gameError || !game) {
      const response: ApiResponse = {
        success: false,
        error: 'Game not found or inactive'
      };
      res.status(404).json(response);
      return;
    }

    // Validate max_players against game limits
    if (roomData.max_players < game.min_players || roomData.max_players > game.max_players) {
      const response: ApiResponse = {
        success: false,
        error: `Max players must be between ${game.min_players} and ${game.max_players}`
      };
      res.status(400).json(response);
      return;
    }

    // Check if user has sufficient balance for entry fee
    if (roomData.entry_fee > 0) {
      const { data: wallet } = await supabaseAdmin
        .from('wallets')
        .select('balance, frozen_balance')
        .eq('user_id', user.id)
        .single();

      if (!wallet || (wallet.balance - wallet.frozen_balance) < roomData.entry_fee) {
        const response: ApiResponse = {
          success: false,
          error: 'Insufficient balance'
        };
        res.status(400).json(response);
        return;
      }
    }

    // Generate room code
    const roomCode = generateRoomCode();

    // Create room
    const { data: room, error: roomError } = await supabaseAdmin
      .from('game_rooms')
      .insert({
        game_id: roomData.game_id,
        host_user_id: user.id,
        name: roomData.name,
        type: roomData.type,
        entry_fee: roomData.entry_fee,
        max_players: roomData.max_players,
        current_players: 0, // Start with 0, will be incremented by trigger when host is added
        is_public: roomData.is_public,
        password_hash: roomData.password ? await hashPassword(roomData.password) : null,
        game_settings: roomData.game_settings || {},
        room_code: roomCode,
        scheduled_start_time: roomData.scheduled_start_time
      })
      .select('*')
      .single();

    if (roomError || !room) {
      logger.error('Failed to create game room:', roomError);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to create game room'
      };
      res.status(500).json(response);
      return;
    }

    // Add host as participant (not ready by default)
    await supabaseAdmin
      .from('room_participants')
      .insert({
        room_id: room.id,
        user_id: user.id,
        is_ready: false
      });

    logBusinessEvent('game_room_created', user.id, {
      room_id: room.id,
      game_id: roomData.game_id,
      entry_fee: roomData.entry_fee,
      max_players: roomData.max_players
    });

    const response: ApiResponse = {
      success: true,
      message: 'Game room created successfully',
      data: {
        room: {
          id: room.id,
          name: room.name,
          type: room.type,
          status: room.status,
          entry_fee: room.entry_fee,
          max_players: room.max_players,
          current_players: room.current_players,
          is_public: room.is_public,
          room_code: room.room_code,
          created_at: room.created_at
        }
      }
    };

    res.status(201).json(response);
  } catch (error) {
    logger.error('Create game room error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

/**
 * Get game ranking/leaderboard
 */
export const getGameRanking = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { page, limit } = extractPagination(req.query);
    const { ranking_type = 'global', period_start, period_end } = req.query;

    let query = supabaseAdmin
      .from('global_rankings')
      .select(`
        position, points,
        users (id, username, display_name, avatar_url, country),
        user_stats (total_matches, total_wins, win_rate)
      `, { count: 'exact' })
      .eq('game_id', id)
      .eq('ranking_type', ranking_type);

    // Apply period filters if provided
    if (period_start) {
      query = query.gte('period_start', period_start);
    }
    if (period_end) {
      query = query.lte('period_end', period_end);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('position')
      .range(offset, offset + limit - 1);

    const { data: rankings, error, count } = await query;

    if (error) {
      logger.error('Failed to get game ranking:', error);
      const response: ApiResponse = {
        success: false,
        error: 'Failed to retrieve game ranking'
      };
      res.status(500).json(response);
      return;
    }

    const pagination = createPagination(page, limit, count || 0);

    const response: ApiResponse = {
      success: true,
      data: {
        rankings: rankings || []
      },
      pagination
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Get game ranking error:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error'
    };
    res.status(500).json(response);
  }
};

// Helper functions
const generateRoomCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

const hashPassword = async (password: string): Promise<string> => {
  const bcrypt = require('bcryptjs');
  return await bcrypt.hash(password, 10);
};

-- Migration 012: Referral System Functions
-- Created: 2024-06-08
-- Description: Creates functions to support the referral system

-- Function to increment total referrals for an affiliate
CREATE OR REPLACE FUNCTION increment_total_referrals(affiliate_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    current_count INTEGER;
BEGIN
    -- Get current total referrals
    SELECT COALESCE(total_referrals, 0) INTO current_count
    FROM affiliate_programs
    WHERE user_id = affiliate_user_id;
    
    -- Return incremented value
    RETURN current_count + 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get affiliate stats
CREATE OR REPLACE FUNCTION get_affiliate_stats(affiliate_user_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_referrals', COALESCE(ap.total_referrals, 0),
        'active_referrals', (
            SELECT COUNT(*)
            FROM user_referrals ur
            WHERE ur.referrer_user_id = affiliate_user_id
            AND ur.is_active = true
        ),
        'total_earnings', COALESCE(ap.total_earnings, 0),
        'pending_earnings', COALESCE(ap.pending_earnings, 0),
        'commission_rate', COALESCE(ap.commission_rate, 0.15),
        'tier_level', COALESCE(ap.tier_level, 1),
        'affiliate_code', ap.affiliate_code
    ) INTO result
    FROM affiliate_programs ap
    WHERE ap.user_id = affiliate_user_id;
    
    RETURN COALESCE(result, '{}'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate affiliate code
CREATE OR REPLACE FUNCTION validate_affiliate_code(code TEXT)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'valid', true,
        'user_id', ap.user_id,
        'username', u.username,
        'display_name', u.display_name
    ) INTO result
    FROM affiliate_programs ap
    JOIN users u ON u.id = ap.user_id
    WHERE ap.affiliate_code = code
    AND ap.is_active = true
    AND u.status = 'active';
    
    IF result IS NULL THEN
        result := json_build_object('valid', false);
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

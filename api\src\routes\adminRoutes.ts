import { Router } from 'express';
import {
  getAdminDashboard,
  getUsers,
  updateUserStatus,
  getTransactions,
  getReports,
  getSystemSettings,
  updateSystemSettings,
  getAdminActions,
  processReport
} from '../controllers/adminController';
import { validate, schemas } from '../middleware/validation';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// Apply admin authentication to all routes
router.use(authenticate);
router.use(requireAdmin);

/**
 * @route   GET /api/admin/dashboard
 * @desc    Get admin dashboard data
 * @access  Admin
 */
router.get('/dashboard',
  getAdminDashboard
);

/**
 * @route   GET /api/admin/users
 * @desc    Get all users (admin view)
 * @access  Admin
 */
router.get('/users',
  validate(schemas.pagination, 'query'),
  getUsers
);

/**
 * @route   PUT /api/admin/users/:id/status
 * @desc    Update user status
 * @access  Admin
 */
router.put('/users/:id/status',
  validate(schemas.uuidParam, 'params'),
  updateUserStatus
);

/**
 * @route   GET /api/admin/transactions
 * @desc    Get all transactions (admin view)
 * @access  Admin
 */
router.get('/transactions',
  validate(schemas.pagination, 'query'),
  getTransactions
);

/**
 * @route   GET /api/admin/reports
 * @desc    Get user reports
 * @access  Admin
 */
router.get('/reports',
  validate(schemas.pagination, 'query'),
  getReports
);

/**
 * @route   POST /api/admin/reports/:id/process
 * @desc    Process user report
 * @access  Admin
 */
router.post('/reports/:id/process',
  validate(schemas.uuidParam, 'params'),
  processReport
);

/**
 * @route   GET /api/admin/actions
 * @desc    Get admin actions log
 * @access  Admin
 */
router.get('/actions',
  validate(schemas.pagination, 'query'),
  getAdminActions
);

/**
 * @route   GET /api/admin/system/settings
 * @desc    Get system settings
 * @access  Admin
 */
router.get('/system/settings',
  getSystemSettings
);

/**
 * @route   PUT /api/admin/system/settings
 * @desc    Update system settings
 * @access  Admin
 */
router.put('/system/settings',
  validate(schemas.systemSettings),
  updateSystemSettings
);

export default router;

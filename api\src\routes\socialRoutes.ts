import { Router } from 'express';
import {
  getFriends,
  sendFriendRequest,
  acceptFriendRequest,
  declineFriendRequest,
  removeFriend,
  getFriendRequests,
  getFriendSuggestions,
  challengeFriend,
  getClubs
} from '../controllers/socialController';
import {
  createClub,
  getClubById,
  joinClub,
  leaveClub,
  getClubMembers
} from '../controllers/socialController2';
import {
  getPrivateChatMessages,
  sendPrivateMessage,
  getChatNotifications,
  getRecentConversations,
  markConversationAsRead,
  deleteMessage
} from '../controllers/chatController';
import { validate, schemas } from '../middleware/validation';
import { authenticate } from '../middleware/auth';
import { friendRequestRateLimit, chatRateLimit, clubCreationRateLimit } from '../middleware/rateLimiting';

const router = Router();

// Friends endpoints
/**
 * @route   GET /api/social/friends
 * @desc    Get user's friends list
 * @access  Private
 */
router.get('/friends',
  authenticate,
  validate(schemas.pagination, 'query'),
  getFriends
);

/**
 * @route   POST /api/social/friends/request
 * @desc    Send friend request
 * @access  Private
 */
router.post('/friends/request',
  authenticate,
  friendRequestRateLimit,
  validate(schemas.friendRequest),
  sendFriendRequest
);

/**
 * @route   GET /api/social/friends/requests
 * @desc    Get pending friend requests
 * @access  Private
 */
router.get('/friends/requests',
  authenticate,
  validate(schemas.pagination, 'query'),
  getFriendRequests
);

/**
 * @route   GET /api/social/friends/suggestions
 * @desc    Get friend suggestions
 * @access  Private
 */
router.get('/friends/suggestions',
  authenticate,
  validate(schemas.pagination, 'query'),
  getFriendSuggestions
);

/**
 * @route   POST /api/social/friends/challenge
 * @desc    Challenge friend to match
 * @access  Private
 */
router.post('/friends/challenge',
  authenticate,
  validate(schemas.challenge),
  challengeFriend
);

/**
 * @route   POST /api/social/friends/accept/:requestId
 * @desc    Accept friend request
 * @access  Private
 */
router.post('/friends/accept/:requestId',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  acceptFriendRequest
);

/**
 * @route   POST /api/social/friends/decline/:requestId
 * @desc    Decline friend request
 * @access  Private
 */
router.post('/friends/decline/:requestId',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  declineFriendRequest
);

/**
 * @route   DELETE /api/social/friends/:friendId
 * @desc    Remove friend
 * @access  Private
 */
router.delete('/friends/:friendId',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  removeFriend
);

// Clubs endpoints
/**
 * @route   GET /api/social/clubs
 * @desc    Get clubs
 * @access  Public
 */
router.get('/clubs',
  validate(schemas.pagination, 'query'),
  getClubs
);

/**
 * @route   POST /api/social/clubs
 * @desc    Create club
 * @access  Private
 */
router.post('/clubs',
  authenticate,
  clubCreationRateLimit,
  validate(schemas.clubCreation),
  createClub
);

/**
 * @route   GET /api/social/clubs/:id
 * @desc    Get club by ID
 * @access  Public
 */
router.get('/clubs/:id',
  validate(schemas.uuidParam, 'params'),
  getClubById
);

/**
 * @route   POST /api/social/clubs/:id/join
 * @desc    Join club
 * @access  Private
 */
router.post('/clubs/:id/join',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  joinClub
);

/**
 * @route   POST /api/social/clubs/:id/leave
 * @desc    Leave club
 * @access  Private
 */
router.post('/clubs/:id/leave',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  leaveClub
);

/**
 * @route   GET /api/social/clubs/:id/members
 * @desc    Get club members
 * @access  Public
 */
router.get('/clubs/:id/members',
  validate(schemas.uuidParam, 'params'),
  validate(schemas.pagination, 'query'),
  getClubMembers
);

// Chat endpoints
/**
 * @route   GET /api/social/chat/conversations
 * @desc    Get recent conversations
 * @access  Private
 */
router.get('/chat/conversations',
  authenticate,
  validate(schemas.pagination, 'query'),
  getRecentConversations
);

/**
 * @route   GET /api/social/chat/notifications
 * @desc    Get chat notifications
 * @access  Private
 */
router.get('/chat/notifications',
  authenticate,
  getChatNotifications
);

/**
 * @route   GET /api/social/chat/private/:friendId
 * @desc    Get private chat messages
 * @access  Private
 */
router.get('/chat/private/:friendId',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  validate(schemas.pagination, 'query'),
  getPrivateChatMessages
);

/**
 * @route   POST /api/social/chat/private/:friendId
 * @desc    Send private message
 * @access  Private
 */
router.post('/chat/private/:friendId',
  authenticate,
  chatRateLimit,
  validate(schemas.uuidParam, 'params'),
  validate(schemas.message),
  sendPrivateMessage
);

/**
 * @route   PUT /api/social/chat/private/:friendId/read
 * @desc    Mark conversation as read
 * @access  Private
 */
router.put('/chat/private/:friendId/read',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  markConversationAsRead
);

/**
 * @route   DELETE /api/social/chat/messages/:messageId
 * @desc    Delete message
 * @access  Private
 */
router.delete('/chat/messages/:messageId',
  authenticate,
  validate(schemas.uuidParam, 'params'),
  deleteMessage
);

export default router;

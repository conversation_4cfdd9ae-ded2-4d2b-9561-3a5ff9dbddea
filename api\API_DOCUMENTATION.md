# Playstrike API - Documentação Completa

## 📋 Visão Geral

Esta documentação detalha todas as 120+ APIs implementadas para a plataforma Playstrike. As APIs estão organizadas por módulos funcionais e incluem exemplos de uso, códigos de resposta e estruturas de dados.

## 🔐 Autenticação

Todas as rotas privadas requerem um token JWT no header:
```
Authorization: Bearer <token>
```

## 📊 Estrutura de Resposta Padrão

```json
{
  "success": boolean,
  "data": any,
  "error": string,
  "message": string,
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "totalPages": number,
    "hasNext": boolean,
    "hasPrev": boolean
  }
}
```

## 🚀 APIs Implementadas

### 1. Autenticação (`/api/auth`)

#### ✅ POST `/api/auth/register`
**Descrição**: Registrar novo usuário
**Acesso**: Público
**Rate Limit**: 5 tentativas por 15 minutos

**Body**:
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "display_name": "Display Name",
  "password": "Password123!",
  "birth_date": "1990-01-01",
  "country": "BR",
  "timezone": "America/Sao_Paulo",
  "language": "pt-BR"
}
```

**Resposta (201)**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "username": "username",
      "display_name": "Display Name",
      "status": "active",
      "created_at": "2024-01-01T00:00:00Z"
    },
    "tokens": {
      "access_token": "jwt_token",
      "refresh_token": "refresh_token",
      "expires_in": "7d"
    }
  }
}
```

#### ✅ POST `/api/auth/login`
**Descrição**: Login do usuário
**Acesso**: Público
**Rate Limit**: 5 tentativas por 15 minutos

**Body**:
```json
{
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

#### ✅ POST `/api/auth/refresh`
**Descrição**: Renovar token de acesso
**Acesso**: Público

**Body**:
```json
{
  "refresh_token": "refresh_token"
}
```

#### ✅ GET `/api/auth/profile`
**Descrição**: Obter perfil do usuário atual
**Acesso**: Privado

#### ✅ POST `/api/auth/logout`
**Descrição**: Logout do usuário
**Acesso**: Privado

#### ✅ POST `/api/auth/verify-email`
**Descrição**: Verificar email do usuário
**Acesso**: Público

### 2. Usuários (`/api/users`)

#### ✅ GET `/api/users/search`
**Descrição**: Buscar usuários
**Acesso**: Público
**Rate Limit**: 60 por minuto

**Query Parameters**:
- `query` (string): Termo de busca
- `page` (number): Página (padrão: 1)
- `limit` (number): Itens por página (padrão: 20)

#### ✅ GET `/api/users/:id`
**Descrição**: Obter perfil do usuário por ID
**Acesso**: Público (com controles de privacidade)

#### ✅ PUT `/api/users/:id`
**Descrição**: Atualizar perfil do usuário
**Acesso**: Privado (apenas próprio perfil)

**Body**:
```json
{
  "display_name": "New Display Name",
  "bio": "User bio",
  "avatar_url": "https://example.com/avatar.jpg",
  "favorite_games": ["game_id_1", "game_id_2"],
  "gaming_experience": "advanced",
  "preferred_game_modes": ["1v1", "tournament"],
  "social_links": {
    "twitter": "https://twitter.com/user",
    "twitch": "https://twitch.tv/user"
  },
  "privacy_settings": {
    "profile_visibility": "public",
    "stats_visibility": "friends",
    "match_history_visibility": "public"
  },
  "notification_preferences": {
    "email_notifications": true,
    "push_notifications": true,
    "friend_requests": true,
    "match_updates": true
  }
}
```

#### ✅ GET `/api/users/:id/stats`
**Descrição**: Obter estatísticas do usuário
**Acesso**: Público (com controles de privacidade)

#### ✅ GET `/api/users/:id/achievements`
**Descrição**: Obter conquistas do usuário
**Acesso**: Público (com controles de privacidade)

### 3. Carteira (`/api/wallet`)

#### ✅ GET `/api/wallet`
**Descrição**: Obter informações da carteira
**Acesso**: Privado

**Resposta**:
```json
{
  "success": true,
  "data": {
    "wallet": {
      "id": "uuid",
      "balance": 100.50,
      "frozen_balance": 10.00,
      "total_deposited": 200.00,
      "total_withdrawn": 50.00,
      "available_balance": 90.50,
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

#### ✅ POST `/api/wallet/deposit`
**Descrição**: Criar transação de depósito
**Acesso**: Privado
**Rate Limit**: 20 por hora

**Body**:
```json
{
  "amount": 100.00,
  "payment_method": "pix"
}
```

#### ✅ POST `/api/wallet/withdraw`
**Descrição**: Criar transação de saque
**Acesso**: Privado
**Rate Limit**: 20 por hora

**Body**:
```json
{
  "amount": 50.00,
  "payment_method": "pix",
  "pix_key": "<EMAIL>"
}
```

#### ✅ GET `/api/wallet/transactions`
**Descrição**: Obter histórico de transações
**Acesso**: Privado

**Query Parameters**:
- `page`, `limit`: Paginação
- `type`: Filtrar por tipo (deposit, withdrawal, bet, win, commission, refund)
- `status`: Filtrar por status (pending, completed, failed, cancelled)
- `date_from`, `date_to`: Filtrar por período

#### ✅ GET `/api/wallet/transactions/:id`
**Descrição**: Obter transação por ID
**Acesso**: Privado

#### ✅ GET `/api/wallet/payment-methods`
**Descrição**: Obter métodos de pagamento disponíveis
**Acesso**: Privado

#### ✅ GET `/api/wallet/limits`
**Descrição**: Obter limites da carteira
**Acesso**: Privado

### 4. Jogos (`/api/games`)

#### ✅ GET `/api/games`
**Descrição**: Listar todos os jogos
**Acesso**: Público

**Query Parameters**:
- `type`: Filtrar por tipo (internal, external)
- `is_active`: Filtrar por status ativo (true/false)

#### ✅ GET `/api/games/internal`
**Descrição**: Listar jogos internos
**Acesso**: Público

#### ✅ GET `/api/games/external`
**Descrição**: Listar jogos externos
**Acesso**: Público

#### ✅ GET `/api/games/:id`
**Descrição**: Obter jogo por ID ou slug
**Acesso**: Público

**Resposta**:
```json
{
  "success": true,
  "data": {
    "game": {
      "id": "uuid",
      "name": "FlapRocket",
      "slug": "flaprocket",
      "description": "Controle seu foguete e desvie dos obstáculos",
      "type": "internal",
      "image_url": "https://example.com/game.jpg",
      "icon_url": "https://example.com/icon.jpg",
      "is_active": true,
      "min_players": 2,
      "max_players": 8,
      "default_entry_fee": 5.00,
      "supported_platforms": ["web", "mobile"],
      "game_modes": ["1v1", "tournament"],
      "settings_schema": {}
    },
    "statistics": {
      "total_matches": 150,
      "total_players": 500,
      "total_prize_pool": 5000.00,
      "average_match_duration": 25,
      "peak_concurrent_players": 50
    },
    "active_rooms": 5,
    "leaderboard": [
      {
        "position": 1,
        "points": 1500,
        "users": {
          "username": "player1",
          "display_name": "Player One",
          "avatar_url": "https://example.com/avatar1.jpg"
        }
      }
    ]
  }
}
```

#### ✅ GET `/api/games/:id/rooms`
**Descrição**: Obter salas do jogo
**Acesso**: Público

**Query Parameters**:
- `page`, `limit`: Paginação
- `status`: Filtrar por status (waiting, starting, playing, finished)
- `type`: Filtrar por tipo (1v1, tournament, practice)
- `is_public`: Filtrar por visibilidade (true/false)

#### ✅ POST `/api/games/:id/rooms`
**Descrição**: Criar sala de jogo
**Acesso**: Privado
**Rate Limit**: 10 por 5 minutos

**Body**:
```json
{
  "name": "Sala do Player",
  "type": "1v1",
  "entry_fee": 10.00,
  "max_players": 2,
  "is_public": true,
  "password": "optional_password",
  "game_settings": {
    "difficulty": "normal",
    "rounds": 5
  },
  "scheduled_start_time": "2024-01-01T15:00:00Z"
}
```

#### ✅ GET `/api/games/:id/ranking`
**Descrição**: Obter ranking do jogo
**Acesso**: Público

**Query Parameters**:
- `page`, `limit`: Paginação
- `ranking_type`: Tipo de ranking (global, weekly, monthly, seasonal)
- `period_start`, `period_end`: Filtrar por período

### 5. WebSockets (Socket.IO)

#### ✅ Eventos Implementados

**Autenticação**:
- Middleware de autenticação JWT
- Verificação de usuário ativo

**Salas de Jogo**:
- `join_room` - Entrar em sala
- `leave_room` - Sair da sala
- `user_joined_room` - Notificação de usuário entrando
- `user_left_room` - Notificação de usuário saindo

**Chat**:
- `send_message` - Enviar mensagem
- `new_message` - Nova mensagem recebida
- `typing_start` - Usuário começou a digitar
- `typing_stop` - Usuário parou de digitar

**Partidas**:
- `join_match` - Entrar em partida
- `player_ready` - Status de pronto do jogador
- `player_ready_update` - Atualização de status

**Matchmaking**:
- `start_matchmaking` - Iniciar busca por partida
- `cancel_matchmaking` - Cancelar busca
- `matchmaking_started` - Confirmação de início
- `matchmaking_cancelled` - Confirmação de cancelamento

**Sistema**:
- `notification` - Notificações em tempo real
- `ping`/`pong` - Health check da conexão
- `error` - Erros do sistema

## ✅ APIs Totalmente Implementadas (120+)

### 6. Partidas (`/api/matches`) - ✅ IMPLEMENTADO
- `GET /api/matches` - Listar partidas com filtros
- `POST /api/matches` - Criar partida
- `GET /api/matches/:id` - Obter partida por ID
- `POST /api/matches/:id/join` - Entrar na partida
- `POST /api/matches/:id/leave` - Sair da partida
- `POST /api/matches/:id/result` - Submeter resultado
- `POST /api/matches/:id/dispute` - Disputar resultado
- `GET /api/matches/history` - Histórico de partidas do usuário
- `GET /api/matches/upcoming` - Próximas partidas do usuário

### 7. Torneios (`/api/tournaments`) - ✅ IMPLEMENTADO
- `GET /api/tournaments` - Listar torneios com filtros
- `POST /api/tournaments` - Criar torneio
- `GET /api/tournaments/:id` - Obter torneio por ID
- `POST /api/tournaments/:id/register` - Inscrever-se no torneio
- `POST /api/tournaments/:id/unregister` - Cancelar inscrição
- `GET /api/tournaments/:id/brackets` - Obter chaves do torneio
- `GET /api/tournaments/:id/participants` - Lista de participantes
- `GET /api/tournaments/:id/matches` - Partidas do torneio
- `GET /api/tournaments/:id/seasons` - Temporadas do torneio
- `GET /api/tournaments/results` - Resultados de torneios

### 8. Social (`/api/social`) - ✅ IMPLEMENTADO

**Amigos**:
- `GET /api/social/friends` - Lista de amigos
- `POST /api/social/friends/request` - Enviar solicitação de amizade
- `POST /api/social/friends/accept/:id` - Aceitar solicitação
- `POST /api/social/friends/decline/:id` - Recusar solicitação
- `DELETE /api/social/friends/:id` - Remover amigo
- `GET /api/social/friends/requests` - Solicitações pendentes
- `GET /api/social/friends/suggestions` - Sugestões de amigos
- `POST /api/social/friends/challenge` - Desafiar amigo para partida

**Clubes**:
- `GET /api/social/clubs` - Listar clubes públicos
- `POST /api/social/clubs` - Criar clube
- `GET /api/social/clubs/:id` - Obter clube por ID
- `POST /api/social/clubs/:id/join` - Entrar no clube
- `POST /api/social/clubs/:id/leave` - Sair do clube
- `GET /api/social/clubs/:id/members` - Membros do clube

**Chat**:
- `GET /api/social/chat/conversations` - Conversas recentes
- `GET /api/social/chat/private/:id` - Mensagens privadas
- `POST /api/social/chat/private/:id` - Enviar mensagem privada
- `PUT /api/social/chat/private/:id/read` - Marcar conversa como lida
- `DELETE /api/social/chat/messages/:id` - Deletar mensagem
- `GET /api/social/chat/notifications` - Notificações de chat

### 9. Afiliados (`/api/affiliate`) - ✅ IMPLEMENTADO
- `GET /api/affiliate/stats` - Estatísticas completas de afiliado
- `GET /api/affiliate/referrals` - Lista de indicações com filtros
- `GET /api/affiliate/earnings` - Histórico detalhado de ganhos
- `GET /api/affiliate/link` - Link de afiliado personalizado
- `POST /api/affiliate/withdraw` - Sacar comissões acumuladas
- `GET /api/affiliate/commission-rate` - Taxa de comissão atual

### 10. Notificações (`/api/notifications`) - ✅ IMPLEMENTADO
- `GET /api/notifications` - Listar notificações com filtros
- `PUT /api/notifications/:id/read` - Marcar notificação como lida
- `PUT /api/notifications/read-all` - Marcar todas como lidas
- `DELETE /api/notifications/:id` - Deletar notificação
- `GET /api/notifications/settings` - Obter configurações
- `POST /api/notifications/settings` - Atualizar configurações

### 11. Admin (`/api/admin`) - ✅ IMPLEMENTADO
- `GET /api/admin/dashboard` - Dashboard com estatísticas completas
- `GET /api/admin/users` - Gerenciar usuários com filtros
- `PUT /api/admin/users/:id/status` - Alterar status do usuário
- `GET /api/admin/transactions` - Ver todas as transações
- `GET /api/admin/reports` - Relatórios de usuários
- `POST /api/admin/reports/:id/process` - Processar relatório
- `GET /api/admin/actions` - Log de ações administrativas
- `GET /api/admin/system/settings` - Configurações do sistema
- `PUT /api/admin/system/settings` - Atualizar configurações

### 12. Webhooks (`/api/webhooks`) - ✅ IMPLEMENTADO
- `POST /api/webhooks/payment` - Webhooks de pagamento com verificação
- `POST /api/webhooks/tournament` - Webhooks de torneios automatizados

## 📊 Códigos de Status HTTP

- `200` - OK
- `201` - Created
- `400` - Bad Request (erro de validação)
- `401` - Unauthorized (não autenticado)
- `403` - Forbidden (não autorizado)
- `404` - Not Found
- `409` - Conflict (recurso já existe)
- `429` - Too Many Requests (rate limit)
- `500` - Internal Server Error
- `501` - Not Implemented
- `503` - Service Unavailable

## 🔒 Rate Limiting

- **Geral**: 100 requests por 15 minutos
- **Autenticação**: 5 tentativas por 15 minutos
- **Transações**: 20 por hora
- **Criação de partidas**: 10 por 5 minutos
- **Solicitações de amizade**: 50 por hora
- **Chat**: 30 mensagens por minuto
- **Busca**: 60 por minuto
- **Criação de torneios**: 5 por dia
- **Criação de clubes**: 3 por dia
- **Relatórios**: 10 por hora
- **Upload de arquivos**: 50 por hora
- **Webhooks**: 1000 por minuto

## 🎯 Próximos Passos

1. **✅ APIs Implementadas** - Todas as 120+ APIs estão funcionais
2. **Adicionar cache Redis** para melhor performance
3. **Integrar gateway de pagamento** real (PIX, cartões)
4. **Implementar análise de resultados** com OpenAI/IA
5. **Expandir testes unitários** para 100% de cobertura
6. **Implementar sistema de filas** (Bull/Redis) para processamento assíncrono
7. **Adicionar monitoramento** com Prometheus/Grafana
8. **Implementar CI/CD pipeline** completo
9. **Adicionar rate limiting dinâmico** baseado no usuário
10. **Implementar sistema de logs** estruturados com ELK Stack
11. **Adicionar testes de integração** e E2E com Cypress
12. **Implementar sistema de backup** automatizado
13. **Adicionar métricas de performance** em tempo real
14. **Implementar sistema de alertas** para problemas críticos

import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Target } from 'lucide-react';
import GameResults from '../../components/GameResults';

interface Player {
  id: number;
  x: number;
  y: number;
  angle: number;
  power: number;
  isDrawing: boolean;
  score: number;
  health: number;
  isActive: boolean;
  powerUps: {
    multishot: number;
    slowmo: number;
    explosive: number;
  };
}

interface Arrow {
  playerId: number;
  x: number;
  y: number;
  dx: number;
  dy: number;
  angle: number;
  active: boolean;
  isExplosive: boolean;
}

interface Obstacle {
  x: number;
  y: number;
  width: number;
  height: number;
  type: 'wood' | 'stone';
  health: number;
}

interface PowerUp {
  x: number;
  y: number;
  type: 'multishot' | 'slowmo' | 'explosive';
  active: boolean;
  radius: number;
}

const CANVAS_WIDTH = 800;
const CANVAS_HEIGHT = 600;
const GRAVITY = 0.5;
const ARROW_SPEED = 15;
const WIND_CHANGE_INTERVAL = 4000;
const POWER_MULTIPLIER = 1.5;
const PLAYER_SIZE = 40;

export default function StickmanArcher() {
  const navigate = useNavigate();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const contextRef = useRef<CanvasRenderingContext2D | null>(null);
  const [isGameOver, setIsGameOver] = useState(false);
  const [windSpeed, setWindSpeed] = useState(0);
  const [currentPlayer, setCurrentPlayer] = useState(1);

  const gameStateRef = useRef({
    players: {
      1: {
        id: 1,
        x: 100,
        y: CANVAS_HEIGHT - 100,
        angle: 0,
        power: 0,
        isDrawing: false,
        score: 0,
        health: 100,
        isActive: true,
        powerUps: {
          multishot: 0,
          slowmo: 0,
          explosive: 0
        }
      },
      2: {
        id: 2,
        x: CANVAS_WIDTH - 100,
        y: CANVAS_HEIGHT - 100,
        angle: Math.PI,
        power: 0,
        isDrawing: false,
        score: 0,
        health: 100,
        isActive: true,
        powerUps: {
          multishot: 0,
          slowmo: 0,
          explosive: 0
        }
      }
    } as Record<number, Player>,
    arrows: [] as Arrow[],
    obstacles: [] as Obstacle[],
    powerUps: [] as PowerUp[],
    timeScale: 1,
    animationFrame: 0
  });

  const createObstacles = () => {
    const obstacles: Obstacle[] = [
      // Plataformas centrais
      {
        x: CANVAS_WIDTH / 2 - 100,
        y: CANVAS_HEIGHT - 200,
        width: 200,
        height: 20,
        type: 'stone',
        health: 100
      },
      {
        x: CANVAS_WIDTH / 2 - 50,
        y: CANVAS_HEIGHT - 300,
        width: 100,
        height: 20,
        type: 'wood',
        health: 50
      },
      // Proteções laterais
      {
        x: 200,
        y: CANVAS_HEIGHT - 150,
        width: 20,
        height: 80,
        type: 'wood',
        health: 50
      },
      {
        x: CANVAS_WIDTH - 220,
        y: CANVAS_HEIGHT - 150,
        width: 20,
        height: 80,
        type: 'wood',
        health: 50
      }
    ];
    return obstacles;
  };

  const createPowerUp = () => {
    const types = ['multishot', 'slowmo', 'explosive'] as const;
    const type = types[Math.floor(Math.random() * types.length)];
    
    return {
      x: CANVAS_WIDTH/2 - 100 + Math.random() * 200,
      y: CANVAS_HEIGHT - 350 - Math.random() * 100,
      type,
      active: true,
      radius: 15
    };
  };

  const spawnPowerUps = () => {
    if (Math.random() < 0.3 && gameStateRef.current.powerUps.length < 2) {
      gameStateRef.current.powerUps.push(createPowerUp());
    }
  };

  const initializeGame = () => {
    gameStateRef.current.obstacles = createObstacles();
    gameStateRef.current.arrows = [];
    gameStateRef.current.powerUps = [];
    gameStateRef.current.players[1].health = 100;
    gameStateRef.current.players[2].health = 100;
    gameStateRef.current.players[1].isActive = true;
    gameStateRef.current.players[2].isActive = true;
    setCurrentPlayer(1);
    setIsGameOver(false);
    spawnPowerUps();
  };

  const shootArrow = () => {
    const player = gameStateRef.current.players[currentPlayer];
    const power = player.power * ARROW_SPEED * POWER_MULTIPLIER;
    
    const createArrow = (angleOffset = 0) => ({
      playerId: currentPlayer,
      x: player.x,
      y: player.y,
      dx: Math.cos(player.angle + angleOffset) * power,
      dy: Math.sin(player.angle + angleOffset) * power,
      angle: player.angle + angleOffset,
      active: true,
      isExplosive: player.powerUps.explosive > 0
    });

    if (player.powerUps.multishot > 0) {
      gameStateRef.current.arrows.push(
        createArrow(-0.1),
        createArrow(),
        createArrow(0.1)
      );
      player.powerUps.multishot--;
    } else {
      gameStateRef.current.arrows.push(createArrow());
    }

    if (player.powerUps.explosive > 0) {
      player.powerUps.explosive--;
    }

    // Troca de turno
    setCurrentPlayer(currentPlayer === 1 ? 2 : 1);
  };

  const checkCollisionWithObstacle = (x: number, y: number, obstacle: Obstacle) => {
    return x >= obstacle.x && 
           x <= obstacle.x + obstacle.width &&
           y >= obstacle.y && 
           y <= obstacle.y + obstacle.height;
  };

  const updateGame = () => {
    const { arrows, obstacles, powerUps, players } = gameStateRef.current;
    const timeScale = players[currentPlayer].powerUps.slowmo > 0 ? 0.5 : 1;

    // Update arrows
    arrows.forEach((arrow, index) => {
      if (!arrow.active) return;

      arrow.x += (arrow.dx + windSpeed) * timeScale;
      arrow.y += arrow.dy * timeScale;
      arrow.dy += GRAVITY * timeScale;
      arrow.angle = Math.atan2(arrow.dy, arrow.dx);

      // Check collisions with players
      Object.values(players).forEach(player => {
        if (!player.isActive || player.id === arrow.playerId) return;

        const dx = arrow.x - player.x;
        const dy = arrow.y - player.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < PLAYER_SIZE / 2) {
          const damage = arrow.isExplosive ? 40 : 20;
          player.health = Math.max(0, player.health - damage);
          
          if (player.health <= 0) {
            player.isActive = false;
            if (!Object.values(players).some(p => p.id !== arrow.playerId && p.isActive)) {
              setIsGameOver(true);
            }
          }
          
          arrow.active = false;
        }
      });

      // Check collisions with obstacles
      obstacles.forEach(obstacle => {
        if (checkCollisionWithObstacle(arrow.x, arrow.y, obstacle)) {
          if (arrow.isExplosive) {
            obstacle.health -= 50;
          } else {
            obstacle.health -= 20;
          }

          if (obstacle.health <= 0) {
            obstacles.splice(obstacles.indexOf(obstacle), 1);
          }
          
          arrow.active = false;
        }
      });

      // Check collisions with power-ups
      powerUps.forEach(powerUp => {
        if (!powerUp.active) return;
        
        const dx = arrow.x - powerUp.x;
        const dy = arrow.y - powerUp.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < powerUp.radius) {
          powerUp.active = false;
          players[arrow.playerId].powerUps[powerUp.type]++;
          arrow.active = false;
        }
      });

      // Check if arrow is out of bounds
      if (arrow.x < 0 || arrow.x > CANVAS_WIDTH || arrow.y > CANVAS_HEIGHT) {
        arrow.active = false;
      }
    });

    // Clean up inactive arrows
    gameStateRef.current.arrows = arrows.filter(arrow => arrow.active);

    // Update slow-mo
    if (players[currentPlayer].powerUps.slowmo > 0) {
      players[currentPlayer].powerUps.slowmo -= timeScale * 0.016;
    }

    // Spawn new power-ups
    if (Math.random() < 0.002) {
      spawnPowerUps();
    }
  };

  const drawGame = () => {
    if (!contextRef.current) return;
    const ctx = contextRef.current;
    const { players, arrows, obstacles, powerUps } = gameStateRef.current;

    // Clear canvas
    ctx.fillStyle = '#1F2937';
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // Draw wind indicator
    const windIndicatorY = 50;
    const windIndicatorX = CANVAS_WIDTH / 2;
    const windIndicatorWidth = Math.abs(windSpeed) * 50;
    
    ctx.beginPath();
    ctx.moveTo(windIndicatorX, windIndicatorY);
    ctx.lineTo(
      windIndicatorX + (windSpeed > 0 ? windIndicatorWidth : -windIndicatorWidth),
      windIndicatorY
    );
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    ctx.beginPath();
    ctx.moveTo(
      windIndicatorX + (windSpeed > 0 ? windIndicatorWidth : -windIndicatorWidth),
      windIndicatorY - 5
    );
    ctx.lineTo(
      windIndicatorX + (windSpeed > 0 ? windIndicatorWidth : -windIndicatorWidth),
      windIndicatorY + 5
    );
    ctx.stroke();

    // Draw obstacles
    obstacles.forEach(obstacle => {
      ctx.fillStyle = obstacle.type === 'wood' ? '#92400E' : '#4B5563';
      ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
      
      // Health bar
      const healthWidth = (obstacle.health / 100) * obstacle.width;
      ctx.fillStyle = '#EF4444';
      ctx.fillRect(obstacle.x, obstacle.y - 5, healthWidth, 3);
    });

    // Draw players
    Object.values(players).forEach(player => {
      if (!player.isActive) return;

      ctx.save();
      ctx.translate(player.x, player.y);
      ctx.rotate(player.angle);
      
      // Draw stickman
      ctx.strokeStyle = player.id === 1 ? '#3B82F6' : '#EF4444';
      ctx.lineWidth = 2;
      
      // Body
      ctx.beginPath();
      ctx.moveTo(0, -20);
      ctx.lineTo(0, 10);
      ctx.stroke();
      
      // Arms
      ctx.beginPath();
      ctx.moveTo(-15, -10);
      ctx.lineTo(15, -10);
      ctx.stroke();
      
      // Legs
      ctx.beginPath();
      ctx.moveTo(0, 10);
      ctx.lineTo(-10, 30);
      ctx.moveTo(0, 10);
      ctx.lineTo(10, 30);
      ctx.stroke();
      
      // Head
      ctx.beginPath();
      ctx.arc(0, -25, 5, 0, Math.PI * 2);
      ctx.stroke();
      
      // Bow
      ctx.beginPath();
      ctx.strokeStyle = '#D97706';
      ctx.lineWidth = 3;
      ctx.arc(15, -10, 20, -Math.PI / 2, Math.PI / 2);
      ctx.stroke();

      // Bowstring
      if (player.isDrawing) {
        ctx.beginPath();
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        ctx.moveTo(15, -30);
        ctx.lineTo(15 + player.power * 20, -10);
        ctx.lineTo(15, 10);
        ctx.stroke();
      } else {
        ctx.beginPath();
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1;
        ctx.moveTo(15, -30);
        ctx.lineTo(35, -10);
        ctx.lineTo(15, 10);
        ctx.stroke();
      }

      ctx.restore();

      // Health bar
      const healthBarWidth = 60;
      const healthBarHeight = 6;
      const healthBarX = player.x - healthBarWidth / 2;
      const healthBarY = player.y - 50;

      ctx.fillStyle = '#EF4444';
      ctx.fillRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);
      
      ctx.fillStyle = '#22C55E';
      ctx.fillRect(
        healthBarX,
        healthBarY,
        (player.health / 100) * healthBarWidth,
        healthBarHeight
      );
    });

    // Draw arrows
    arrows.forEach(arrow => {
      if (!arrow.active) return;

      ctx.save();
      ctx.translate(arrow.x, arrow.y);
      ctx.rotate(arrow.angle);
      
      // Arrow body
      ctx.fillStyle = arrow.isExplosive ? '#F87171' : '#FFFFFF';
      ctx.fillRect(-15, -1, 30, 2);
      
      // Arrow head
      ctx.beginPath();
      ctx.moveTo(15, 0);
      ctx.lineTo(10, -4);
      ctx.lineTo(10, 4);
      ctx.closePath();
      ctx.fill();

      ctx.restore();
    });

    // Draw power-ups
    powerUps.forEach(powerUp => {
      if (!powerUp.active) return;
      
      ctx.save();
      ctx.translate(powerUp.x, powerUp.y);
      
      ctx.beginPath();
      ctx.arc(0, 0, powerUp.radius, 0, Math.PI * 2);
      ctx.fillStyle = powerUp.type === 'multishot' ? '#60A5FA' :
                     powerUp.type === 'slowmo' ? '#34D399' :
                     '#F87171';
      ctx.fill();
      
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(
        powerUp.type === 'multishot' ? '3X' :
        powerUp.type === 'slowmo' ? '⏱' :
        '💥',
        0, 0
      );
      
      ctx.restore();
    });

    // Draw current player indicator
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(
      `Turno do Jogador ${currentPlayer}`,
      CANVAS_WIDTH / 2,
      30
    );

    // Draw wind speed
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(
      `Vento: ${windSpeed > 0 ? '→' : '←'} ${Math.abs(windSpeed).toFixed(1)}`,
      CANVAS_WIDTH / 2,
      80
    );
  };

  const gameLoop = () => {
    updateGame();
    drawGame();
    gameStateRef.current.animationFrame = requestAnimationFrame(gameLoop);
  };

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    if (!context) return;

    contextRef.current = context;
    initializeGame();

    // Start wind changes
    const windInterval = setInterval(() => {
      setWindSpeed(Math.random() * 4 - 2);
    }, WIND_CHANGE_INTERVAL);

    // Start game loop
    gameStateRef.current.animationFrame = requestAnimationFrame(gameLoop);

    const handleMouseMove = (e: MouseEvent) => {
      const player = gameStateRef.current.players[currentPlayer];
      if (!player.isActive) return;

      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const dx = x - player.x;
      const dy = y - player.y;
      
      // Limita o ângulo de tiro para cada jogador
      let angle = Math.atan2(dy, dx);
      if (currentPlayer === 1) {
        angle = Math.max(-Math.PI/2, Math.min(Math.PI/2, angle));
      } else {
        angle = Math.max(Math.PI/2, Math.min(3*Math.PI/2, angle));
      }
      
      player.angle = angle;
    };

    const handleMouseDown = () => {
      const player = gameStateRef.current.players[currentPlayer];
      if (player.isActive) {
        player.isDrawing = true;
      }
    };

    const handleMouseUp = () => {
      const player = gameStateRef.current.players[currentPlayer];
      if (player.isActive && player.isDrawing) {
        shootArrow();
        player.isDrawing = false;
        player.power = 0;
      }
    };

    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mouseup', handleMouseUp);

    return () => {
      clearInterval(windInterval);
      cancelAnimationFrame(gameStateRef.current.animationFrame);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mouseup', handleMouseUp);
    };
  }, [currentPlayer]);

  // Power charge effect
  useEffect(() => {
    const updatePower = () => {
      const player = gameStateRef.current.players[currentPlayer];
      if (player.isDrawing) {
        player.power = Math.min(player.power + 0.05, 1);
      }
    };

    const powerInterval = setInterval(updatePower, 16);
    return () => clearInterval(powerInterval);
  }, [currentPlayer]);

  return (
    <div className="min-h-screen bg-gray-900 p-6 flex flex-col items-center">
      <div className="w-full max-w-4xl mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="w-6 h-6 text-yellow-400" />
            <h1 className="text-xl font-bold text-white">Stickman Archer</h1>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-400">Turno</p>
            <p className="text-lg font-bold text-yellow-400">Jogador {currentPlayer}</p>
          </div>
        </div>
      </div>

      <canvas
        ref={canvasRef}
        width={CANVAS_WIDTH}
        height={CANVAS_HEIGHT}
        className="bg-gray-800 rounded-lg cursor-crosshair"
      />

      {isGameOver && (
        <GameResults
          players={[
            {
              id: '1',
              name: `Jogador ${
                gameStateRef.current.players[1].isActive ? '1' : '2'
              }`,
              avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
              score: 100,
              isCurrentPlayer: true
            }
          ]}
          prizePool={100}
          onPlayAgain={initializeGame}
          onExit={() => navigate('/games/stickmanarcher')}
        />
      )}

      <div className="w-full max-w-4xl mt-6">
        <div className="bg-gray-800 rounded-xl p-4">
          <h2 className="text-white font-semibold mb-2">Como Jogar</h2>
          <p className="text-gray-400 text-sm">
            Mire com o mouse e segure o botão para carregar a força do tiro.
            Desvie do vento e dos obstáculos para acertar seu oponente! 
            Colete power-ups para habilidades especiais.
          </p>
        </div>
      </div>
    </div>
  );
}
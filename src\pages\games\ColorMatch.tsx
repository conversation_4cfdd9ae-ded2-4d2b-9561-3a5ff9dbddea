import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Grid, Timer } from 'lucide-react';
import GameResults from '../../components/GameResults';

// Cores disponíveis para o jogo
const COLORS = [
  { name: 'red', bg: 'bg-red-500' },
  { name: 'blue', bg: 'bg-blue-500' },
  { name: 'green', bg: 'bg-green-500' },
  { name: 'yellow', bg: 'bg-yellow-500' },
  { name: 'purple', bg: 'bg-purple-500' },
  { name: 'pink', bg: 'bg-pink-500' }
];

const GRID_SIZE = 4;
const ROUND_TIME = 60; // segundos

interface Card {
  id: number;
  color: typeof COLORS[number];
  isFlipped: boolean;
  isMatched: boolean;
}

interface PlayerResult {
  id: string;
  name: string;
  avatar: string;
  score: number;
  isCurrentPlayer?: boolean;
}

export default function ColorMatch() {
  const navigate = useNavigate();
  const [cards, setCards] = useState<Card[]>([]);
  const [flippedCards, setFlippedCards] = useState<Card[]>([]);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(ROUND_TIME);
  const [isGameOver, setIsGameOver] = useState(false);
  const timerRef = useRef<NodeJS.Timeout>();

  // Simula outros jogadores para demonstração
  const playerResults: PlayerResult[] = [
    {
      id: '1',
      name: 'Você',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: score,
      isCurrentPlayer: true
    },
    {
      id: '2',
      name: 'Ana Silva',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: Math.floor(score * 0.8)
    },
    {
      id: '3',
      name: 'Lucas Oliveira',
      avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      score: Math.floor(score * 0.6)
    }
  ];

  const initializeGame = () => {
    // Cria pares de cartas com cores aleatórias
    const pairs = [...COLORS, ...COLORS]
      .sort(() => Math.random() - 0.5)
      .slice(0, (GRID_SIZE * GRID_SIZE) / 2);

    const newCards = [...pairs, ...pairs]
      .sort(() => Math.random() - 0.5)
      .map((color, index) => ({
        id: index,
        color,
        isFlipped: false,
        isMatched: false
      }));

    setCards(newCards);
    setFlippedCards([]);
    setScore(0);
    setTimeLeft(ROUND_TIME);
    setIsGameOver(false);
  };

  const handleCardClick = (card: Card) => {
    if (
      flippedCards.length === 2 ||
      card.isFlipped ||
      card.isMatched ||
      isGameOver
    ) {
      return;
    }

    const newCards = cards.map(c =>
      c.id === card.id ? { ...c, isFlipped: true } : c
    );
    setCards(newCards);

    const newFlippedCards = [...flippedCards, card];
    setFlippedCards(newFlippedCards);

    if (newFlippedCards.length === 2) {
      // Verifica se é um par
      if (newFlippedCards[0].color.name === newFlippedCards[1].color.name) {
        setTimeout(() => {
          setCards(prev =>
            prev.map(c =>
              c.id === newFlippedCards[0].id || c.id === newFlippedCards[1].id
                ? { ...c, isMatched: true }
                : c
            )
          );
          setScore(prev => prev + 100);
          setFlippedCards([]);
        }, 500);
      } else {
        setTimeout(() => {
          setCards(prev =>
            prev.map(c =>
              c.id === newFlippedCards[0].id || c.id === newFlippedCards[1].id
                ? { ...c, isFlipped: false }
                : c
            )
          );
          setFlippedCards([]);
        }, 1000);
      }
    }
  };

  const startGame = () => {
    initializeGame();

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          setIsGameOver(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  useEffect(() => {
    startGame();
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (cards.length > 0 && cards.every(card => card.isMatched)) {
      clearInterval(timerRef.current);
      setIsGameOver(true);
    }
  }, [cards]);

  return (
    <div className="min-h-screen bg-gray-900 p-6 flex flex-col items-center">
      <div className="w-full max-w-lg mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Grid className="w-6 h-6 text-yellow-400" />
            <h1 className="text-xl font-bold text-white">Color Match</h1>
          </div>
          <div className="flex items-center gap-6">
            <div className="text-right">
              <p className="text-sm text-gray-400">Pontuação</p>
              <p className="text-lg font-bold text-yellow-400">{score}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-400">Tempo</p>
              <p className="text-lg font-bold text-white">{timeLeft}s</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-4 gap-3 w-full max-w-lg">
        {cards.map(card => (
          <button
            key={card.id}
            onClick={() => handleCardClick(card)}
            className={`aspect-square rounded-lg transition-all duration-300 transform ${
              card.isFlipped || card.isMatched
                ? card.color.bg
                : 'bg-gray-700 hover:bg-gray-600'
            } ${card.isMatched ? 'opacity-50' : ''}`}
            disabled={card.isMatched}
          />
        ))}
      </div>

      {isGameOver && (
        <GameResults
          players={playerResults}
          prizePool={100}
          onPlayAgain={startGame}
          onExit={() => navigate('/games/colormatch')}
        />
      )}
    </div>
  );
}
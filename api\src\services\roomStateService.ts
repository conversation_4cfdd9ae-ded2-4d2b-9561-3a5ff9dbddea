import { supabaseAdmin } from '../config/database';
import { logger } from '../utils/logger';

export interface RoomState {
  id: string;
  name: string;
  status: 'waiting' | 'starting' | 'playing' | 'finished';
  entry_fee: number;
  max_players: number;
  current_players: number;
  started_at?: string;
  finished_at?: string;
  participants: Array<{
    user_id: string;
    is_ready: boolean;
    joined_at: string;
    users: {
      username: string;
      display_name?: string;
      avatar_url?: string;
    };
  }>;
  currentMatch?: {
    id: string;
    status: string;
    started_at?: string;
    finished_at?: string;
  };
}

export class RoomStateService {
  /**
   * Get complete room state from database
   */
  static async getRoomState(roomId: string): Promise<RoomState | null> {
    try {
      const { data: roomState, error } = await supabaseAdmin
        .from('game_rooms')
        .select(`
          *,
          room_participants (
            user_id, 
            is_ready, 
            joined_at,
            users (username, display_name, avatar_url)
          ),
          matches (id, status, started_at, finished_at)
        `)
        .eq('id', roomId)
        .single();

      if (error || !roomState) {
        logger.error(`Failed to get room state for ${roomId}:`, error);
        return null;
      }

      return {
        id: roomState.id,
        name: roomState.name,
        status: roomState.status,
        entry_fee: roomState.entry_fee,
        max_players: roomState.max_players,
        current_players: roomState.current_players,
        started_at: roomState.started_at,
        finished_at: roomState.finished_at,
        participants: roomState.room_participants || [],
        currentMatch: roomState.matches?.[0] || undefined
      };
    } catch (error) {
      logger.error(`Error getting room state for ${roomId}:`, error);
      return null;
    }
  }

  /**
   * Update room status in database
   */
  static async updateRoomStatus(
    roomId: string, 
    status: 'waiting' | 'starting' | 'playing' | 'finished',
    additionalData?: { started_at?: string; finished_at?: string }
  ): Promise<boolean> {
    try {
      const updateData: any = { status };
      
      if (additionalData?.started_at) {
        updateData.started_at = additionalData.started_at;
      }
      
      if (additionalData?.finished_at) {
        updateData.finished_at = additionalData.finished_at;
      }

      const { error } = await supabaseAdmin
        .from('game_rooms')
        .update(updateData)
        .eq('id', roomId);

      if (error) {
        logger.error(`Failed to update room status for ${roomId}:`, error);
        return false;
      }

      logger.info(`Room ${roomId} status updated to ${status}`);
      return true;
    } catch (error) {
      logger.error(`Error updating room status for ${roomId}:`, error);
      return false;
    }
  }

  /**
   * Check if all players in room are ready
   */
  static async areAllPlayersReady(roomId: string): Promise<{ allReady: boolean; roomFull: boolean; playerCount: number }> {
    try {
      const { data: room, error } = await supabaseAdmin
        .from('game_rooms')
        .select(`
          max_players,
          room_participants (user_id, is_ready)
        `)
        .eq('id', roomId)
        .single();

      if (error || !room) {
        logger.error(`Failed to check ready status for room ${roomId}:`, error);
        return { allReady: false, roomFull: false, playerCount: 0 };
      }

      const participants = room.room_participants || [];
      const playerCount = participants.length;
      const allReady = participants.length > 0 && participants.every((p: any) => p.is_ready);
      const roomFull = playerCount >= room.max_players;

      return { allReady, roomFull, playerCount };
    } catch (error) {
      logger.error(`Error checking ready status for room ${roomId}:`, error);
      return { allReady: false, roomFull: false, playerCount: 0 };
    }
  }

  /**
   * Create match for room
   */
  static async createMatchForRoom(roomId: string): Promise<string | null> {
    try {
      // Get room details
      const { data: room, error: roomError } = await supabaseAdmin
        .from('game_rooms')
        .select(`
          game_id,
          entry_fee,
          room_participants (user_id)
        `)
        .eq('id', roomId)
        .single();

      if (roomError || !room) {
        logger.error(`Failed to get room details for match creation ${roomId}:`, roomError);
        return null;
      }

      const participants = room.room_participants || [];
      
      // Create match
      const { data: match, error: matchError } = await supabaseAdmin
        .from('matches')
        .insert({
          game_id: room.game_id,
          room_id: roomId,
          match_type: '1v1',
          status: 'in_progress',
          entry_fee: room.entry_fee,
          total_prize: room.entry_fee * participants.length,
          started_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (matchError || !match) {
        logger.error(`Failed to create match for room ${roomId}:`, matchError);
        return null;
      }

      // Create match participants
      const matchParticipants = participants.map((p: any) => ({
        match_id: match.id,
        user_id: p.user_id
      }));

      const { error: participantsError } = await supabaseAdmin
        .from('match_participants')
        .insert(matchParticipants);

      if (participantsError) {
        logger.error(`Failed to create match participants for match ${match.id}:`, participantsError);
        return null;
      }

      logger.info(`Match ${match.id} created for room ${roomId} with ${participants.length} participants`);
      return match.id;
    } catch (error) {
      logger.error(`Error creating match for room ${roomId}:`, error);
      return null;
    }
  }

  /**
   * Verify user is participant of room
   */
  static async isUserParticipant(roomId: string, userId: string): Promise<boolean> {
    try {
      const { data: participant, error } = await supabaseAdmin
        .from('room_participants')
        .select('id')
        .eq('room_id', roomId)
        .eq('user_id', userId)
        .single();

      return !error && !!participant;
    } catch (error) {
      logger.error(`Error checking if user ${userId} is participant of room ${roomId}:`, error);
      return false;
    }
  }

  /**
   * Update player ready status
   */
  static async updatePlayerReadyStatus(roomId: string, userId: string, isReady: boolean): Promise<boolean> {
    try {
      const { error } = await supabaseAdmin
        .from('room_participants')
        .update({ is_ready: isReady })
        .eq('room_id', roomId)
        .eq('user_id', userId);

      if (error) {
        logger.error(`Failed to update ready status for user ${userId} in room ${roomId}:`, error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error(`Error updating ready status for user ${userId} in room ${roomId}:`, error);
      return false;
    }
  }

  /**
   * Get room messages from database
   */
  static async getRoomMessages(roomId: string, limit: number = 50): Promise<any[]> {
    try {
      const { data: messages, error } = await supabaseAdmin
        .from('room_chat_messages')
        .select(`
          id, message, message_type, created_at,
          users (username, display_name, avatar_url)
        `)
        .eq('room_id', roomId)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) {
        logger.error(`Failed to get messages for room ${roomId}:`, error);
        return [];
      }

      return messages || [];
    } catch (error) {
      logger.error(`Error getting messages for room ${roomId}:`, error);
      return [];
    }
  }

  /**
   * Clean up finished room - remove participants and reset for reuse
   */
  static async cleanupFinishedRoom(roomId: string): Promise<boolean> {
    try {
      // Remove all participants from the room
      const { error: participantsError } = await supabaseAdmin
        .from('room_participants')
        .delete()
        .eq('room_id', roomId);

      if (participantsError) {
        logger.error(`Error removing participants from room ${roomId}:`, participantsError);
        return false;
      }

      // Reset room status to waiting for reuse
      const { error: roomError } = await supabaseAdmin
        .from('game_rooms')
        .update({
          status: 'waiting',
          started_at: null,
          finished_at: null
        })
        .eq('id', roomId);

      if (roomError) {
        logger.error(`Error resetting room ${roomId}:`, roomError);
        return false;
      }

      // Clear chat messages (optional - keep for history or clear for privacy)
      const { error: chatError } = await supabaseAdmin
        .from('room_chat_messages')
        .delete()
        .eq('room_id', roomId);

      if (chatError) {
        logger.warn(`Warning: Could not clear chat for room ${roomId}:`, chatError);
        // Don't fail the cleanup for chat clearing issues
      }

      logger.info(`Room ${roomId} cleaned up successfully and reset for reuse`);
      return true;
    } catch (error) {
      logger.error(`Error cleaning up room ${roomId}:`, error);
      return false;
    }
  }
}

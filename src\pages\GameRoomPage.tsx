import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Users, Crown, MessageCircle, UserPlus, X, Wallet, Trophy, AlertCircle, Send, Upload, Copy, CheckCircle, HelpCircle, Info, ListChecks, Loader2 } from 'lucide-react';
import MatchResultModal from '../components/MatchResultModal';
import { useUser } from '../contexts/UserContext';
import { realGameService, GameRoom, GameRoomParticipant, GameRoomChatMessage } from '../services/realGameService';
import { socketService } from '../services/socketService';
import { realWalletService } from '../services/realWalletService';

interface Player {
  id: string;
  name: string;
  avatar: string;
  isHost?: boolean;
  isReady?: boolean;
}

interface RoomInfo {
  betAmount: number;
  totalPrize: number;
  maxPlayers: number;
}

interface ChatMessage {
  id: string;
  playerId: string;
  playerName: string;
  playerAvatar: string;
  message: string;
  timestamp: Date;
  type?: 'text' | 'system';
}

export default function GameRoomPage() {
  const navigate = useNavigate();
  const { gameId, roomId } = useParams();
  const { user } = useUser();

  // Estados da UI
  const [showInvite, setShowInvite] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [gameStarting, setGameStarting] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [hasPaid, setHasPaid] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [matchStarted, setMatchStarted] = useState(false);
  const [matchTime, setMatchTime] = useState(1800); // 30 minutes (1800 seconds)
  const [gameStarted, setGameStarted] = useState(false);
  const [currentMatchId, setCurrentMatchId] = useState<string | null>(null);

  // Estados dos dados
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [room, setRoom] = useState<GameRoom | null>(null);
  const [players, setPlayers] = useState<Player[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [userBalance, setUserBalance] = useState(0);

  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Usar o nome do usuário do contexto, ou um nome padrão se não estiver definido
  const currentPlayer = {
    id: user.id || '',
    name: user.name || user.username || 'Jogador',
    avatar: user.avatar || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
  };

  const roomInfo: RoomInfo = {
    betAmount: room?.entry_fee || 0,
    totalPrize: room ? (room.entry_fee * room.max_players) : 0,
    maxPlayers: room?.max_players || 2
  };

  // Carregar dados da sala
  useEffect(() => {
    const loadRoomData = async () => {
      if (!roomId) {
        setError('ID da sala não encontrado');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Carregar dados da sala
        const roomData = await realGameService.getRoomById(roomId);
        if (!roomData) {
          setError('Sala não encontrada');
          setLoading(false);
          return;
        }

        setRoom(roomData);

        // Converter participantes para formato de players
        const roomPlayers: Player[] = roomData.room_participants?.map(participant => ({
          id: participant.user_id,
          name: participant.users.display_name || participant.users.username,
          avatar: participant.users.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          isHost: participant.user_id === roomData.host_user_id,
          isReady: participant.is_ready
        })) || [];

        setPlayers(roomPlayers);

        // Converter mensagens de chat
        const chatMessages: ChatMessage[] = roomData.room_chat_messages?.map(msg => ({
          id: msg.id,
          playerId: msg.message_type === 'system' ? 'system' : msg.users.username,
          playerName: msg.message_type === 'system' ? 'Sistema' : (msg.users.display_name || msg.users.username),
          playerAvatar: msg.message_type === 'system' ? '' : (msg.users.avatar_url || ''),
          message: msg.message,
          timestamp: new Date(msg.created_at),
          type: msg.message_type
        })) || [];

        setMessages(chatMessages);

        // Verificar se o usuário já está na sala
        const userInRoom = roomPlayers.find(p => p.id === user.id);
        if (userInRoom) {
          setHasPaid(true);
          setIsReady(userInRoom.isReady);
        } else {
          setShowPaymentModal(true);
        }

        // Carregar saldo do usuário
        const walletData = await realWalletService.getBalance();
        if (walletData) {
          setUserBalance(walletData.balance);
        }

        setLoading(false);
      } catch (error) {
        console.error('❌ Erro ao carregar dados da sala:', error);
        setError('Erro ao carregar dados da sala');
        setLoading(false);
      }
    };

    loadRoomData();
  }, [roomId, user.id]);

  // Conectar ao WebSocket
  useEffect(() => {
    const connectSocket = async () => {
      if (!roomId || !hasPaid) return;

      try {
        // Força reconexão para garantir que está usando o token correto do usuário atual
        await socketService.reconnect();
        await socketService.joinRoom(roomId);

        // Solicitar sincronização completa do estado da sala
        socketService.syncRoomState(roomId);

        // Escutar sincronização completa do estado
        socketService.onRoomStateSynced((data) => {
          console.log('🔄 Estado da sala sincronizado:', data);

          // Atualizar dados da sala
          if (data.room) {
            setRoom(prev => ({ ...prev, ...data.room }));
          }

          // Atualizar participantes
          if (data.participants) {
            const updatedPlayers = data.participants.map((p: any) => ({
              id: p.user_id,
              name: p.users.display_name || p.users.username,
              avatar: p.users.avatar_url || '/default-avatar.png',
              isReady: p.is_ready,
              isHost: p.user_id === data.room?.host_user_id
            }));
            setPlayers(updatedPlayers);
          }

          // Atualizar mensagens
          if (data.messages) {
            const chatMessages = data.messages.map((msg: any) => ({
              id: msg.id,
              playerId: msg.users.username,
              playerName: msg.users.display_name || msg.users.username,
              playerAvatar: msg.users.avatar_url || '',
              message: msg.message,
              timestamp: new Date(msg.created_at),
              type: msg.message_type
            }));
            setMessages(chatMessages);
          }

          // Atualizar estado da partida
          if (data.currentMatch) {
            setCurrentMatchId(data.currentMatch.id);
            if (data.currentMatch.status === 'in_progress') {
              setMatchStarted(true);
            }
          }
        });

        // Escutar novas mensagens
        socketService.onNewMessage((message) => {
          const newMessage: ChatMessage = {
            id: message.id,
            playerId: message.users.username,
            playerName: message.users.display_name || message.users.username,
            playerAvatar: message.users.avatar_url || '',
            message: message.message,
            timestamp: new Date(message.created_at),
            type: message.message_type
          };
          setMessages(prev => [...prev, newMessage]);
        });

        // Escutar usuários entrando
        socketService.onUserJoinedRoom((user) => {
          addSystemMessage(`${user.username} entrou na sala`);

          // Solicitar sincronização do estado da sala para atualizar lista de participantes
          socketService.syncRoomState(roomId);
        });

        // Escutar usuários saindo
        socketService.onUserLeftRoom((user) => {
          addSystemMessage(`${user.username} saiu da sala`);

          // Solicitar sincronização do estado da sala para atualizar lista de participantes
          socketService.syncRoomState(roomId);
        });

        // Escutar mudanças de status de pronto
        socketService.onPlayerReadyChanged((data) => {
          setPlayers(prev => prev.map(p =>
            p.id === data.userId
              ? { ...p, isReady: data.isReady }
              : p
          ));
        });

        // Escutar estado da sala
        socketService.onRoomStateUpdate((data) => {
          console.log('🔄 Estado da sala atualizado:', data);

          // Atualizar participantes se fornecidos
          if (data.participants) {
            const updatedPlayers = data.participants.map((p: any) => ({
              id: p.user_id,
              name: p.users.display_name || p.users.username,
              avatar: p.users.avatar_url || '/default-avatar.png',
              isReady: p.is_ready,
              isHost: p.user_id === room?.host_user_id
            }));
            setPlayers(updatedPlayers);
            console.log('👥 Lista de participantes atualizada:', updatedPlayers);
          }

          // Atualizar estado baseado no status da sala
          if (data.status === 'playing') {
            setMatchStarted(true);
            setGameStarting(false);
          } else if (data.status === 'starting') {
            setGameStarting(true);
            setMatchStarted(false);
          } else {
            setGameStarting(false);
            setMatchStarted(false);
          }
        });

        // Escutar início do jogo
        socketService.onGameStarting((data) => {
          console.log('🎮 Jogo iniciando:', data);
          setGameStarting(true);
          setCountdown(data.countdown);
          addSystemMessage(data.message);
        });

        // Escutar atualizações do countdown
        socketService.onCountdownUpdate((data) => {
          console.log('⏰ Countdown:', data.countdown);
          setCountdown(data.countdown);
        });

        // Escutar início da partida
        socketService.onMatchStarted((data) => {
          console.log('🚀 Partida iniciada:', data);
          console.log('📝 Match ID recebido:', data.matchId);
          setMatchStarted(true);
          setGameStarting(false);
          setCountdown(0);
          setCurrentMatchId(data.matchId); // Capturar o ID da partida
          console.log('✅ Current Match ID definido:', data.matchId);

          // Salvar no localStorage como backup
          if (data.matchId && roomId) {
            localStorage.setItem(`current_match_${roomId}`, data.matchId);
            console.log('💾 Match ID salvo no localStorage:', data.matchId);
          }
          addSystemMessage(data.message);
        });

        // Escutar partida em progresso
        socketService.onMatchInProgress((data) => {
          console.log('⚡ Partida em progresso:', data);
          setMatchStarted(true);
          setGameStarting(false);
          addSystemMessage('Partida em andamento');
        });

        // Escutar submissão de resultados
        socketService.onMatchResultSubmitted((data) => {
          console.log('📊 Resultado submetido:', data);
          addSystemMessage(`${data.username} submeteu resultado: ${data.result}`);
        });

        // Escutar confirmação de submissão
        socketService.onMatchResultSubmittedSuccess((data) => {
          console.log('✅ Resultado submetido com sucesso:', data);
          addSystemMessage('Seu resultado foi submetido com sucesso!');

          if (data.shouldRedirect && data.matchId) {
            console.log('🔄 Redirecionando para resultados (primeiro a submeter)...');

            // Fechar modal de resultado
            setShowResultModal(false);
            setSelectedFile(null);

            // Redirecionar imediatamente para quem submeteu primeiro
            setTimeout(() => {
              navigate(`/games/${gameId}/results/${data.matchId}`);
            }, 1000);
          }
        });

        // Escutar finalização da partida (primeiro jogador submeteu)
        socketService.onMatchEnding((data) => {
          console.log('🏁 Primeiro resultado submetido:', data);
          addSystemMessage(data.message);

          // NÃO desabilitar o botão ainda - outros jogadores ainda precisam submeter
          // setMatchStarted(false); // Removido - só desabilitar quando todos submeterem

          // Mostrar que aguarda outros resultados
          addSystemMessage('Aguardando outros jogadores submeterem seus resultados...');
        });

        // Escutar notificação de espera por mais resultados
        socketService.onWaitingForResults((data) => {
          console.log('⏳ Aguardando mais resultados:', data);
          addSystemMessage(data.message);
          addSystemMessage(`Resultados submetidos: ${data.submittedCount}/${data.totalCount}`);
        });

        // Escutar conclusão completa da partida
        socketService.onMatchCompleted((data) => {
          console.log('🎉 Partida completamente finalizada:', data);
          addSystemMessage(data.message);

          // AGORA sim desabilitar o botão - partida completamente finalizada
          setMatchStarted(false);

          if (data.redirectToResults && data.matchId) {
            console.log('🔄 Redirecionando para resultados automaticamente...');

            // Aguardar um momento para mostrar a mensagem
            setTimeout(() => {
              navigate(`/games/${gameId}/results/${data.matchId}`);
            }, 2000);
          }
        });

      } catch (error) {
        console.error('❌ Erro ao conectar WebSocket:', error);
      }
    };

    connectSocket();

    return () => {
      socketService.leaveRoom();
      socketService.removeAllListeners();
    };
  }, [roomId, hasPaid]);

  // Scroll chat to bottom when new messages arrive
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // Note: Countdown logic is now handled by WebSocket events from the server
  // This useEffect is kept for any local state management if needed
  useEffect(() => {
    // Local state management can be added here if needed
    // The countdown and game starting logic is now handled by WebSocket events
  }, [players, room?.max_players]);

  // Start match timer when match starts
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (matchStarted && matchTime > 0) {
      interval = setInterval(() => {
        setMatchTime(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [matchStarted]);

  const formatMatchTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePayment = async () => {
    if (!room || userBalance < roomInfo.betAmount) return;

    try {
      // Entrar na sala via API
      const result = await realGameService.joinRoom(room.id);
      if (result.success) {
        setHasPaid(true);
        setShowPaymentModal(false);
        addSystemMessage(`${currentPlayer.name} pagou a entrada e entrou na sala`);

        // Recarregar dados da sala
        const updatedRoom = await realGameService.getRoomById(room.id);
        if (updatedRoom) {
          setRoom(updatedRoom);
          const roomPlayers: Player[] = updatedRoom.room_participants?.map(participant => ({
            id: participant.user_id,
            name: participant.users.display_name || participant.users.username,
            avatar: participant.users.avatar_url || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            isHost: participant.user_id === updatedRoom.host_user_id,
            isReady: participant.is_ready
          })) || [];
          setPlayers(roomPlayers);
        }
      } else {
        addSystemMessage(`Erro ao entrar na sala: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Erro ao pagar entrada:', error);
      addSystemMessage('Erro ao processar pagamento');
    }
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim() && socketService.connected) {
      socketService.sendMessage(newMessage.trim());
      setNewMessage('');
    }
  };

  const addSystemMessage = (text: string) => {
    const message: ChatMessage = {
      id: `system-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, // Unique ID
      playerId: 'system',
      playerName: 'Sistema',
      playerAvatar: '',
      message: text,
      timestamp: new Date(),
      type: 'system'
    };
    setMessages(prev => [...prev, message]);
  };

  const formatMessageTime = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const [showResultModal, setShowResultModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [resultSubmitted, setResultSubmitted] = useState(false);

  const handleUploadResult = (file: File) => {
    // Armazenar o arquivo selecionado
    setSelectedFile(file);

    // Mostrar o modal de processamento
    setShowResultModal(true);

    // Adicionar mensagem no chat
    addSystemMessage('Arquivo selecionado. Processando resultado...');
  };

  const handleSubmitResult = (result: 'win' | 'loss' | 'draw', score?: number) => {
    if (!currentMatchId) {
      console.error('❌ Nenhuma partida ativa para submeter resultado');
      addSystemMessage('Erro: Nenhuma partida ativa encontrada');
      return;
    }

    console.log(`🎮 Submetendo resultado via WebSocket: ${result} (Match ID: ${currentMatchId})`);

    // Submeter resultado via WebSocket
    socketService.submitMatchResult(currentMatchId, result, score, selectedFile?.name);

    // Fechar modal
    setShowResultModal(false);
    setSelectedFile(null);

    // Marcar que o resultado foi submetido
    setResultSubmitted(true);

    // Adicionar mensagem no chat
    addSystemMessage(`Resultado submetido: ${result === 'win' ? 'Vitória' : result === 'loss' ? 'Derrota' : 'Empate'}`);
    addSystemMessage('Aguardando processamento... Você será redirecionado automaticamente.');
  };

  const handleCopyUsername = (opponentName: string) => {
    navigator.clipboard.writeText(opponentName);
    addSystemMessage(`Você copiou o nome de usuário de ${opponentName}`);

    // Mostrar feedback visual temporário
    const feedbackElement = document.createElement('div');
    feedbackElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 z-50';
    feedbackElement.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M20 6 9 17l-5-5"/>
      </svg>
      <span>Nome copiado!</span>
    `;
    document.body.appendChild(feedbackElement);

    setTimeout(() => {
      feedbackElement.classList.add('opacity-0', 'transition-opacity', 'duration-300');
      setTimeout(() => document.body.removeChild(feedbackElement), 300);
    }, 2000);
  };

  // Tela de loading
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-rose-400 animate-spin mx-auto mb-4" />
          <p className="text-white">Carregando sala...</p>
        </div>
      </div>
    );
  }

  // Tela de erro
  if (error || !room) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="w-8 h-8 text-red-400 mx-auto mb-4" />
          <p className="text-white mb-4">{error || 'Sala não encontrada'}</p>
          <button
            onClick={() => navigate(`/external-games/${gameId}`)}
            className="bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold py-2 px-4 rounded-lg"
          >
            Voltar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 flex flex-col pb-16 sm:pb-0">
      {/* Prize Banner */}
      <div className="bg-gradient-to-r from-rose-400 to-pink-400 rounded-xl p-3 sm:p-4 order-1">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
          <div>
            <h2 className="text-black font-bold text-base sm:text-lg">
              {matchStarted ? 'PARTIDA ROLANDO! 🔥' : room.name}
            </h2>
            {matchStarted ? (
              <p className="text-black/80 text-sm sm:text-base">Tempo Restante: {formatMatchTime(matchTime)}</p>
            ) : (
              <p className="text-black/80 text-sm sm:text-base">
                {gameStarting
                  ? `Iniciando em ${countdown}...`
                  : `${players.length}/${room?.max_players || 2} jogadores ${
                      players.length < (room?.max_players || 2)
                        ? `(aguardando ${(room?.max_players || 2) - players.length} jogador${(room?.max_players || 2) - players.length > 1 ? 'es' : ''})`
                        : players.every(p => p.isReady)
                          ? '(todos prontos!)'
                          : '(aguardando jogadores ficarem prontos)'
                    }`
                }
              </p>
            )}
          </div>
          <div className="text-left sm:text-right flex flex-row sm:block justify-between items-center">
            <div className="flex items-center gap-1 sm:gap-2 mb-0 sm:mb-1">
              <Wallet className="w-3 h-3 sm:w-4 sm:h-4 text-black" />
              <span className="text-black font-medium text-xs sm:text-sm">Aposta: R$ {roomInfo.betAmount.toFixed(2)}</span>
            </div>
            <div className="flex items-center gap-1 sm:gap-2">
              <Trophy className="w-3 h-3 sm:w-4 sm:h-4 text-black" />
              <span className="text-black font-bold text-xs sm:text-sm">Prêmio: R$ {roomInfo.totalPrize.toFixed(2)}</span>
            </div>
          </div>
        </div>
        {matchStarted && (
          <div className="mt-3 sm:mt-4 flex justify-center">
            <button
              onClick={() => !resultSubmitted && setShowResultModal(true)}
              disabled={resultSubmitted}
              className={`font-bold px-4 sm:px-6 py-1.5 sm:py-2 rounded-lg flex items-center gap-1 sm:gap-2 text-xs sm:text-sm ${
                resultSubmitted
                  ? 'bg-green-600 text-white cursor-not-allowed'
                  : 'bg-black text-white cursor-pointer hover:bg-gray-900'
              }`}
            >
              {resultSubmitted ? (
                <>
                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Resultado Processado!
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 sm:w-5 sm:h-5" />
                  Upload do Resultado
                </>
              )}
            </button>
          </div>
        )}
      </div>

      {/* Instruction Card */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-3 sm:p-4 order-5 sm:order-4">
        <div className="flex items-center gap-1.5 sm:gap-2 mb-2 sm:mb-3">
          <ListChecks className="w-4 h-4 sm:w-5 sm:h-5 text-rose-400" />
          <h2 className="text-white font-bold text-sm sm:text-base">Como jogar esta partida</h2>
        </div>

        <div className="space-y-2 sm:space-y-3">
          <div className="flex gap-2 sm:gap-3">
            <div className="bg-rose-400/20 rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-rose-400 text-xs sm:text-sm font-bold">1</span>
            </div>
            <div>
              <p className="text-white text-xs sm:text-sm font-medium">Copie o nome de usuário do seu oponente</p>
              <div className="mt-1 sm:mt-2 flex flex-wrap items-center gap-1.5 sm:gap-2">
                {players.filter(p => p.id !== currentPlayer.id).map((opponent) => (
                  <div
                    key={opponent.id}
                    className="bg-gray-700/50 backdrop-blur-sm border border-white/10 rounded-lg px-2 sm:px-3 py-1 sm:py-1.5 flex items-center gap-1 sm:gap-2 cursor-pointer hover:bg-gray-700/80 transition-colors mb-1 sm:mb-0"
                    onClick={() => handleCopyUsername(opponent.name)}
                  >
                    <span className="text-white text-xs sm:text-sm">{opponent.name}</span>
                    <Copy className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="flex gap-2 sm:gap-3">
            <div className="bg-rose-400/20 rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-rose-400 text-xs sm:text-sm font-bold">2</span>
            </div>
            <div>
              <p className="text-white text-xs sm:text-sm font-medium">Adicione o seu oponente na PSN ou Xbox</p>
              <p className="text-gray-400 text-[10px] sm:text-xs mt-0.5 sm:mt-1">Abra o aplicativo do console e adicione seu oponente como amigo</p>
            </div>
          </div>

          <div className="flex gap-2 sm:gap-3">
            <div className="bg-rose-400/20 rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-rose-400 text-xs sm:text-sm font-bold">3</span>
            </div>
            <div>
              <p className="text-white text-xs sm:text-sm font-medium">Chame para uma partida segundo as regras da sala</p>
              <p className="text-gray-400 text-[10px] sm:text-xs mt-0.5 sm:mt-1">Certifique-se de seguir todas as configurações definidas para esta partida</p>
            </div>
          </div>

          <div className="flex gap-2 sm:gap-3">
            <div className="bg-rose-400/20 rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-rose-400 text-xs sm:text-sm font-bold">4</span>
            </div>
            <div>
              <p className="text-white text-xs sm:text-sm font-medium">Jogue e tire print do resultado final</p>
              <p className="text-gray-400 text-[10px] sm:text-xs mt-0.5 sm:mt-1">Após a partida, tire uma screenshot da tela de resultados</p>
            </div>
          </div>

          <div className="flex gap-2 sm:gap-3">
            <div className="bg-rose-400/20 rounded-full w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-rose-400 text-xs sm:text-sm font-bold">5</span>
            </div>
            <div>
              <p className="text-white text-xs sm:text-sm font-medium">Qualquer dúvida, use o chat da sala abaixo</p>
              <p className="text-gray-400 text-[10px] sm:text-xs mt-0.5 sm:mt-1">Nossos moderadores estão disponíveis para ajudar em caso de problemas</p>
            </div>
          </div>
        </div>
      </div>

      {/* Players Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 order-3 sm:order-2">
        {players.map((player) => (
          <div
            key={player.id}
            className={`bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4 ${
              player.isReady ? 'border-2 border-green-400' : ''
            }`}
          >
            <div className="flex items-center gap-2 sm:gap-3">
              <img
                src={player.avatar}
                alt={player.name}
                className="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover"
              />
              <div>
                <div className="flex items-center gap-1 sm:gap-2">
                  <h3 className="text-white font-semibold text-sm sm:text-base">{player.name}</h3>
                  {player.isHost && (
                    <Crown className="w-3 h-3 sm:w-4 sm:h-4 text-rose-400" />
                  )}
                </div>
                <p className={`text-xs sm:text-sm ${player.isReady ? 'text-green-400' : 'text-gray-400'}`}>
                  {player.isReady ? 'Pronto' : 'Não pronto'}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Chat Section */}
      <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-3 sm:p-4 flex flex-col h-[350px] sm:h-80 order-2 sm:order-3">
        <div className="flex items-center justify-between gap-2 mb-3">
          <div className="flex items-center gap-1 sm:gap-2">
            <MessageCircle className="w-4 h-4 sm:w-5 sm:h-5 text-rose-400" />
            <h2 className="text-white font-bold text-sm sm:text-base">Chat da Sala</h2>
          </div>
          <button
            onClick={() => setShowInvite(true)}
            className="text-rose-400 hover:text-rose-300"
          >
            <UserPlus className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
        </div>

        {/* Messages Container */}
        <div
          ref={chatContainerRef}
          className="flex-1 overflow-y-auto space-y-2 sm:space-y-3 mb-2 sm:mb-3 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-800"
        >
          {messages.map((msg) => (
            <div key={msg.id} className="flex items-start gap-2 sm:gap-3">
              {msg.type === 'system' || msg.playerId === 'system' ? (
                <div className="bg-gray-700/50 backdrop-blur-sm border border-white/10 text-gray-400 text-xs sm:text-sm py-1 px-3 rounded-lg w-full text-center">
                  {msg.message}
                </div>
              ) : (
                <>
                  <img
                    src={msg.playerAvatar}
                    alt={msg.playerName}
                    className="w-6 h-6 sm:w-8 sm:h-8 rounded-full object-cover flex-shrink-0"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-1 sm:gap-2 mb-0.5 sm:mb-1">
                      <span className="text-white font-semibold text-xs sm:text-sm">{msg.playerName}</span>
                      <span className="text-gray-500 text-[10px] sm:text-xs">{formatMessageTime(msg.timestamp)}</span>
                    </div>
                    <p className="text-gray-300 break-words text-xs sm:text-sm">{msg.message}</p>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>

        {/* Message Input */}
        <form onSubmit={handleSendMessage} className="relative">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Digite sua mensagem..."
            className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-1.5 sm:py-2 pl-3 pr-10 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-rose-400"
          />
          <button
            type="submit"
            className="absolute right-2 top-1/2 -translate-y-1/2 text-rose-400 hover:text-rose-300"
          >
            <Send className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
        </form>
      </div>

      {/* Ready Button */}
      <button
        onClick={() => {
          const newReadyState = !isReady;
          setIsReady(newReadyState);

          // Atualizar via WebSocket
          if (socketService.connected) {
            socketService.setPlayerReady(newReadyState);
          }

          // Atualizar localmente
          setPlayers(prev => prev.map(p =>
            p.id === currentPlayer.id
              ? { ...p, isReady: newReadyState }
              : p
          ));
        }}
        disabled={!hasPaid || loading}
        className={`w-full py-2 sm:py-3 rounded-xl font-bold text-sm sm:text-base order-4 sticky bottom-4 z-10 ${
          !hasPaid || loading
            ? 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-gray-400 cursor-not-allowed'
            : isReady
              ? 'bg-green-500 text-white'
              : 'bg-gradient-to-r from-rose-400 to-pink-400 text-white'
        }`}
      >
        {loading ? 'Carregando...' : !hasPaid ? 'Aguardando pagamento...' : isReady ? 'Pronto!' : 'Ficar Pronto'}
      </button>

      {/* Payment Modal */}
      {showPaymentModal && !hasPaid && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl w-full max-w-md p-6">
            <div className="text-center space-y-6">
              <div className="bg-rose-400/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto">
                <Wallet className="w-8 h-8 text-rose-400" />
              </div>

              <div>
                <h2 className="text-xl sm:text-2xl font-bold text-white mb-2">Pagar Entrada</h2>
                <p className="text-gray-400 text-sm sm:text-base">
                  Para participar desta sala, é necessário pagar o valor da entrada
                </p>
              </div>

              <div className="bg-gray-700/50 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 space-y-2 sm:space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400 text-xs sm:text-sm">Seu Saldo:</span>
                  <span className="text-white font-bold text-xs sm:text-sm">R$ {userBalance.toFixed(2)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400 text-xs sm:text-sm">Valor da Entrada:</span>
                  <span className="text-rose-400 font-bold text-xs sm:text-sm">R$ {roomInfo.betAmount.toFixed(2)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400 text-xs sm:text-sm">Prêmio Total:</span>
                  <span className="text-green-400 font-bold text-xs sm:text-sm">R$ {roomInfo.totalPrize.toFixed(2)}</span>
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handlePayment}
                  disabled={userBalance < roomInfo.betAmount}
                  className={`w-full py-2 sm:py-3 rounded-xl font-bold text-sm sm:text-base ${
                    userBalance >= roomInfo.betAmount
                      ? 'bg-gradient-to-r from-rose-400 to-pink-400 text-white hover:opacity-90'
                      : 'bg-gray-700/50 backdrop-blur-sm border border-white/10 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {userBalance >= roomInfo.betAmount ? (
                    'Pagar e Entrar'
                  ) : (
                    'Saldo Insuficiente'
                  )}
                </button>

                <button
                  onClick={() => navigate(`/external-games/${gameId}`)}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white font-bold py-2 sm:py-3 rounded-xl hover:bg-gray-600/50 text-sm sm:text-base"
                >
                  Voltar
                </button>
              </div>

              {userBalance < roomInfo.betAmount && (
                <div className="flex items-center gap-1.5 sm:gap-2 text-red-400 text-xs sm:text-sm">
                  <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>Adicione mais fundos à sua carteira para participar</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Invite Modal */}
      {showInvite && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-white text-base sm:text-lg font-bold">Convidar Amigos</h3>
              <button
                onClick={() => setShowInvite(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="relative mb-4">
              <input
                type="text"
                placeholder="Procurar amigos..."
                className="w-full bg-gray-700/50 backdrop-blur-sm border border-white/10 text-white rounded-lg py-1.5 sm:py-2 pl-10 pr-4 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-rose-400"
              />
            </div>

            <div className="space-y-2">
              {[
                { id: '1', name: 'Maria Santos', avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80', online: true },
                { id: '2', name: 'Pedro Costa', avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80', online: false }
              ].map(friend => (
                <div key={friend.id} className="flex items-center justify-between p-2 sm:p-3 rounded-lg hover:bg-gray-700/50">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <div className="relative">
                      <img
                        src={friend.avatar}
                        alt={friend.name}
                        className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover"
                      />
                      <div className={`absolute -bottom-1 -right-1 w-2 h-2 sm:w-3 sm:h-3 rounded-full border-2 border-gray-800 ${
                        friend.online ? 'bg-green-500' : 'bg-gray-500'
                      }`} />
                    </div>
                    <div>
                      <p className="text-white text-sm sm:text-base">{friend.name}</p>
                      <p className="text-xs sm:text-sm text-gray-400">
                        {friend.online ? 'Online' : 'Offline'}
                      </p>
                    </div>
                  </div>
                  <button className="bg-gradient-to-r from-rose-400 to-pink-400 text-white font-semibold px-3 sm:px-4 py-1 rounded-lg hover:opacity-90 text-xs sm:text-sm">
                    Convidar
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Match Result Modal */}
      {showResultModal && (
        <MatchResultModal
          onClose={() => {
            setShowResultModal(false);
            setSelectedFile(null);
          }}
          onResultSubmit={(winner, player1Score, player2Score, awardPrize) => {
            // Processar o resultado (método original)
            addSystemMessage(`Resultado processado: ${winner} venceu com placar ${player1Score} - ${player2Score}`);

            // Fechar o modal
            setShowResultModal(false);
            setSelectedFile(null);

            // Navegar para a página de resultados com o matchId
            console.log('🔍 Verificando currentMatchId:', currentMatchId);
            console.log('🔍 Estado da partida - matchStarted:', matchStarted);

            if (currentMatchId) {
              console.log('✅ Navegando para resultados com Match ID:', currentMatchId);
              navigate(`/games/${gameId}/results/${currentMatchId}`);
            } else {
              console.warn('⚠️ Match ID não encontrado, tentando recuperar...');
              console.warn('🔍 Estado atual - currentMatchId:', currentMatchId, 'matchStarted:', matchStarted);

              // Tentar recuperar do localStorage
              const cachedMatchId = localStorage.getItem(`current_match_${roomId}`);
              if (cachedMatchId) {
                console.log('📦 Match ID recuperado do cache:', cachedMatchId);
                navigate(`/games/${gameId}/results/${cachedMatchId}`);
              } else {
                console.error('❌ Não foi possível recuperar o Match ID. Navegando para página de erro.');
                // Em vez de navegar para "unknown", vamos para uma página de erro específica
                navigate(`/games/${gameId}/results/error?reason=match_id_not_found`);
              }
            }
          }}
          onSubmitViaWebSocket={handleSubmitResult}
          useWebSocket={true}
          match={{
            id: roomId || '1',
            player1: {
              id: currentPlayer.id,
              name: currentPlayer.name,
              avatar: currentPlayer.avatar,
              // Adicionar informações extras para melhor identificação
              username: user.username,
              display_name: user.display_name
            },
            player2: {
              id: players.find(p => p.id !== currentPlayer.id)?.id || players[1]?.id || 'unknown',
              name: players.find(p => p.id !== currentPlayer.id)?.name || players[1]?.name || 'Oponente',
              avatar: players.find(p => p.id !== currentPlayer.id)?.avatar || players[1]?.avatar || '/default-avatar.png'
            },
            scheduledTime: new Date().toISOString()
          }}
          selectedFile={selectedFile}
          onFileSelect={setSelectedFile}
        />
      )}
    </div>
  );
}
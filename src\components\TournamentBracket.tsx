import React from 'react';
import { Match } from '../types/tournament';

interface TournamentBracketProps {
  matches: Match[];
  highlightParticipantId?: string;
  bracketName?: string;
  bracketStatus?: 'upcoming' | 'in_progress' | 'completed';
  winnerId?: string;
}

export default function TournamentBracket({
  matches,
  highlightParticipantId,
  bracketName,
  bracketStatus,
  winnerId
}: TournamentBracketProps) {
  // Group matches by round
  const matchesByRound: { [key: number]: Match[] } = {};
  matches.forEach(match => {
    if (!matchesByRound[match.round]) {
      matchesByRound[match.round] = [];
    }
    matchesByRound[match.round].push(match);
  });

  // Sort rounds
  const rounds = Object.keys(matchesByRound)
    .map(Number)
    .sort((a, b) => a - b);

  // Calculate bracket dimensions
  const maxRound = Math.max(...rounds);
  const bracketHeight = Math.pow(2, maxRound) * 80; // 80px per match in first round

  return (
    <div className="relative" style={{ minHeight: bracketHeight }}>
      <div className="flex">
        {rounds.map(round => (
          <div
            key={round}
            className="flex-1 flex flex-col"
            style={{
              gap: `${Math.pow(2, round) * 80 - 80}px`,
              marginTop: `${Math.pow(2, round - 1) * 40 - 40}px`
            }}
          >
            <div className="text-center mb-4">
              <h3 className="text-white font-medium">
                {round === 1 ? 'Primeira Rodada' :
                 round === maxRound ? 'Final' :
                 round === maxRound - 1 ? 'Semifinal' :
                 round === maxRound - 2 ? 'Quartas de Final' :
                 `Rodada ${round}`}
              </h3>
            </div>

            {matchesByRound[round]
              .sort((a, b) => a.position - b.position)
              .map(match => (
                <div
                  key={match.id}
                  className={`bg-gray-700/50 backdrop-blur-sm border border-white/10 rounded-lg p-3 mx-2 ${
                    match.status === 'completed' ? 'border-l-4 border-l-indigo-500' : ''
                  }`}
                >
                  <div className="flex flex-col gap-2">
                    <div className={`flex items-center gap-2 ${
                      match.winner === match.player1?.id ? 'bg-indigo-500/20 rounded-lg p-1' : ''
                    } ${
                      highlightParticipantId === match.player1?.id ? 'border border-purple-500/50 rounded-lg p-1' : ''
                    }`}>
                      {match.player1 ? (
                        <>
                          <img
                            src={match.player1.avatar}
                            alt={match.player1.name}
                            className="w-6 h-6 rounded-full object-cover"
                          />
                          <span className={`text-sm flex-1 truncate ${
                            highlightParticipantId === match.player1.id ? 'text-purple-400 font-medium' : 'text-white'
                          }`}>
                            {match.player1.name}
                            {highlightParticipantId === match.player1.id && ' (Você)'}
                          </span>
                          {match.status === 'completed' && (
                            <span className={`text-sm font-bold ${
                              match.winner === match.player1.id ? 'text-indigo-400' : 'text-gray-400'
                            }`}>
                              {match.player1.score}
                            </span>
                          )}
                        </>
                      ) : (
                        <span className="text-gray-500 text-sm">TBD</span>
                      )}
                    </div>

                    <div className={`flex items-center gap-2 ${
                      match.winner === match.player2?.id ? 'bg-indigo-500/20 rounded-lg p-1' : ''
                    } ${
                      highlightParticipantId === match.player2?.id ? 'border border-purple-500/50 rounded-lg p-1' : ''
                    }`}>
                      {match.player2 ? (
                        <>
                          <img
                            src={match.player2.avatar}
                            alt={match.player2.name}
                            className="w-6 h-6 rounded-full object-cover"
                          />
                          <span className={`text-sm flex-1 truncate ${
                            highlightParticipantId === match.player2.id ? 'text-purple-400 font-medium' : 'text-white'
                          }`}>
                            {match.player2.name}
                            {highlightParticipantId === match.player2.id && ' (Você)'}
                          </span>
                          {match.status === 'completed' && (
                            <span className={`text-sm font-bold ${
                              match.winner === match.player2.id ? 'text-indigo-400' : 'text-gray-400'
                            }`}>
                              {match.player2.score}
                            </span>
                          )}
                        </>
                      ) : (
                        <span className="text-gray-500 text-sm">TBD</span>
                      )}
                    </div>
                  </div>

                  {match.status === 'scheduled' && match.scheduledTime && (
                    <div className="mt-2 text-center">
                      <span className="text-gray-400 text-xs">{match.scheduledTime}</span>
                    </div>
                  )}
                </div>
              ))}
          </div>
        ))}
      </div>

      {/* Connector lines would be added here with absolute positioning */}

      {/* Bracket info */}
      {bracketName && bracketStatus && (
        <div className="mt-6 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="bg-indigo-500/20 text-indigo-400 px-3 py-1 rounded-lg flex items-center gap-2">
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              <span className="font-medium">{bracketName}</span>
            </div>
            <span className={`text-xs px-2 py-0.5 rounded ${
              bracketStatus === 'completed' ? 'bg-green-500/20 text-green-400' :
              bracketStatus === 'in_progress' ? 'bg-indigo-500/20 text-indigo-400' :
              'bg-gray-700 text-gray-400'
            }`}>
              {bracketStatus === 'completed' ? 'Concluída' :
               bracketStatus === 'in_progress' ? 'Em andamento' :
               'Aguardando'}
            </span>
          </div>

          {winnerId && (
            <div className="flex items-center gap-2 bg-yellow-500/20 text-yellow-400 px-3 py-1 rounded-lg">
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6" />
                <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18" />
                <path d="M4 22h16" />
                <path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22" />
                <path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22" />
                <path d="M9 6.5v-4a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 .5.5v4" />
                <path d="M9 10.5v-4a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 .5.5v4" />
                <path d="M12 12a4 4 0 0 0 4-4V6H8v2a4 4 0 0 0 4 4Z" />
              </svg>
              <span>Vencedor: {matches.find(m => m.player1?.id === winnerId)?.player1?.name || matches.find(m => m.player2?.id === winnerId)?.player2?.name || 'Desconhecido'}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

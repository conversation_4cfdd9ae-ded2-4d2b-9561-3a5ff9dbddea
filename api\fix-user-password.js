const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixUserPassword() {
  try {
    const email = '<EMAIL>';
    const password = 'Milhao123#';
    const userId = 'dfd3cd04-6f0f-4345-862b-5c11188dd7e2';
    
    console.log(`🔧 Setting password for user: ${email}`);
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);
    console.log('🔐 Password hashed successfully');
    console.log('Hash length:', hashedPassword.length);
    
    // Try updating by ID instead of email
    console.log('\n📝 Updating user by ID...');
    const { data, error } = await supabase
      .from('users')
      .update({ password_hash: hashedPassword })
      .eq('id', userId)
      .select('id, email, username, password_hash');
    
    if (error) {
      console.error('❌ Failed to set password:', error);
      
      // Try a different approach - upsert
      console.log('\n🔄 Trying upsert approach...');
      const { data: upsertData, error: upsertError } = await supabase
        .from('users')
        .upsert({ 
          id: userId,
          email: email,
          username: 'matt',
          display_name: 'matt',
          password_hash: hashedPassword 
        })
        .select('id, email, username');
      
      if (upsertError) {
        console.error('❌ Upsert also failed:', upsertError);
        return;
      }
      
      console.log('✅ Upsert successful:', upsertData);
    } else {
      console.log('✅ Update successful:', data);
    }
    
    // Verify the password was set
    console.log('\n🔍 Verifying password was set...');
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('id, email, username, password_hash')
      .eq('email', email)
      .single();
    
    if (verifyError) {
      console.error('❌ Failed to verify:', verifyError);
      return;
    }
    
    console.log('User data after update:');
    console.log('- ID:', verifyUser.id);
    console.log('- Email:', verifyUser.email);
    console.log('- Username:', verifyUser.username);
    console.log('- Password Hash exists:', !!verifyUser.password_hash);
    console.log('- Password Hash length:', verifyUser.password_hash ? verifyUser.password_hash.length : 0);
    
    if (verifyUser.password_hash) {
      const isValid = await bcrypt.compare(password, verifyUser.password_hash);
      console.log('✅ Password verification:', isValid ? 'SUCCESS' : 'FAILED');
    }
    
  } catch (error) {
    console.error('❌ Error setting password:', error);
  }
}

fixUserPassword();

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestUser() {
  try {
    const email = '<EMAIL>';
    const username = 'matt2';
    const password = 'Milhao123#';
    
    console.log(`👤 Creating test user: ${email}`);
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);
    console.log('🔐 Password hashed successfully');
    
    // Create the user with password
    const { data: newUser, error: createError } = await supabase
      .from('users')
      .insert({
        email: email,
        username: username,
        display_name: username,
        password_hash: hashedPassword,
        status: 'active',
        timezone: 'America/Sao_Paulo',
        language: 'pt-BR'
      })
      .select('*');
    
    if (createError) {
      console.error('❌ Failed to create user:', createError);
      return;
    }
    
    console.log('✅ User created successfully:');
    console.log('- ID:', newUser[0].id);
    console.log('- Email:', newUser[0].email);
    console.log('- Username:', newUser[0].username);
    console.log('- Password Hash exists:', !!newUser[0].password_hash);
    
    const userId = newUser[0].id;
    
    // Create related records
    console.log('\n📝 Creating related records...');
    
    // Create user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({ user_id: userId });
    
    // Create user stats
    const { error: statsError } = await supabase
      .from('user_stats')
      .insert({ user_id: userId });
    
    // Create wallet
    const { error: walletError } = await supabase
      .from('wallets')
      .insert({ user_id: userId });
    
    console.log('Related records created:');
    console.log('- Profile:', profileError ? `FAILED: ${profileError.message}` : 'SUCCESS');
    console.log('- Stats:', statsError ? `FAILED: ${statsError.message}` : 'SUCCESS');
    console.log('- Wallet:', walletError ? `FAILED: ${walletError.message}` : 'SUCCESS');
    
    // Test password verification
    console.log('\n🔍 Testing password verification...');
    const isValid = await bcrypt.compare(password, newUser[0].password_hash);
    console.log('✅ Password verification:', isValid ? 'SUCCESS' : 'FAILED');
    
    console.log('\n🎉 Test user created successfully!');
    console.log('You can now test login with:');
    console.log(`Email: ${email}`);
    console.log(`Password: ${password}`);
    
  } catch (error) {
    console.error('❌ Error creating test user:', error);
  }
}

createTestUser();

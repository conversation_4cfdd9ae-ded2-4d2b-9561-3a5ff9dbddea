import { apiService } from './apiService';

// Serviço para testar a conectividade com a API
export class ApiTestService {
  
  // Testa se a API está respondendo
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔍 Testando conectividade com a API...');
      
      const isHealthy = await apiService.healthCheck();
      
      if (isHealthy) {
        console.log('✅ API está respondendo!');
        return true;
      } else {
        console.log('❌ API não está respondendo');
        return false;
      }
    } catch (error) {
      console.error('❌ Erro ao testar conectividade:', error);
      return false;
    }
  }

  // Testa o endpoint de informações da API
  async testApiInfo(): Promise<void> {
    try {
      console.log('🔍 Testando endpoint de informações da API...');
      
      const response = await apiService.get('/');
      
      if (response.success !== false) {
        console.log('✅ Endpoint de informações funcionando!');
        console.log('📋 Informações da API:', response);
      } else {
        console.log('❌ Endpoint de informações com erro:', response.error);
      }
    } catch (error) {
      console.error('❌ Erro ao testar endpoint de informações:', error);
    }
  }

  // Testa o endpoint de health check
  async testHealthCheck(): Promise<void> {
    try {
      console.log('🔍 Testando health check...');
      
      // Fazendo chamada direta para /health (não /api/health)
      const response = await fetch('http://localhost:3001/health');
      const data = await response.json();
      
      if (response.ok) {
        console.log('✅ Health check funcionando!');
        console.log('🏥 Status da saúde:', data);
      } else {
        console.log('❌ Health check com erro:', data);
      }
    } catch (error) {
      console.error('❌ Erro ao testar health check:', error);
    }
  }

  // Testa autenticação mock
  async testMockAuth(): Promise<void> {
    try {
      console.log('🔍 Testando autenticação mock...');
      
      // Teste de login
      const loginResponse = await apiService.post('/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      });
      
      if (loginResponse.success) {
        console.log('✅ Login mock funcionando!');
        console.log('👤 Dados do usuário:', loginResponse.data?.user);
        console.log('🔑 Token recebido:', loginResponse.data?.tokens?.access_token);
        
        // Salva o token para próximos testes
        if (loginResponse.data?.tokens?.access_token) {
          apiService.setAuthToken(loginResponse.data.tokens.access_token);
          
          // Testa endpoint protegido
          await this.testProtectedEndpoint();
        }
      } else {
        console.log('❌ Login mock com erro:', loginResponse.error);
      }
    } catch (error) {
      console.error('❌ Erro ao testar autenticação mock:', error);
    }
  }

  // Testa endpoint protegido
  async testProtectedEndpoint(): Promise<void> {
    try {
      console.log('🔍 Testando endpoint protegido...');
      
      const profileResponse = await apiService.get('/auth/profile');
      
      if (profileResponse.success) {
        console.log('✅ Endpoint protegido funcionando!');
        console.log('👤 Perfil do usuário:', profileResponse.data);
      } else {
        console.log('❌ Endpoint protegido com erro:', profileResponse.error);
      }
    } catch (error) {
      console.error('❌ Erro ao testar endpoint protegido:', error);
    }
  }

  // Testa endpoint de torneios
  async testTournaments(): Promise<void> {
    try {
      console.log('🔍 Testando endpoint de torneios...');
      
      const tournamentsResponse = await apiService.get('/tournaments');
      
      if (tournamentsResponse.success) {
        console.log('✅ Endpoint de torneios funcionando!');
        console.log('🏆 Torneios encontrados:', tournamentsResponse.data?.length || 0);
        
        // Testa endpoint específico de torneio
        if (tournamentsResponse.data && tournamentsResponse.data.length > 0) {
          const tournamentId = tournamentsResponse.data[0].id;
          await this.testSpecificTournament(tournamentId);
        }
      } else {
        console.log('❌ Endpoint de torneios com erro:', tournamentsResponse.error);
      }
    } catch (error) {
      console.error('❌ Erro ao testar endpoint de torneios:', error);
    }
  }

  // Testa endpoint específico de torneio
  async testSpecificTournament(tournamentId: string): Promise<void> {
    try {
      console.log(`🔍 Testando torneio específico: ${tournamentId}...`);
      
      const tournamentResponse = await apiService.get(`/tournaments/${tournamentId}`);
      
      if (tournamentResponse.success) {
        console.log('✅ Endpoint de torneio específico funcionando!');
        console.log('🏆 Detalhes do torneio:', tournamentResponse.data?.title);
      } else {
        console.log('❌ Endpoint de torneio específico com erro:', tournamentResponse.error);
      }
    } catch (error) {
      console.error('❌ Erro ao testar torneio específico:', error);
    }
  }

  // Executa todos os testes
  async runAllTests(): Promise<void> {
    console.log('🚀 Iniciando testes da API...\n');
    
    // Teste 1: Conectividade básica
    const isConnected = await this.testConnection();
    if (!isConnected) {
      console.log('❌ API não está acessível. Verifique se o servidor está rodando.');
      return;
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Teste 2: Health check
    await this.testHealthCheck();
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Teste 3: Informações da API
    await this.testApiInfo();
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Teste 4: Autenticação mock
    await this.testMockAuth();
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Teste 5: Torneios
    await this.testTournaments();
    
    console.log('\n🎉 Testes concluídos!');
  }
}

// Instância singleton
export const apiTestService = new ApiTestService();

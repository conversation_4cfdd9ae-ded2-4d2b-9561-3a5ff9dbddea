# Playstrike Database Schema

Este diretório contém todas as migrations do banco de dados para a plataforma Playstrike, desenvolvida para o Supabase PostgreSQL.

## 📋 Visão Geral

O esquema do banco de dados foi projetado para suportar uma plataforma completa de jogos competitivos com as seguintes funcionalidades:

- ✅ Sistema de usuários e autenticação
- ✅ Carteira digital com transações
- ✅ Jogos internos e externos
- ✅ Sistema de matchmaking
- ✅ Torneios com chaves e temporadas
- ✅ Recursos sociais (amigos, clubes, chat)
- ✅ Programa de afiliados
- ✅ Rankings e estatísticas
- ✅ Sistema de notificações
- ✅ Conquistas e gamificação
- ✅ Streams e conteúdo

## 🗂️ Estrutura das Migrations

### Migration 001: Schema Inicial
**Arquivo:** `001_initial_schema.sql`
**Descrição:** Cria as tabelas principais do sistema

**Tabelas criadas:**
- `users` - Dados básicos dos usuários
- `user_profiles` - Perfis estendidos dos usuários
- `user_stats` - Estatísticas dos usuários
- `wallets` - Carteiras digitais
- `transactions` - Transações financeiras
- `games` - Jogos disponíveis na plataforma

**Tipos customizados:**
- `user_status` - Status do usuário (active, inactive, banned, suspended)
- `transaction_type` - Tipos de transação (deposit, withdrawal, bet, win, commission, refund)
- `transaction_status` - Status da transação (pending, completed, failed, cancelled)
- `payment_method` - Métodos de pagamento (pix, credit_card, bank_transfer)
- `match_status` - Status da partida (scheduled, in_progress, completed, cancelled, disputed)
- `tournament_status` - Status do torneio (upcoming, registration_open, in_progress, completed, cancelled)
- `game_type` - Tipo do jogo (internal, external)
- `room_status` - Status da sala (waiting, starting, playing, finished)
- `friend_request_status` - Status da solicitação de amizade (pending, accepted, declined)
- `notification_type` - Tipos de notificação (friend_request, match_found, tournament_update, system, achievement)

### Migration 002: Partidas e Salas
**Arquivo:** `002_matches_and_rooms.sql`
**Descrição:** Sistema de salas de jogos, partidas e matchmaking

**Tabelas criadas:**
- `game_rooms` - Salas de jogos
- `room_participants` - Participantes das salas
- `matches` - Partidas
- `match_participants` - Participantes das partidas
- `match_disputes` - Disputas de resultados
- `matchmaking_queue` - Fila de matchmaking
- `room_chat_messages` - Mensagens do chat das salas

### Migration 003: Torneios
**Arquivo:** `003_tournaments.sql`
**Descrição:** Sistema completo de torneios com temporadas e chaves

**Tabelas criadas:**
- `tournaments` - Torneios
- `tournament_seasons` - Temporadas dos torneios
- `tournament_brackets` - Chaves dos torneios
- `tournament_participants` - Participantes dos torneios
- `tournament_match_schedule` - Cronograma das partidas
- `tournament_prizes` - Prêmios dos torneios
- `tournament_announcements` - Anúncios dos torneios

### Migration 004: Recursos Sociais
**Arquivo:** `004_social_features.sql`
**Descrição:** Sistema social completo com amigos, clubes e chat

**Tabelas criadas:**
- `friendships` - Amizades entre usuários
- `clubs` - Clubes/grupos
- `club_members` - Membros dos clubes
- `club_invitations` - Convites para clubes
- `private_conversations` - Conversas privadas
- `private_messages` - Mensagens privadas
- `club_messages` - Mensagens dos clubes
- `friend_challenges` - Desafios entre amigos
- `user_blocks` - Usuários bloqueados
- `user_reports` - Denúncias de usuários

### Migration 005: Programa de Afiliados
**Arquivo:** `005_affiliate_program.sql`
**Descrição:** Sistema completo de afiliados com comissões e campanhas

**Tabelas criadas:**
- `affiliate_programs` - Programas de afiliados
- `user_referrals` - Indicações de usuários
- `commission_transactions` - Transações de comissão
- `affiliate_tiers` - Níveis de afiliados
- `affiliate_payouts` - Pagamentos de comissões
- `affiliate_campaigns` - Campanhas de marketing
- `affiliate_campaign_participants` - Participantes das campanhas
- `referral_tracking_links` - Links de rastreamento
- `link_click_tracking` - Rastreamento de cliques

### Migration 006: Notificações e Sistema
**Arquivo:** `006_notifications_and_system.sql`
**Descrição:** Sistema de notificações, conquistas, rankings e recursos do sistema

**Tabelas criadas:**
- `notifications` - Notificações dos usuários
- `achievements` - Conquistas disponíveis
- `user_achievements` - Conquistas dos usuários
- `global_rankings` - Rankings globais
- `seasons` - Temporadas
- `user_season_stats` - Estatísticas por temporada
- `streams` - Transmissões ao vivo
- `stream_viewers` - Visualizadores das streams
- `system_settings` - Configurações do sistema
- `maintenance_schedules` - Cronograma de manutenção
- `game_statistics` - Estatísticas dos jogos
- `user_activity_logs` - Logs de atividade

### Migration 007: Políticas RLS
**Arquivo:** `007_rls_policies.sql`
**Descrição:** Row Level Security (RLS) para segurança dos dados

**Recursos:**
- Habilita RLS em todas as tabelas
- Cria políticas de segurança para acesso aos dados
- Garante que usuários só acessem seus próprios dados
- Permite acesso público a dados apropriados (rankings, jogos, etc.)

### Migration 008: Funções e Triggers
**Arquivo:** `008_functions_and_triggers.sql`
**Descrição:** Lógica de negócio automatizada através de funções e triggers

**Funções criadas:**
- `create_user_profile_and_wallet()` - Cria perfil e carteira automaticamente
- `update_wallet_balance()` - Atualiza saldo da carteira
- `update_user_stats_after_match()` - Atualiza estatísticas após partidas
- `create_commission_transaction()` - Cria transações de comissão
- `update_room_participant_count()` - Atualiza contadores de participantes
- `update_tournament_participant_count()` - Atualiza contadores de torneios
- `update_club_member_count()` - Atualiza contadores de clubes

### Migration 009: Dados Iniciais
**Arquivo:** `009_seed_data.sql`
**Descrição:** Dados iniciais para funcionamento da plataforma

**Dados inseridos:**
- Jogos padrão (internos e externos)
- Níveis de afiliados
- Conquistas básicas
- Configurações do sistema
- Temporada inicial
- Campanhas de afiliados
- Estatísticas iniciais

## 🚀 Como Executar as Migrations

### No Supabase Dashboard:
1. Acesse o Supabase Dashboard
2. Vá para "SQL Editor"
3. Execute as migrations na ordem (001 → 009)

### Via CLI do Supabase:
```bash
# Instalar Supabase CLI
npm install -g supabase

# Login no Supabase
supabase login

# Executar migrations
supabase db push
```

### Via psql (PostgreSQL):
```bash
# Conectar ao banco
psql "postgresql://[user]:[password]@[host]:[port]/[database]"

# Executar cada migration
\i database/migrations/001_initial_schema.sql
\i database/migrations/002_matches_and_rooms.sql
\i database/migrations/003_tournaments.sql
\i database/migrations/004_social_features.sql
\i database/migrations/005_affiliate_program.sql
\i database/migrations/006_notifications_and_system.sql
\i database/migrations/007_rls_policies.sql
\i database/migrations/008_functions_and_triggers.sql
\i database/migrations/009_seed_data.sql
```

## 📊 Principais Relacionamentos

### Usuários e Perfis
```
users (1:1) user_profiles
users (1:1) user_stats
users (1:1) wallets
users (1:1) affiliate_programs
```

### Jogos e Partidas
```
games (1:N) game_rooms
game_rooms (1:N) room_participants
games (1:N) matches
matches (1:N) match_participants
```

### Torneios
```
tournaments (1:N) tournament_seasons
tournament_seasons (1:N) tournament_brackets
tournaments (1:N) tournament_participants
tournaments (1:N) matches
```

### Social
```
users (N:N) friendships
clubs (1:N) club_members
users (N:N) private_conversations
private_conversations (1:N) private_messages
```

### Afiliados
```
users (1:1) affiliate_programs
users (1:N) user_referrals (as referrer)
users (1:1) user_referrals (as referred)
transactions (1:N) commission_transactions
```

## 🔒 Segurança

### Row Level Security (RLS)
- Todas as tabelas têm RLS habilitado
- Usuários só podem acessar seus próprios dados
- Dados públicos (jogos, rankings) são acessíveis a todos
- Administradores têm acesso completo via service role

### Políticas de Acesso
- **Usuários**: Acesso total aos próprios dados
- **Perfis**: Visibilidade baseada em configurações de privacidade
- **Transações**: Apenas o próprio usuário pode ver
- **Partidas**: Participantes podem ver detalhes
- **Torneios**: Dados públicos visíveis a todos
- **Clubes**: Membros podem ver dados do clube
- **Chat**: Apenas participantes podem ver mensagens

## 🔧 Manutenção

### Backup
```sql
-- Backup completo
pg_dump -h [host] -U [user] -d [database] > backup.sql

-- Backup apenas dados
pg_dump -h [host] -U [user] -d [database] --data-only > data_backup.sql
```

### Monitoramento
- Use as tabelas `user_activity_logs` para auditoria
- Monitore `game_statistics` para métricas de performance
- Acompanhe `system_settings` para configurações críticas

### Otimização
- Todos os índices necessários já estão criados
- Use `EXPLAIN ANALYZE` para otimizar queries específicas
- Considere particionamento para tabelas grandes (logs, estatísticas)

## 📈 Escalabilidade

### Considerações para Crescimento
1. **Particionamento**: Considere particionar tabelas de logs por data
2. **Índices**: Monitore e adicione índices conforme necessário
3. **Arquivamento**: Implemente estratégia de arquivamento para dados antigos
4. **Cache**: Use Redis para dados frequentemente acessados
5. **Read Replicas**: Configure réplicas de leitura para queries pesadas

### Métricas Importantes
- Número de usuários ativos
- Volume de transações por dia
- Número de partidas simultâneas
- Tamanho das tabelas de logs
- Performance das queries principais

## 🐛 Troubleshooting

### Problemas Comuns
1. **Erro de permissão**: Verifique se RLS está configurado corretamente
2. **Constraint violation**: Verifique dados de entrada e relacionamentos
3. **Performance lenta**: Analise índices e otimize queries
4. **Deadlocks**: Revise ordem de locks em transações

### Logs Úteis
```sql
-- Ver queries lentas
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Ver locks ativos
SELECT * FROM pg_locks WHERE NOT granted;

-- Ver atividade atual
SELECT * FROM pg_stat_activity WHERE state = 'active';
```

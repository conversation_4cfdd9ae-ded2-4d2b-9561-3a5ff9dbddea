import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Trophy,
  Search,
  Filter,
  Calendar,
  Users,
  Clock,
  Plus,
  ChevronRight,
  X,
  Gamepad2,
  Wallet,
  Loader2
} from 'lucide-react';
import CreateTournamentModal from '../components/CreateTournamentModal';
import { realTournamentService } from '../services/realTournamentService';
import { Tournament } from '../types/tournament';

export default function TournamentsPage() {
  const navigate = useNavigate();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGame, setSelectedGame] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'upcoming' | 'ongoing' | 'completed'>('all');

  // Estado para dados da API
  const [tournaments, setTournaments] = useState<Tournament[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carrega torneios da API
  useEffect(() => {
    const loadTournaments = async () => {
      try {
        setIsLoading(true);
        setError(null);
        console.log('🔄 Carregando torneios...');

        const data = await realTournamentService.getTournaments();
        setTournaments(data);

        console.log(`✅ ${data.length} torneios carregados`);
      } catch (err) {
        console.error('❌ Erro ao carregar torneios:', err);
        setError('Erro ao carregar torneios. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadTournaments();
  }, []);

  // Games for filtering
  const games = [
    { id: 'cs2', name: 'Counter-Strike 2' },
    { id: 'fifa', name: 'FIFA 24' },
    { id: 'ml', name: 'Mobile Legends' },
    { id: 'cod', name: 'Call of Duty: Mobile' },
    { id: 'rocket', name: 'Foguete Espacial' }
  ];

  // Filter tournaments based on search, game, and status
  const filteredTournaments = tournaments.filter(tournament => {
    // Filter by search query
    if (searchQuery && !tournament.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Filter by game
    if (selectedGame) {
      const game = games.find(g => g.id === selectedGame);
      if (game && tournament.game !== game.name) {
        return false;
      }
    }

    // Filter by status
    if (selectedStatus !== 'all' && tournament.status !== selectedStatus) {
      return false;
    }

    return true;
  });

  const handleCreateTournament = (tournamentData: any) => {
    // Here you would typically send the data to your backend
    console.log('Creating tournament:', tournamentData);
    setShowCreateModal(false);
    // Optionally navigate to the new tournament page
    // navigate(`/tournaments/${newTournamentId}`);
  };

  const getStatusBadgeClass = (status: Tournament['status']) => {
    switch (status) {
      case 'upcoming':
        return 'bg-indigo-500/20 text-indigo-400';
      case 'ongoing':
        return 'bg-green-500/20 text-green-400';
      case 'completed':
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getStatusText = (status: Tournament['status']) => {
    switch (status) {
      case 'upcoming':
        return 'Em breve';
      case 'ongoing':
        return 'Em andamento';
      case 'completed':
        return 'Concluído';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Função para lidar com dados da API que podem ter estrutura diferente
  const getTournamentGameIcon = (tournament: Tournament) => {
    // Se a API não retornar gameIcon, usa uma imagem padrão baseada no jogo
    if (tournament.gameIcon) {
      return tournament.gameIcon;
    }

    // Imagens padrão baseadas no nome do jogo
    const gameIcons: Record<string, string> = {
      'Counter-Strike 2': 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80',
      'FIFA 24': 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80',
      'Mobile Legends': 'https://images.unsplash.com/photo-1560419015-7c427e8ae5ba?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80',
      'Call of Duty: Mobile': 'https://images.unsplash.com/photo-1509198397868-475647b2a1e5?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80',
      'Foguete Espacial': 'https://images.unsplash.com/photo-1614732414444-096e5f1122d5?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80'
    };

    return gameIcons[tournament.game] || 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80';
  };

  return (
    <div className="space-y-6 animate-fadeIn">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-white mb-1">Torneios</h1>
          <p className="text-gray-400 text-sm sm:text-base">Participe de competições e ganhe prêmios</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-3 sm:px-4 py-2 rounded-lg flex items-center gap-2 hover:opacity-90 shadow-lg transition-all duration-300 text-sm sm:text-base self-start sm:self-auto"
        >
          <Plus className="w-4 h-4 sm:w-5 sm:h-5" />
          <span>Criar Torneio</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Buscar torneios..."
            className="w-full bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white rounded-xl py-2 sm:py-3 pl-9 sm:pl-10 pr-4 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-indigo-400"
          />
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-2 overflow-x-auto hide-scrollbar pb-1">
          <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 p-2 rounded-lg">
            <Filter className="w-5 h-5 text-indigo-400" />
          </div>

          {/* Game Filter */}
          {games.map(game => (
            <button
              key={game.id}
              onClick={() => setSelectedGame(selectedGame === game.id ? null : game.id)}
              className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 text-xs sm:text-sm whitespace-nowrap ${
                selectedGame === game.id
                  ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                  : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
              }`}
            >
              {game.name}
            </button>
          ))}

          {/* Status Filter */}
          <button
            onClick={() => setSelectedStatus('all')}
            className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 text-xs sm:text-sm whitespace-nowrap ${
              selectedStatus === 'all'
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Todos
          </button>
          <button
            onClick={() => setSelectedStatus('upcoming')}
            className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 text-xs sm:text-sm whitespace-nowrap ${
              selectedStatus === 'upcoming'
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Em breve
          </button>
          <button
            onClick={() => setSelectedStatus('ongoing')}
            className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 text-xs sm:text-sm whitespace-nowrap ${
              selectedStatus === 'ongoing'
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Em andamento
          </button>
          <button
            onClick={() => setSelectedStatus('completed')}
            className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all duration-200 text-xs sm:text-sm whitespace-nowrap ${
              selectedStatus === 'completed'
                ? 'bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-800/50 backdrop-blur-sm border border-white/10 text-white hover:bg-gray-700/50'
            }`}
          >
            Concluídos
          </button>

          {/* Clear Filters */}
          {(selectedGame || selectedStatus !== 'all' || searchQuery) && (
            <button
              onClick={() => {
                setSelectedGame(null);
                setSelectedStatus('all');
                setSearchQuery('');
              }}
              className="px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg bg-gray-800/50 backdrop-blur-sm border border-white/10 text-gray-400 hover:text-white hover:bg-gray-700/50 transition-all duration-200 flex items-center gap-1 text-xs sm:text-sm whitespace-nowrap"
            >
              <X className="w-4 h-4" />
              Limpar filtros
            </button>
          )}
        </div>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Loader2 className="w-8 h-8 text-indigo-400 mx-auto mb-4 animate-spin" />
          <h3 className="text-white font-bold text-lg mb-2">Carregando torneios...</h3>
          <p className="text-gray-400">Aguarde enquanto buscamos os torneios disponíveis.</p>
        </div>
      ) : error ? (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Trophy className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-white font-bold text-lg mb-2">Erro ao carregar torneios</h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg hover:opacity-90 shadow-lg transition-all duration-300"
          >
            Tentar novamente
          </button>
        </div>
      ) : filteredTournaments.length > 0 ? (
        <div className="grid grid-cols-1 gap-4">
          {filteredTournaments.map(tournament => (
            <div
              key={tournament.id}
              className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden hover:bg-gray-700/50 transition-all duration-300 cursor-pointer"
              onClick={() => navigate(`/tournaments/${tournament.id}`)}
            >
              <div className="p-4">
                <div className="flex flex-col sm:flex-row sm:items-start gap-4">
                  {/* Game Icon */}
                  <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-2 flex-shrink-0">
                    <img
                      src={getTournamentGameIcon(tournament)}
                      alt={tournament.game}
                      className="w-12 h-12 object-cover rounded-md"
                    />
                  </div>

                  {/* Tournament Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-gray-400 text-sm">{tournament.game}</span>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${getStatusBadgeClass(tournament.status)}`}>
                        {getStatusText(tournament.status)}
                      </span>
                    </div>

                    <h2 className="text-white font-bold text-lg mb-2">{tournament.title}</h2>

                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4 text-xs sm:text-sm">
                      <div className="flex items-center gap-1.5">
                        <Calendar className="w-4 h-4 text-indigo-400" />
                        <span className="text-gray-300">
                          {formatDate(tournament.startDate)}
                          {tournament.format === 'points' && tournament.startDate !== tournament.endDate &&
                            ` - ${formatDate(tournament.endDate)}`
                          }
                        </span>
                      </div>

                      <div className="flex items-center gap-1.5">
                        <Trophy className="w-4 h-4 text-yellow-400" />
                        <span className="text-gray-300">R$ {tournament.prizePool.toLocaleString()}</span>
                      </div>

                      <div className="flex items-center gap-1.5">
                        <Users className="w-4 h-4 text-indigo-400" />
                        <span className="text-gray-300">
                          {tournament.participants}/{tournament.maxParticipants} participantes
                        </span>
                      </div>

                      <div className="flex items-center gap-1.5">
                        <Wallet className="w-4 h-4 text-green-400" />
                        <span className="text-gray-300">R$ {tournament.entryFee} de inscrição</span>
                      </div>
                    </div>
                  </div>

                  {/* Action Button */}
                  <div className="flex items-center self-center mt-4 sm:mt-0">
                    <ChevronRight className="w-6 h-6 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-white font-bold text-lg mb-2">Nenhum torneio encontrado</h3>
          <p className="text-gray-400 mb-4">
            {searchQuery || selectedGame || selectedStatus !== 'all'
              ? 'Não encontramos torneios com os filtros selecionados.'
              : 'Não há torneios disponíveis no momento.'}
          </p>
          {(searchQuery || selectedGame || selectedStatus !== 'all') && (
            <button
              onClick={() => {
                setSelectedGame(null);
                setSelectedStatus('all');
                setSearchQuery('');
              }}
              className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg inline-flex items-center gap-2 hover:opacity-90 shadow-lg transition-all duration-300 hover:scale-105 text-xs sm:text-sm"
            >
              <X className="w-4 h-4" />
              Limpar filtros
            </button>
          )}
        </div>
      )}

      {/* Create Tournament Modal */}
      {showCreateModal && (
        <CreateTournamentModal
          onClose={() => setShowCreateModal(false)}
          onCreate={handleCreateTournament}
          games={games.map(game => ({ id: game.id, title: game.name }))}
        />
      )}
    </div>
  );
}

import React from 'react';
import { Trophy, Medal } from 'lucide-react';

interface Participant {
  id: string;
  name: string;
  avatar: string;
  status: 'registered' | 'confirmed' | 'eliminated';
  position?: number;
  score?: number;
}

interface TournamentLeaderboardProps {
  participants: Participant[];
}

export default function TournamentLeaderboard({ participants }: TournamentLeaderboardProps) {
  // Sort participants by score (highest first)
  const sortedParticipants = [...participants]
    .filter(p => p.score !== undefined)
    .sort((a, b) => (b.score || 0) - (a.score || 0));
  
  const getPositionDisplay = (position: number) => {
    if (position === 1) {
      return (
        <div className="bg-yellow-400/20 p-1.5 rounded-full">
          <Trophy className="w-5 h-5 text-yellow-400" />
        </div>
      );
    } else if (position === 2) {
      return (
        <div className="bg-gray-400/20 p-1.5 rounded-full">
          <Medal className="w-5 h-5 text-gray-400" />
        </div>
      );
    } else if (position === 3) {
      return (
        <div className="bg-amber-600/20 p-1.5 rounded-full">
          <Medal className="w-5 h-5 text-amber-600" />
        </div>
      );
    } else {
      return (
        <div className="w-9 h-9 flex items-center justify-center">
          <span className="text-gray-400 font-medium">{position}</span>
        </div>
      );
    }
  };
  
  return (
    <div className="overflow-hidden">
      <div className="grid grid-cols-12 gap-4 p-3 border-b border-gray-700 text-gray-400 text-sm">
        <div className="col-span-1">Pos.</div>
        <div className="col-span-7">Jogador</div>
        <div className="col-span-2 text-right">Pontos</div>
        <div className="col-span-2 text-right">Status</div>
      </div>
      
      {sortedParticipants.map((participant, index) => (
        <div 
          key={participant.id}
          className={`grid grid-cols-12 gap-4 p-3 border-b border-gray-700 hover:bg-gray-700/30 transition-colors ${
            index < 3 ? 'bg-gray-700/20' : ''
          }`}
        >
          <div className="col-span-1 flex items-center justify-center">
            {getPositionDisplay(index + 1)}
          </div>
          
          <div className="col-span-7 flex items-center gap-3">
            <img
              src={participant.avatar}
              alt={participant.name}
              className="w-8 h-8 rounded-full object-cover"
            />
            <span className="text-white font-medium">{participant.name}</span>
          </div>
          
          <div className="col-span-2 text-right flex items-center justify-end">
            <span className="text-white font-bold">{participant.score}</span>
          </div>
          
          <div className="col-span-2 text-right">
            <span className={`text-xs px-2 py-1 rounded-full ${
              participant.status === 'confirmed' ? 'bg-green-500/20 text-green-400' :
              participant.status === 'registered' ? 'bg-yellow-500/20 text-yellow-400' :
              'bg-red-500/20 text-red-400'
            }`}>
              {participant.status === 'confirmed' ? 'Ativo' :
               participant.status === 'registered' ? 'Pendente' :
               'Eliminado'}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
}

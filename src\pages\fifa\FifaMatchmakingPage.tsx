import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Trophy,
  Users,
  Timer,
  Settings,
  Gamepad2,
  Shield,
  X,
  Copy,
  MessageCircle,
  Wallet,
  AlertCircle,
  Check,
  Loader2
} from 'lucide-react';
import { realMatchService } from '../../services/realMatchService';

interface MatchSettings {
  gameMode: 'friendlies' | 'fut' | 'seasons';
  matchDuration: 4 | 6 | 8 | 10 | 12;
  cameraType: 'default' | 'tele_broadcast';
  controlType: 'manual' | 'semi_assisted' | 'automatic';
  allowedTeams: 'clubs_only' | 'all';
  overtime: boolean;
  weather: 'clear' | 'random';
  tiebreaker: 'replay' | 'overtime' | 'penalties';
  disconnectRule: 'winning_player' | 'online_player';
  betAmount: number;
}

interface Player {
  id: string;
  name: string;
  avatar: string;
  platform: 'psn' | 'xbox';
  platformId: string;
  rating: number;
  status: 'searching' | 'ready' | 'offline' | 'waiting_payment' | 'paid';
}

interface Message {
  id: string;
  playerId: string;
  text: string;
  timestamp: Date;
}

export default function FifaMatchmakingPage() {
  const navigate = useNavigate();
  const { gameId } = useParams();
  const [matchSettings, setMatchSettings] = useState<MatchSettings>({
    gameMode: 'friendlies',
    matchDuration: 6,
    cameraType: 'default',
    controlType: 'semi_assisted',
    allowedTeams: 'all',
    overtime: true,
    weather: 'clear',
    tiebreaker: 'penalties',
    disconnectRule: 'winning_player',
    betAmount: 10
  });
  const [isMatchmaking, setIsMatchmaking] = useState(false);
  const [foundMatch, setFoundMatch] = useState(false);
  const [opponent, setOpponent] = useState<Player | null>(null);
  const [showChat, setShowChat] = useState(false);
  const [searchTime, setSearchTime] = useState(0);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [currentPlayer, setCurrentPlayer] = useState<Player>(() => {
    // Usar nome do cache ou fallback
    const cachedName = localStorage.getItem('playerName') || 'Jogador';
    return {
      id: '1',
      name: cachedName,
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      platform: 'psn',
      platformId: `${cachedName.toLowerCase().replace(/\s+/g, '_')}_psn`,
      rating: 1200,
      status: 'ready'
    };
  });

  const handleStartMatchmaking = async () => {
    try {
      setIsMatchmaking(true);
      setCurrentPlayer(prev => ({ ...prev, status: 'searching' }));

      console.log('🔄 Iniciando matchmaking FIFA...');

      // Tentar usar API real primeiro
      const matchmakingRequest = {
        game_id: 'fifa24',
        bet_amount: matchSettings.betAmount,
        preferred_rating_range: {
          min: currentPlayer.rating - 200,
          max: currentPlayer.rating + 200
        },
        game_settings: {
          gameMode: matchSettings.gameMode,
          matchDuration: matchSettings.matchDuration,
          cameraType: matchSettings.cameraType,
          controlType: matchSettings.controlType,
          allowedTeams: matchSettings.allowedTeams,
          overtime: matchSettings.overtime,
          weather: matchSettings.weather,
          tiebreaker: matchSettings.tiebreaker,
          disconnectRule: matchSettings.disconnectRule
        }
      };

      const result = await realMatchService.startMatchmaking(matchmakingRequest);

      if (result.success) {
        console.log('✅ Matchmaking FIFA iniciado via API:', result.matchmaking_id);
        // Simular encontrar oponente após criar match
        setTimeout(() => {
          const simulatedOpponent: Player = {
            id: '2',
            name: 'Ana Silva',
            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            platform: 'psn',
            platformId: 'ana_silva_psn',
            rating: 1250,
            status: 'waiting_payment'
          };
          setOpponent(simulatedOpponent);
          setFoundMatch(true);
          setCurrentPlayer(prev => ({ ...prev, status: 'waiting_payment' }));
        }, 3000);
      } else {
        throw new Error('Falha ao criar match');
      }

    } catch (error) {
      console.error('❌ Erro no matchmaking FIFA, usando simulação:', error);

      // Fallback para simulação
      setTimeout(() => {
        const simulatedOpponent: Player = {
          id: '2',
          name: 'Ana Silva',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
          platform: 'psn',
          platformId: 'ana_silva_psn',
          rating: 1250,
          status: 'waiting_payment'
        };
        setOpponent(simulatedOpponent);
        setFoundMatch(true);
        setCurrentPlayer(prev => ({ ...prev, status: 'waiting_payment' }));
      }, 5000);
    }
  };

  const handlePayment = (playerId: string) => {
    if (playerId === currentPlayer.id) {
      setCurrentPlayer(prev => ({ ...prev, status: 'paid' }));
    } else if (opponent) {
      setOpponent(prev => prev ? { ...prev, status: 'paid' } : null);
    }
  };

  const handleSendMessage = (text: string) => {
    const newMsg: Message = {
      id: Date.now().toString(),
      playerId: currentPlayer.id,
      text,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newMsg]);
    setNewMessage('');

    // Simulate opponent response
    if (text.toLowerCase().includes('oi') || text.toLowerCase().includes('olá')) {
      setTimeout(() => {
        const response: Message = {
          id: (Date.now() + 1).toString(),
          playerId: opponent?.id || '2',
          text: 'Oi! Pronto para jogar?',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, response]);
      }, 1500);
    }
  };

  const handleCopyPlatformId = (id: string) => {
    navigator.clipboard.writeText(id);
  };

  // Update search time
  useEffect(() => {
    let interval: number;
    if (isMatchmaking && !foundMatch) {
      interval = setInterval(() => {
        setSearchTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isMatchmaking, foundMatch]);

  // Check if both players have paid
  useEffect(() => {
    if (currentPlayer.status === 'paid' && opponent?.status === 'paid') {
      // Both players have paid, show game instructions
      const systemMsg: Message = {
        id: Date.now().toString(),
        playerId: 'system',
        text: 'Ambos os jogadores pagaram. Vocês podem adicionar um ao outro e começar a partida!',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, systemMsg]);
    }
  }, [currentPlayer.status, opponent?.status]);

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-2xl mx-auto space-y-6">
        {!isMatchmaking && (
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">FIFA 24</h1>
              <p className="text-gray-400">Encontre oponentes e jogue partidas 1v1</p>
            </div>
            <button
              onClick={() => navigate('/external-games/fifa')}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        )}

        {!isMatchmaking ? (
          <>
            {/* Match Settings */}
            <div className="bg-gray-800 rounded-xl p-6 space-y-6">
              <div className="flex items-center gap-2">
                <Settings className="w-5 h-5 text-rose-400" />
                <h2 className="text-white font-bold">Configurações da Partida</h2>
              </div>

              {/* Game Mode */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Modo de Jogo</label>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, gameMode: 'friendlies' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.gameMode === 'friendlies'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Amistosos
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, gameMode: 'fut' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.gameMode === 'fut'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Ultimate Team
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, gameMode: 'seasons' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.gameMode === 'seasons'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Temporadas
                  </button>
                </div>
              </div>

              {/* Match Duration */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Duração da Partida</label>
                <div className="grid grid-cols-5 gap-2">
                  {[4, 6, 8, 10, 12].map(duration => (
                    <button
                      key={duration}
                      onClick={() => setMatchSettings(prev => ({ ...prev, matchDuration: duration as 4 | 6 | 8 | 10 | 12 }))}
                      className={`p-3 rounded-lg text-center ${
                        matchSettings.matchDuration === duration
                          ? 'bg-rose-400 text-white'
                          : 'bg-gray-700 text-gray-300'
                      }`}
                    >
                      {duration}min
                    </button>
                  ))}
                </div>
              </div>

              {/* Camera Type */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Tipo de Câmera</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, cameraType: 'default' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.cameraType === 'default'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Padrão
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, cameraType: 'tele_broadcast' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.cameraType === 'tele_broadcast'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Tele Broadcast
                  </button>
                </div>
              </div>

              {/* Control Type */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Tipo de Controle</label>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, controlType: 'manual' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.controlType === 'manual'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Manual
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, controlType: 'semi_assisted' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.controlType === 'semi_assisted'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Semi-Assistido
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, controlType: 'automatic' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.controlType === 'automatic'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Automático
                  </button>
                </div>
              </div>

              {/* Allowed Teams */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Times Permitidos</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, allowedTeams: 'clubs_only' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.allowedTeams === 'clubs_only'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Apenas Clubes
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, allowedTeams: 'all' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.allowedTeams === 'all'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Clubes e Seleções
                  </button>
                </div>
              </div>

              {/* Overtime and Weather */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-400 text-sm mb-2">Prorrogação</label>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => setMatchSettings(prev => ({ ...prev, overtime: true }))}
                      className={`p-3 rounded-lg text-center ${
                        matchSettings.overtime
                          ? 'bg-rose-400 text-white'
                          : 'bg-gray-700 text-gray-300'
                      }`}
                    >
                      Ativada
                    </button>
                    <button
                      onClick={() => setMatchSettings(prev => ({ ...prev, overtime: false }))}
                      className={`p-3 rounded-lg text-center ${
                        !matchSettings.overtime
                          ? 'bg-rose-400 text-white'
                          : 'bg-gray-700 text-gray-300'
                      }`}
                    >
                      Desativada
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-gray-400 text-sm mb-2">Clima</label>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => setMatchSettings(prev => ({ ...prev, weather: 'clear' }))}
                      className={`p-3 rounded-lg text-center ${
                        matchSettings.weather === 'clear'
                          ? 'bg-rose-400 text-white'
                          : 'bg-gray-700 text-gray-300'
                      }`}
                    >
                      Sempre Claro
                    </button>
                    <button
                      onClick={() => setMatchSettings(prev => ({ ...prev, weather: 'random' }))}
                      className={`p-3 rounded-lg text-center ${
                        matchSettings.weather === 'random'
                          ? 'bg-rose-400 text-white'
                          : 'bg-gray-700 text-gray-300'
                      }`}
                    >
                      Aleatório
                    </button>
                  </div>
                </div>
              </div>

              {/* Tiebreaker */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Desempate</label>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, tiebreaker: 'replay' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.tiebreaker === 'replay'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Replay
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, tiebreaker: 'overtime' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.tiebreaker === 'overtime'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Prorrogação
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, tiebreaker: 'penalties' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.tiebreaker === 'penalties'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Pênaltis
                  </button>
                </div>
              </div>

              {/* Disconnect Rule */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Regra de Desconexão</label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, disconnectRule: 'winning_player' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.disconnectRule === 'winning_player'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Vitória para quem estava ganhando
                  </button>
                  <button
                    onClick={() => setMatchSettings(prev => ({ ...prev, disconnectRule: 'online_player' }))}
                    className={`p-3 rounded-lg text-center ${
                      matchSettings.disconnectRule === 'online_player'
                        ? 'bg-rose-400 text-white'
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    Vitória para quem permaneceu online
                  </button>
                </div>
              </div>

              {/* Bet Amount */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Valor da Aposta</label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">R$</span>
                  <input
                    type="number"
                    min="10"
                    step="5"
                    value={matchSettings.betAmount}
                    onChange={(e) => setMatchSettings(prev => ({ ...prev, betAmount: Number(e.target.value) }))}
                    className="w-full bg-gray-700 text-white rounded-lg py-3 pl-10 pr-3 focus:outline-none focus:ring-2 focus:ring-rose-400"
                  />
                </div>
              </div>
            </div>

            {/* Start Button */}
            <button
              onClick={handleStartMatchmaking}
              className="w-full bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold py-4 rounded-xl flex items-center justify-center gap-2"
            >
              <Users className="w-5 h-5" />
              Encontrar Oponente
            </button>
          </>
        ) : foundMatch ? (
          <div className="bg-gray-800 rounded-xl p-6 space-y-6">
            <div className="text-center">
              <Trophy className="w-12 h-12 text-rose-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-2">Oponente Encontrado!</h2>
              <p className="text-gray-400">Faça o depósito para iniciar a partida</p>
            </div>

            <div className="grid grid-cols-2 gap-8">
              {/* Current Player */}
              <div className="text-center space-y-3">
                <img
                  src={currentPlayer.avatar}
                  alt={currentPlayer.name}
                  className="w-20 h-20 rounded-full mx-auto"
                />
                <div>
                  <h3 className="text-white font-bold">{currentPlayer.name}</h3>
                  <p className="text-gray-400 text-sm">Rating: {currentPlayer.rating}</p>
                  <div className={`mt-2 text-sm ${
                    currentPlayer.status === 'paid' ? 'text-green-400' : 'text-yellow-400'
                  }`}>
                    {currentPlayer.status === 'paid' ? 'Pago' : 'Aguardando Pagamento'}
                  </div>
                </div>
                <div className="space-y-2">
                  <button
                    onClick={() => handleCopyPlatformId(currentPlayer.platformId)}
                    className="w-full bg-gray-700 text-white rounded-lg py-2 px-3 flex items-center justify-center gap-2 hover:bg-gray-600"
                  >
                    <Copy className="w-4 h-4" />
                    {currentPlayer.platformId}
                  </button>
                  {currentPlayer.status === 'waiting_payment' && (
                    <button
                      onClick={() => handlePayment(currentPlayer.id)}
                      className="w-full bg-gradient-to-r from-rose-400 to-pink-400 text-white rounded-lg py-2 px-3 flex items-center justify-center gap-2"
                    >
                      <Wallet className="w-4 h-4" />
                      Pagar R$ {matchSettings.betAmount}
                    </button>
                  )}
                </div>
              </div>

              {/* Opponent */}
              {opponent && (
                <div className="text-center space-y-3">
                  <img
                    src={opponent.avatar}
                    alt={opponent.name}
                    className="w-20 h-20 rounded-full mx-auto"
                  />
                  <div>
                    <h3 className="text-white font-bold">{opponent.name}</h3>
                    <p className="text-gray-400 text-sm">Rating: {opponent.rating}</p>
                    <div className={`mt-2 text-sm ${
                      opponent.status === 'paid' ? 'text-green-400' : 'text-yellow-400'
                    }`}>
                      {opponent.status === 'paid' ? 'Pago' : 'Aguardando Pagamento'}
                    </div>
                  </div>
                  <button
                    onClick={() => handleCopyPlatformId(opponent.platformId)}
                    className="w-full bg-gray-700 text-white rounded-lg py-2 px-3 flex items-center justify-center gap-2 hover:bg-gray-600"
                  >
                    <Copy className="w-4 h-4" />
                    {opponent.platformId}
                  </button>
                </div>
              )}
            </div>

            {/* Match Info */}
            <div className="bg-gray-700 rounded-lg p-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Modo de Jogo:</span>
                <span className="text-white">{matchSettings.gameMode === 'friendlies' ? 'Amistosos' : matchSettings.gameMode === 'fut' ? 'Ultimate Team' : 'Temporadas'}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Duração:</span>
                <span className="text-white">{matchSettings.matchDuration} minutos</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Câmera:</span>
                <span className="text-white">
                  {matchSettings.cameraType === 'default' ? 'Padrão' : 'Tele Broadcast'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Controle:</span>
                <span className="text-white">
                  {matchSettings.controlType === 'manual' ? 'Manual' :
                   matchSettings.controlType === 'semi_assisted' ? 'Semi-Assistido' :
                   'Automático'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Times:</span>
                <span className="text-white">
                  {matchSettings.allowedTeams === 'clubs_only' ? 'Apenas Clubes' : 'Clubes e Seleções'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Prorrogação:</span>
                <span className="text-white">{matchSettings.overtime ? 'Ativada' : 'Desativada'}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Clima:</span>
                <span className="text-white">
                  {matchSettings.weather === 'clear' ? 'Sempre Claro' : 'Aleatório'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Desempate:</span>
                <span className="text-white">
                  {matchSettings.tiebreaker === 'replay' ? 'Replay' :
                   matchSettings.tiebreaker === 'overtime' ? 'Prorrogação' :
                   'Pênaltis'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Valor da Aposta:</span>
                <span className="text-rose-400 font-bold">R$ {matchSettings.betAmount}</span>
              </div>
            </div>

            {/* Chat Section */}
            {showChat ? (
              <div className="space-y-4">
                <div className="h-60 bg-gray-700 rounded-lg p-4 overflow-y-auto space-y-3">
                  {messages.map((msg) => (
                    <div
                      key={msg.id}
                      className={`flex ${msg.playerId === currentPlayer.id ? 'justify-end' : 'justify-start'}`}
                    >
                      {msg.playerId === 'system' ? (
                        <div className="bg-gray-600/50 text-gray-300 text-sm py-2 px-4 rounded-lg max-w-[80%] flex items-center gap-2">
                          <AlertCircle className="w-4 h-4 text-yellow-400" />
                          {msg.text}
                        </div>
                      ) : (
                        <div className={`${
                          msg.playerId === currentPlayer.id
                            ? 'bg-rose-400 text-white'
                            : 'bg-gray-600 text-gray-200'
                        } py-2 px-4 rounded-lg max-w-[80%]`}>
                          {msg.text}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Digite sua mensagem..."
                    className="flex-1 bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-rose-400"
                  />
                  <button
                    onClick={() => handleSendMessage(newMessage)}
                    disabled={!newMessage.trim()}
                    className="bg-rose-400 text-white px-4 rounded-lg disabled:opacity-50"
                  >
                    <MessageCircle className="w-5 h-5" />
                  </button>
                </div>
                <button
                  onClick={() => setShowChat(false)}
                  className="w-full bg-gray-700 text-gray-300 py-2 rounded-lg hover:bg-gray-600"
                >
                  Fechar Chat
                </button>
              </div>
            ) : (
              <button
                onClick={() => setShowChat(true)}
                className="w-full bg-gray-700 text-gray-300 py-2 rounded-lg hover:bg-gray-600 flex items-center justify-center gap-2"
              >
                <MessageCircle className="w-5 h-5" />
                Abrir Chat
              </button>
            )}
          </div>
        ) : (
          <div className="bg-gray-800 rounded-xl p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-rose-400 border-t-transparent mx-auto mb-4"></div>
            <h2 className="text-2xl font-bold text-white mb-2">Procurando Oponente...</h2>
            <p className="text-gray-400">Tempo de busca: {searchTime}s</p>
            <button
              onClick={() => {
                setIsMatchmaking(false);
                setSearchTime(0);
                setCurrentPlayer(prev => ({ ...prev, status: 'ready' }));
              }}
              className="mt-4 text-rose-400 hover:text-rose-300"
            >
              Cancelar
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
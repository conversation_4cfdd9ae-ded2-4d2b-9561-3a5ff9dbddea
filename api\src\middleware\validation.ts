import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { ApiResponse, ValidationError } from '../types/api';
import { logger } from '../utils/logger';

// Validation middleware factory
export const validate = (schema: Joi.ObjectSchema, target: 'body' | 'params' | 'query' = 'body') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const dataToValidate = req[target];

    const { error, value } = schema.validate(dataToValidate, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const validationErrors: ValidationError[] = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      logger.warn('Validation error:', {
        errors: validationErrors,
        data: dataToValidate,
        receivedFields: Object.keys(dataToValidate || {}),
        errorDetails: error.details.map(d => ({
          field: d.path.join('.'),
          message: d.message,
          value: d.context?.value,
          type: d.type
        }))
      });

      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        data: validationErrors
      };

      res.status(400).json(response);
      return;
    }

    // Replace the original data with validated and sanitized data
    req[target] = value;
    next();
  };
};

// Common validation schemas
export const schemas = {
  // User schemas
  userRegistration: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    username: Joi.string().alphanum().min(3).max(30).required().messages({
      'string.alphanum': 'Username must contain only letters and numbers',
      'string.min': 'Username must be at least 3 characters long',
      'string.max': 'Username must not exceed 30 characters',
      'any.required': 'Username is required'
    }),
    display_name: Joi.string().min(2).max(100).required().messages({
      'string.min': 'Display name must be at least 2 characters long',
      'string.max': 'Display name must not exceed 100 characters',
      'any.required': 'Display name is required'
    }),
    password: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])')).required().messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character',
      'any.required': 'Password is required'
    }),
    birth_date: Joi.date().iso().max('now').optional(),
    country: Joi.string().length(2).optional(),
    timezone: Joi.string().optional(),
    language: Joi.string().length(5).optional(),
    referral_code: Joi.string().min(3).max(50).optional().allow(null)
  }),

  userLogin: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  userProfileUpdate: Joi.object({
    display_name: Joi.string().min(2).max(100).optional(),
    bio: Joi.string().max(500).optional(),
    avatar_url: Joi.string().uri().optional(),
    favorite_games: Joi.array().items(Joi.string()).max(10).optional(),
    gaming_experience: Joi.string().valid('beginner', 'intermediate', 'advanced', 'professional').optional(),
    preferred_game_modes: Joi.array().items(Joi.string()).max(10).optional(),
    social_links: Joi.object().pattern(Joi.string(), Joi.string().uri()).optional(),
    privacy_settings: Joi.object().optional(),
    notification_preferences: Joi.object().optional()
  }),

  // Transaction schemas
  transaction: Joi.object({
    type: Joi.string().valid('deposit', 'withdrawal', 'bet', 'win', 'commission', 'refund').required(),
    amount: Joi.number().positive().precision(2).required().messages({
      'number.positive': 'Amount must be positive',
      'any.required': 'Amount is required'
    }),
    payment_method: Joi.string().valid('pix', 'credit_card', 'bank_transfer').optional(),
    description: Joi.string().max(255).optional(),
    metadata: Joi.object().optional()
  }),

  deposit: Joi.object({
    amount: Joi.number().positive().min(10).max(5000).precision(2).required(),
    payment_method: Joi.string().valid('pix', 'credit_card').required()
  }),

  withdrawal: Joi.object({
    amount: Joi.number().positive().min(10).max(10000).precision(2).required(),
    payment_method: Joi.string().valid('pix').required(),
    pix_key: Joi.string().required().when('payment_method', {
      is: 'pix',
      then: Joi.required(),
      otherwise: Joi.optional()
    })
  }),

  // Game room schemas
  gameRoomCreation: Joi.object({
    game_id: Joi.string().uuid().required(),
    name: Joi.string().min(3).max(100).required(),
    type: Joi.string().valid('1v1', 'tournament', 'practice').required(),
    entry_fee: Joi.number().min(0).precision(2).required(),
    max_players: Joi.number().integer().min(2).max(16).required(),
    is_public: Joi.boolean().default(true),
    password: Joi.string().min(4).max(20).optional(),
    game_settings: Joi.object().optional(),
    scheduled_start_time: Joi.date().iso().min('now').optional()
  }),

  // Tournament schemas
  tournamentCreation: Joi.object({
    game_id: Joi.string().uuid().required(),
    title: Joi.string().min(5).max(200).required(),
    description: Joi.string().max(1000).optional(),
    rules: Joi.string().max(5000).optional(),
    format: Joi.string().valid('elimination', 'round_robin', 'swiss', 'points_based').required(),
    is_public: Joi.boolean().default(true),
    entry_fee: Joi.number().min(0).precision(2).required(),
    max_participants: Joi.number().integer().min(4).max(1024).required(),
    prize_distribution: Joi.object().pattern(Joi.string(), Joi.number().min(0).max(100)).required(),
    registration_start_date: Joi.date().iso().optional(),
    registration_end_date: Joi.date().iso().min('now').required(),
    start_date: Joi.date().iso().min(Joi.ref('registration_end_date')).required(),
    end_date: Joi.date().iso().min(Joi.ref('start_date')).optional(),
    tournament_settings: Joi.object().optional()
  }),

  // Club schemas
  clubCreation: Joi.object({
    name: Joi.string().min(3).max(100).required(),
    description: Joi.string().max(1000).optional(),
    rules: Joi.string().max(5000).optional(),
    game_id: Joi.string().uuid().optional(),
    privacy_type: Joi.string().valid('public', 'private', 'invite_only').required(),
    max_members: Joi.number().integer().min(5).max(1000).default(100),
    region: Joi.string().max(100).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional()
  }),

  // Message schemas
  message: Joi.object({
    message: Joi.string().min(1).max(1000).required(),
    message_type: Joi.string().valid('text', 'image', 'system').default('text'),
    attachment_url: Joi.string().uri().optional(),
    reply_to_message_id: Joi.string().uuid().optional()
  }),

  // Friend request schemas
  friendRequest: Joi.object({
    addressee_user_id: Joi.string().uuid().required()
  }),

  // Challenge schemas
  challenge: Joi.object({
    challenged_user_id: Joi.string().uuid().required(),
    game_id: Joi.string().uuid().required(),
    bet_amount: Joi.number().min(0).precision(2).required(),
    game_settings: Joi.object().optional(),
    message: Joi.string().max(255).optional()
  }),

  // Match result schemas
  matchResult: Joi.object({
    match_id: Joi.string().uuid().required(),
    winner_user_id: Joi.string().uuid().optional(),
    result_data: Joi.object().required(),
    result_image_url: Joi.string().uri().optional(),
    participants_scores: Joi.array().items(
      Joi.object({
        user_id: Joi.string().uuid().required(),
        score: Joi.number().integer().min(0).required(),
        placement: Joi.number().integer().min(1).required(),
        stats: Joi.object().optional()
      })
    ).required()
  }),

  // Pagination schemas
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  }),

  // Search schemas
  search: Joi.object({
    query: Joi.string().min(1).max(100).optional(),
    category: Joi.string().optional(),
    game_id: Joi.string().uuid().optional(),
    status: Joi.string().optional(),
    date_from: Joi.date().iso().optional(),
    date_to: Joi.date().iso().min(Joi.ref('date_from')).optional(),
    min_amount: Joi.number().min(0).optional(),
    max_amount: Joi.number().min(Joi.ref('min_amount')).optional(),
    tags: Joi.array().items(Joi.string()).optional()
  }),

  // UUID parameter validation
  uuidParam: Joi.object({
    id: Joi.string().uuid().required()
  }),

  // Room ID parameter validation
  roomIdParam: Joi.object({
    roomId: Joi.string().uuid().required()
  }),

  // Report schemas
  report: Joi.object({
    reported_user_id: Joi.string().uuid().required(),
    match_id: Joi.string().uuid().optional(),
    reason: Joi.string().min(5).max(100).required(),
    description: Joi.string().max(1000).optional(),
    evidence_urls: Joi.array().items(Joi.string().uri()).max(5).optional()
  }),

  // Stream schemas
  stream: Joi.object({
    title: Joi.string().min(5).max(200).required(),
    description: Joi.string().max(1000).optional(),
    game_id: Joi.string().uuid().optional(),
    stream_url: Joi.string().uri().required(),
    thumbnail_url: Joi.string().uri().optional(),
    platform: Joi.string().valid('twitch', 'youtube', 'facebook', 'custom').required(),
    external_stream_id: Joi.string().optional()
  }),

  // System settings schemas
  systemSettings: Joi.object({
    key: Joi.string().min(1).max(100).required(),
    value: Joi.any().required(),
    description: Joi.string().max(255).optional(),
    is_public: Joi.boolean().default(false)
  }),

  // Notification settings schemas
  notificationSettings: Joi.object({
    email: Joi.boolean().default(true),
    push: Joi.boolean().default(true),
    friend_requests: Joi.boolean().default(true),
    match_updates: Joi.boolean().default(true),
    tournament_updates: Joi.boolean().default(true)
  })
};

// Validate file upload
export const validateFileUpload = (
  allowedMimeTypes: string[] = [],
  maxSize: number = 10 * 1024 * 1024 // 10MB default
) => {
  return (req: any, res: Response, next: NextFunction): void => {
    if (!req.file) {
      const response: ApiResponse = {
        success: false,
        error: 'No file uploaded'
      };
      res.status(400).json(response);
      return;
    }

    // Check file size
    if (req.file.size > maxSize) {
      const response: ApiResponse = {
        success: false,
        error: `File size exceeds limit of ${maxSize / (1024 * 1024)}MB`
      };
      res.status(400).json(response);
      return;
    }

    // Check MIME type
    if (allowedMimeTypes.length > 0 && !allowedMimeTypes.includes(req.file.mimetype)) {
      const response: ApiResponse = {
        success: false,
        error: `File type not allowed. Allowed types: ${allowedMimeTypes.join(', ')}`
      };
      res.status(400).json(response);
      return;
    }

    next();
  };
};

// Validate array of UUIDs
export const validateUuidArray = (field: string) => {
  return Joi.array().items(Joi.string().uuid()).min(1).required().messages({
    'array.min': `${field} must contain at least one valid UUID`,
    'any.required': `${field} is required`
  });
};

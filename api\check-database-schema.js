const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabaseSchema() {
  try {
    console.log('🔍 Checking database schema...');
    
    // Check if users table exists and get its structure
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1);
    
    if (usersError) {
      console.error('❌ Error accessing users table:', usersError);
      return;
    }
    
    console.log('✅ Users table accessible');
    
    if (users && users.length > 0) {
      console.log('\n📋 Available columns in users table:');
      console.log(Object.keys(users[0]));
    }
    
    // Try to get user with all possible fields
    console.log('\n🔍 Checking <NAME_EMAIL>...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();
    
    if (userError) {
      console.error('❌ User not found:', userError);
    } else {
      console.log('✅ User found:');
      console.log('- ID:', user.id);
      console.log('- Email:', user.email);
      console.log('- Username:', user.username);
      console.log('- Display Name:', user.display_name);
      console.log('- Status:', user.status);
      console.log('- Password Hash:', user.password_hash ? 'EXISTS' : 'NULL');
      console.log('- Created At:', user.created_at);
    }
    
    // Try to manually add password_hash column using raw SQL
    console.log('\n🔧 Attempting to add password_hash column...');
    
    // First, let's try to select from information_schema to check if column exists
    const { data: columnInfo, error: columnError } = await supabase
      .rpc('exec_sql', {
        sql: `SELECT column_name FROM information_schema.columns 
              WHERE table_name = 'users' AND column_name = 'password_hash';`
      });
    
    if (columnError) {
      console.log('⚠️ Cannot check column info via RPC:', columnError.message);
    } else {
      console.log('Column check result:', columnInfo);
    }
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
  }
}

checkDatabaseSchema();

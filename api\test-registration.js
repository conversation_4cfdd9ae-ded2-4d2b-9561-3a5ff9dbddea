const axios = require('axios');

async function testRegistration() {
  try {
    console.log('🧪 Testing user registration...');
    
    const userData = {
      email: '<EMAIL>',
      username: 'testuser',
      display_name: 'Test User',
      password: 'TestPassword123#',
      birth_date: '1990-01-01',
      country: 'BR',
      timezone: 'America/Sao_Paulo',
      language: 'pt-BR'
    };
    
    console.log('📝 Registering user:', userData.email);
    
    const response = await axios.post('http://localhost:3002/api/auth/register', userData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Registration successful:');
    console.log('- Status:', response.status);
    console.log('- User ID:', response.data.data?.user?.id);
    console.log('- Username:', response.data.data?.user?.username);
    console.log('- Email:', response.data.data?.user?.email);
    
    // Now test login
    console.log('\n🔐 Testing login...');
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      email: userData.email,
      password: userData.password
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Login successful:');
    console.log('- Status:', loginResponse.status);
    console.log('- Access Token:', loginResponse.data.data?.tokens?.access_token ? 'EXISTS' : 'MISSING');
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testRegistration();

import React, { useState, useEffect } from 'react';
import { ExternalLink, Globe, Shield, Users, Trophy, Gamepad2, Loader2, Search, Rocket, Radio } from 'lucide-react';
import { useNavigate, Link } from 'react-router-dom';
import { realGameService, Game } from '../services/realGameService';

interface ExternalGame {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  players?: number;
  minBet?: number;
  platform: 'steam' | 'xbox' | 'playstation';
  status: 'verified' | 'warning' | 'danger';
  appId?: string;
  currentPlayers?: number;
  genre: string;
  rating: number;
  releaseDate: string;
  publisher: string;
  features: string[];
}

interface PlatformConnection {
  platform: 'steam' | 'xbox' | 'playstation';
  connected: boolean;
  username?: string;
  avatar?: string;
}

export default function ExternalGamesPage() {
  const navigate = useNavigate();
  const [connections, setConnections] = useState<PlatformConnection[]>([
    { platform: 'steam', connected: false },
    { platform: 'xbox', connected: false },
    { platform: 'playstation', connected: false }
  ]);
  const [loading, setLoading] = useState(false);
  const [selectedGame, setSelectedGame] = useState<ExternalGame | null>(null);

  // Estados para dados da API
  const [games, setGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carrega jogos externos da API
  useEffect(() => {
    const loadExternalGames = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando jogos externos...');

        const data = await realGameService.getExternalGames();
        setGames(data);

        console.log(`✅ ${data.length} jogos externos carregados`);
      } catch (err) {
        console.error('❌ Erro ao carregar jogos externos:', err);
        setError('Erro ao carregar jogos. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadExternalGames();
  }, []);

  // Dados mock como fallback (serão removidos quando a API estiver funcionando)
  const mockGames: ExternalGame[] = [
    {
      id: '1',
      title: 'Counter-Strike 2',
      description: 'O FPS tático mais popular do mundo agora com gráficos e mecânicas aprimoradas',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      players: 850000,
      minBet: 10,
      platform: 'steam',
      status: 'verified',
      appId: '730',
      currentPlayers: 850000,
      genre: 'FPS Tático',
      rating: 4.8,
      releaseDate: '2023',
      publisher: 'Valve Corporation',
      features: ['Competitivo', 'Times', 'Ranqueado', 'Customização']
    },
    {
      id: '2',
      title: 'FIFA 24',
      description: 'A mais recente edição do simulador de futebol mais popular do mundo',
      imageUrl: 'https://images.unsplash.com/photo-1591293835940-934a7c4f2d9b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      players: 25000,
      minBet: 20,
      platform: 'playstation',
      status: 'verified',
      genre: 'Esporte',
      rating: 4.7,
      releaseDate: '2023',
      publisher: 'EA Sports',
      features: ['Times', 'Competitivo', 'Ultimate Team', 'Carreira']
    },
    {
      id: '3',
      title: 'Mobile Legends: Bang Bang',
      description: 'Arena de batalha móvel com heróis únicos e batalhas intensas',
      imageUrl: 'https://images.unsplash.com/photo-1579373903781-fd5c0c30c4cd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      players: 15000,
      minBet: 15,
      platform: 'mobile',
      status: 'coming_soon',
      genre: 'MOBA',
      rating: 4.5,
      releaseDate: '2023',
      publisher: 'Moonton',
      features: ['Heróis', 'Competitivo', '5v5', 'Ranqueado']
    },
    {
      id: '4',
      title: 'Call of Duty: Mobile',
      description: 'A experiência clássica de Call of Duty otimizada para dispositivos móveis',
      imageUrl: 'https://images.unsplash.com/photo-1614027164847-1b28cfe1df60?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      players: 42000,
      platform: 'mobile',
      status: 'verified',
      genre: 'FPS',
      rating: 4.6,
      releaseDate: '2019',
      publisher: 'Activision',
      features: ['Battle Royale', 'Multiplayer', 'Clãs', 'Eventos']
    },
    {
      id: '5',
      title: 'League of Legends',
      description: 'O MOBA mais popular do mundo com mais de 140 campeões',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      platform: 'pc',
      status: 'coming_soon',
      genre: 'MOBA',
      rating: 4.8,
      releaseDate: '2009',
      publisher: 'Riot Games',
      features: ['Campeões', '5v5', 'Ranqueado', 'eSports']
    },
    {
      id: '6',
      title: 'Fortnite',
      description: 'Battle Royale com construção e eventos únicos',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      platform: 'pc',
      status: 'coming_soon',
      genre: 'Battle Royale',
      rating: 4.7,
      releaseDate: '2017',
      publisher: 'Epic Games',
      features: ['Construção', 'Battle Pass', 'Crossplay', 'Eventos']
    },
    {
      id: '7',
      title: 'Valorant',
      description: 'FPS tático com agentes únicos e habilidades especiais',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      platform: 'pc',
      status: 'coming_soon',
      genre: 'FPS Tático',
      rating: 4.8,
      releaseDate: '2020',
      publisher: 'Riot Games',
      features: ['Agentes', 'Habilidades', 'Competitivo', 'eSports']
    },
    {
      id: '8',
      title: 'PUBG: Battlegrounds',
      description: 'Battle Royale realista com combates intensos',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      platform: 'pc',
      status: 'coming_soon',
      genre: 'Battle Royale',
      rating: 4.6,
      releaseDate: '2017',
      publisher: 'KRAFTON',
      features: ['Realista', 'Squad', 'Veículos', 'Customização']
    },
    {
      id: '9',
      title: 'Roblox',
      description: 'Plataforma de jogos com infinitas possibilidades',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      platform: 'pc',
      status: 'coming_soon',
      genre: 'Plataforma',
      rating: 4.5,
      releaseDate: '2006',
      publisher: 'Roblox Corporation',
      features: ['Criação', 'Multiplayer', 'Social', 'Cross-platform']
    },
    {
      id: '10',
      title: 'Call of Duty: Warzone',
      description: 'Battle Royale gratuito no universo de Call of Duty',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      platform: 'pc',
      status: 'coming_soon',
      genre: 'Battle Royale',
      rating: 4.7,
      releaseDate: '2020',
      publisher: 'Activision',
      features: ['Free-to-play', 'Cross-play', 'Loadouts', 'Gulag']
    },
    {
      id: '11',
      title: 'Apex Legends',
      description: 'Battle Royale com heróis e movimento fluido',
      imageUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      platform: 'pc',
      status: 'coming_soon',
      genre: 'Battle Royale',
      rating: 4.8,
      releaseDate: '2019',
      publisher: 'Electronic Arts',
      features: ['Legends', 'Habilidades', 'Squad', 'Ranked']
    }
  ];

  const handleConnect = async (platform: 'steam' | 'xbox' | 'playstation') => {
    setLoading(true);
    try {
      // Simulando conexão com a API
      await new Promise(resolve => setTimeout(resolve, 1500));

      setConnections(prev => prev.map(conn =>
        conn.platform === platform
          ? {
              ...conn,
              connected: true,
              username: `Player_${Math.floor(Math.random() * 1000)}`,
              avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
            }
          : conn
      ));
    } catch (error) {
      console.error('Erro ao conectar:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGameClick = (game: ExternalGame) => {
    if (game.status === 'coming_soon') {
      return; // Do nothing for unreleased games
    }

    switch (game.title) {
      case 'FIFA 24':
        navigate('/external-games/fifa');
        break;
      case 'Mobile Legends: Bang Bang':
        navigate('/external-games/mobile-legends');
        break;
      case 'Call of Duty: Mobile':
        navigate('/external-games/cod-mobile');
        break;
      default:
        navigate(`/external-games/${game.id}`);
    }
  };

  const handleCreateRoom = (game: ExternalGame) => {
    setSelectedGame(game);
  };

  const getPlatformIcon = (platform: 'steam' | 'xbox' | 'playstation') => {
    return Gamepad2;
  };

  const getPlatformName = (platform: 'steam' | 'xbox' | 'playstation') => {
    switch (platform) {
      case 'steam':
        return 'Steam';
      case 'xbox':
        return 'Xbox';
      case 'playstation':
        return 'PlayStation';
    }
  };

  // Função para converter dados da API para o formato da interface
  const convertApiGameToExternalGame = (apiGame: Game): ExternalGame => ({
    id: apiGame.id,
    title: apiGame.name,
    description: apiGame.description || 'Descrição não disponível',
    imageUrl: apiGame.image_url || 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    players: apiGame.player_count || Math.floor(Math.random() * 10000) + 1000, // Placeholder se não tiver dados
    minBet: apiGame.default_entry_fee || 10,
    platform: apiGame.supported_platforms?.[0] === 'mobile' ? 'xbox' : 'steam', // Mapear plataforma
    status: apiGame.is_active ? 'verified' : 'coming_soon',
    genre: apiGame.game_modes?.[0] || 'Geral',
    rating: 4.5, // Padrão, pode ser ajustado baseado nos dados da API
    releaseDate: new Date(apiGame.created_at).getFullYear().toString(),
    publisher: 'Publisher', // Padrão, pode ser ajustado baseado nos dados da API
    features: apiGame.game_modes || ['Multiplayer']
  });

  // Usa dados da API se disponíveis, senão usa mock como fallback
  const displayGames = games.length > 0
    ? games.map(convertApiGameToExternalGame)
    : mockGames;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white mb-2">Jogos</h1>
        <p className="text-gray-400">Jogue seus jogos favoritos e participe de torneios</p>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-center">
          <p className="text-red-400 mb-2">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Loader2 className="w-8 h-8 text-indigo-400 mx-auto mb-4 animate-spin" />
          <h3 className="text-white font-bold text-lg mb-2">Carregando jogos...</h3>
          <p className="text-gray-400">Aguarde enquanto buscamos os jogos disponíveis.</p>
        </div>
      ) : (
        <>


      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-4">
        <Link
          to="/internal-games"
          className="bg-gradient-to-br from-rose-400 via-salmon-400 to-pink-400 rounded-2xl p-8 text-center hover:scale-[1.02] hover:shadow-xl transition-all duration-300 shadow-lg"
        >
          <div className="bg-white/20 p-4 rounded-xl inline-block mb-4">
            <Rocket className="w-12 h-12 text-white" />
          </div>
          <h3 className="text-black font-bold mb-1">Jogos Internos</h3>
          <p className="text-black/80 text-sm">
            Jogue nossos jogos exclusivos e ganhe prêmios
          </p>
        </Link>
        <Link
          to="/streams"
          className="bg-gradient-to-br from-fuchsia-500 via-magenta-500 to-rose-500 rounded-2xl p-8 text-center hover:scale-[1.02] hover:shadow-xl transition-all duration-300 shadow-lg"
        >
          <div className="bg-white/20 p-4 rounded-xl inline-block mb-4">
            <Radio className="w-12 h-12 text-white" />
          </div>
          <h3 className="text-white font-bold mb-1">Streams</h3>
          <p className="text-white/80 text-sm">
            Assista aos melhores jogadores e torneios
          </p>
        </Link>
      </div>

      {/* Search and Filter Header */}
      <div className="flex items-center gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Buscar jogos..."
            className="w-full bg-gray-800/50 text-white rounded-xl py-3 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-rose-400 backdrop-blur-sm border border-white/10"
          />
        </div>

        <div className="flex gap-2">
          <button className="bg-gray-800/50 text-white px-4 py-2 rounded-xl hover:bg-gray-700/50 backdrop-blur-sm border border-white/10">
            Gênero
          </button>
          <button className="bg-gray-800/50 text-white px-4 py-2 rounded-xl hover:bg-gray-700/50 backdrop-blur-sm border border-white/10">
            Plataforma
          </button>
        </div>
      </div>

      {/* Featured Game */}
      {displayGames.length > 0 && (
        <div
          className="relative h-[400px] rounded-2xl overflow-hidden cursor-pointer group shadow-xl"
          onClick={() => navigate(`/external-games/${displayGames[0].id}`)}
        >
          <img
            src={displayGames[0].imageUrl}
            alt={displayGames[0].title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-transparent backdrop-blur-[2px] group-hover:backdrop-blur-none transition-all duration-300">
            <div className="absolute bottom-0 left-0 right-0 p-8">
              <h1 className="text-4xl font-bold text-white mb-2">{displayGames[0].title}</h1>
              <p className="text-gray-200 text-lg mb-4 max-w-2xl">{displayGames[0].description}</p>
              <div className="flex items-center gap-4">
                <button className="bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold px-8 py-3 rounded-xl flex items-center gap-2 hover:opacity-90">
                  <Gamepad2 className="w-5 h-5" />
                  Jogar Agora
                </button>
                <div className="flex items-center gap-4 text-white">
                  <span className="flex items-center gap-1">
                    <Users className="w-5 h-5" />
                    {displayGames[0].players?.toLocaleString() || '0'} online
                  </span>
                  <span className="flex items-center gap-1">
                    <Trophy className="w-5 h-5" />
                    {displayGames[0].rating} / 5
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Games Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
        {displayGames.slice(1).map((game) => (
          <div
            key={game.id}
            className={`bg-gray-800/50 backdrop-blur-sm rounded-2xl overflow-hidden ${
              game.status === 'coming_soon' ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer group hover:scale-[1.02] hover:shadow-xl'
            } transition-all duration-300 border border-white/10`}
            onClick={() => navigate(`/external-games/${game.id}`)}
          >
            <div className="relative aspect-video">
              <img
                src={game.imageUrl}
                alt={game.title}
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900/90 via-gray-900/50 to-transparent backdrop-blur-[2px] group-hover:backdrop-blur-none transition-all duration-300" />
              <div className="absolute bottom-0 left-0 right-0 p-4">
                <h3 className="text-xl font-bold text-white mb-1">{game.title}</h3>
                <div className="flex items-center gap-3 text-sm text-gray-300">
                  <span>{game.genre}</span>
                  <span>•</span>
                  {game.status === 'coming_soon' ? (
                    <span className="text-rose-400">Em Breve</span>
                  ) : (
                    <span>{game.rating} / 5</span>
                  )}
                </div>
              </div>
            </div>
            <div className="p-4 space-y-3">
              <p className="text-gray-400 text-sm line-clamp-2">{game.description}</p>
              <div className="flex flex-wrap gap-2">
                {game.features.slice(0, 3).map((feature) => (
                  <span
                    key={feature}
                    className="text-xs bg-gray-700/50 text-gray-300 px-3 py-1.5 rounded-full border border-white/5"
                  >
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {!isLoading && displayGames.length === 0 && (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Gamepad2 className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-white font-bold text-lg mb-2">Nenhum jogo encontrado</h3>
          <p className="text-gray-400">Não conseguimos carregar os jogos no momento. Tente novamente mais tarde.</p>
        </div>
      )}
        </>
      )}

      {/* Platform Connections */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
        <h2 className="text-white text-lg font-bold mb-2">Suas Plataformas</h2>
        <p className="text-gray-400 mb-6">É importante se conectar. Alguns jogos exigem conexão para rodar.</p>
        <div className="grid grid-cols-3 gap-4">
          {connections.map(({ platform, connected, username, avatar }) => {
            const PlatformIcon = getPlatformIcon(platform);
            return (
              <div key={platform} className="bg-gray-700/50 rounded-xl p-6 border border-white/5 relative group">
                <div className="flex flex-col items-center text-center">
                  <PlatformIcon className={`w-8 h-8 mb-2 ${connected ? 'text-green-400' : 'text-gray-400'}`} />
                  <h3 className="text-white font-semibold mb-2">{getPlatformName(platform)}</h3>

                  {connected ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 justify-center">
                        <img src={avatar} alt={username} className="w-6 h-6 rounded-full" />
                        <span className="text-sm text-gray-300">{username}</span>
                      </div>
                      <button
                        onClick={() => handleConnect(platform)}
                        className="text-red-400 text-sm hover:text-red-300"
                      >
                        Desconectar
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => handleConnect(platform)}
                      disabled={loading}
                      className="bg-gradient-to-r from-rose-400 to-pink-400 text-white text-sm font-semibold px-4 py-2 rounded-lg hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg transition-all duration-300"
                    >
                      {loading ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        'Conectar'
                      )}
                    </button>
                  )}
                </div>
                {!connected && (
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-rose-400 rounded-full opacity-75 animate-ping" />
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
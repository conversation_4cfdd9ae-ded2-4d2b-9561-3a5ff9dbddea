import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import GameCard from '../components/GameCard';
import { Rocket, MousePointer, Grid, Type, Target, Trophy, Timer, Loader2 } from 'lucide-react';
import { realGameService, Game as ApiGame } from '../services/realGameService';

interface Game {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  rating: number;
  players?: number;
  type: 'realtime' | 'continuous';
  icon: typeof Rocket;
  prizePool?: number;
  entryFee?: number;
}

export default function InternalGamesPage() {
  const navigate = useNavigate();

  // Estados para dados da API
  const [apiGames, setApiGames] = useState<ApiGame[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carrega jogos internos da API
  useEffect(() => {
    const loadInternalGames = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Carregando jogos internos...');

        const gamesData = await realGameService.getInternalGames();
        setApiGames(gamesData);

        console.log(`✅ ${gamesData.length} jogos internos carregados`);

      } catch (err) {
        console.error('❌ Erro ao carregar jogos internos:', err);
        setError('Erro ao carregar jogos. Usando dados offline.');
      } finally {
        setIsLoading(false);
      }
    };

    loadInternalGames();
  }, []);

  const games: Game[] = [
    {
      id: 'flaprocket',
      title: 'FLAPROCKET',
      description: 'Controle seu foguete e desvie dos obstáculos',
      imageUrl: 'https://images.unsplash.com/photo-1516849841032-87cbac4d88f7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
      rating: 4.8,
      players: 768,
      type: 'continuous',
      prizePool: 2450,
      entryFee: 1,
      icon: Rocket
    },
    {
      id: 'reaction',
      title: 'REAÇÃO RÁPIDA',
      description: 'Teste seus reflexos ao máximo',
      imageUrl: 'https://images.unsplash.com/photo-1550745165-9bc0b252726f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      rating: 4.5,
      type: 'realtime',
      icon: MousePointer
    },
    {
      id: 'colormatch',
      title: 'COLOR MATCH',
      description: 'Encontre os pares de cores antes que o tempo acabe',
      imageUrl: 'https://images.unsplash.com/photo-1513542789411-b6a5d4f31634?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      rating: 4.7,
      players: 432,
      type: 'realtime',
      icon: Grid
    },
    {
      id: 'wordscramble',
      title: 'WORD SCRAMBLE',
      description: 'Descubra as palavras embaralhadas',
      imageUrl: 'https://images.unsplash.com/photo-1544396821-4dd40b938ad3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      rating: 4.6,
      players: 523,
      type: 'realtime',
      icon: Type
    },
    {
      id: 'stickmanarcher',
      title: 'STICKMAN ARCHER',
      description: 'Teste sua precisão com arco e flecha',
      imageUrl: 'https://images.unsplash.com/photo-1511355624756-6a4d32a79e8c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      rating: 4.9,
      players: 645,
      type: 'continuous',
      icon: Target
    }
  ];

  // Converte dados da API para o formato da interface
  const convertApiGameToGame = (apiGame: ApiGame): Game => ({
    id: apiGame.id,
    title: apiGame.name,
    description: apiGame.description || 'Jogo interno da plataforma',
    icon: getGameIcon(apiGame.name),
    color: getGameColor(apiGame.name),
    difficulty: apiGame.difficulty_level || 'medium',
    players: apiGame.player_count || 0,
    rating: 4.5, // Padrão
    category: 'skill',
    imageUrl: apiGame.image_url || 'https://images.unsplash.com/photo-1511355624756-6a4d32a79e8c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    type: apiGame.type === 'competitive' ? 'realtime' : 'continuous'
  });

  // Função para obter ícone baseado no nome do jogo
  const getGameIcon = (gameName: string) => {
    if (gameName.toLowerCase().includes('rocket')) return Rocket;
    if (gameName.toLowerCase().includes('click')) return MousePointer;
    if (gameName.toLowerCase().includes('memory')) return Grid;
    if (gameName.toLowerCase().includes('type')) return Type;
    if (gameName.toLowerCase().includes('aim')) return Target;
    return Trophy;
  };

  // Função para obter cor baseada no nome do jogo
  const getGameColor = (gameName: string) => {
    const colors = ['from-blue-400 to-purple-500', 'from-green-400 to-blue-500', 'from-purple-400 to-pink-500', 'from-yellow-400 to-orange-500', 'from-red-400 to-pink-500'];
    return colors[gameName.length % colors.length];
  };

  // Usa dados da API se disponíveis, senão usa dados mock
  const displayGames = apiGames.length > 0
    ? apiGames.map(convertApiGameToGame)
    : games;

  const handleGameClick = (game: Game) => {
    // Navegar para a página do jogo
    navigate(`/games/${game.id}`);
  };

  const continuousGames = displayGames.filter(game => game.type === 'continuous');
  const realtimeGames = displayGames.filter(game => game.type === 'realtime');
  const featuredGame = continuousGames[0] || displayGames[0];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white mb-2">Jogos Internos</h1>
        <p className="text-gray-400">Jogue nossos jogos exclusivos e ganhe prêmios</p>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-xl p-4 text-center">
          <p className="text-yellow-400 text-sm">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Loader2 className="w-8 h-8 text-rose-400 mx-auto mb-4 animate-spin" />
          <h3 className="text-white font-bold text-lg mb-2">Carregando jogos...</h3>
          <p className="text-gray-400">Aguarde enquanto buscamos os jogos disponíveis.</p>
        </div>
      ) : featuredGame ? (
        <>


      {/* Featured Game */}
      <div className="relative h-[300px] rounded-xl overflow-hidden">
        <img
          src={featuredGame.imageUrl}
          alt={featuredGame.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent">
          <div className="absolute bottom-0 left-0 right-0 p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-white mb-2">{featuredGame.title}</h1>
                <p className="text-gray-300 mb-4">{featuredGame.description}</p>
                <button
                  onClick={() => handleGameClick(featuredGame)}
                  className="bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold px-8 py-3 rounded-xl flex items-center gap-2 hover:opacity-90"
                >
                  <featuredGame.icon className="w-5 h-5" />
                  Jogar Agora
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Continuous Challenges */}
      <section>
        <div className="mb-3">
          <h2 className="text-white text-lg font-bold flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            DESAFIOS EM TEMPO CONTÍNUO
          </h2>
          <p className="text-gray-400 text-sm">Jogue quando quiser e concorra a prêmios semanais</p>
        </div>
        <div className="grid grid-cols-2 gap-3">
          {continuousGames.map((game) => (
            <div key={game.id} className="space-y-2">
              <button
                onClick={() => handleGameClick(game)}
                className="relative group w-full"
              >
                <GameCard
                  title={game.title}
                  rating={game.rating}
                  imageUrl={game.imageUrl}
                />
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="text-center">
                    <game.icon className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                    <p className="text-white text-sm px-2">{game.description}</p>
                  </div>
                </div>
              </button>
            </div>
          ))}
        </div>
      </section>

      {/* Real-time Challenges */}
      <section>
        <div className="mb-3">
          <h2 className="text-white text-lg font-bold flex items-center gap-2">
            <Timer className="w-5 h-5 text-yellow-400" />
            DESAFIOS EM TEMPO REAL
          </h2>
          <p className="text-gray-400 text-sm">Partidas rápidas contra outros jogadores</p>
        </div>
        <div className="grid grid-cols-2 gap-3">
          {realtimeGames.map((game) => (
            <div key={game.id} className="space-y-2">
              <button
                onClick={() => handleGameClick(game)}
                className="relative group w-full"
              >
                <GameCard
                  title={game.title}
                  rating={game.rating}
                  imageUrl={game.imageUrl}
                />
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="text-center">
                    <game.icon className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                    <p className="text-white text-sm px-2">{game.description}</p>
                  </div>
                </div>
              </button>
            </div>
          ))}
        </div>
      </section>
        </>
      ) : (
        <div className="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 text-center">
          <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-white font-bold text-lg mb-2">Nenhum jogo encontrado</h3>
          <p className="text-gray-400">Não há jogos internos disponíveis no momento.</p>
        </div>
      )}
    </div>
  );
}

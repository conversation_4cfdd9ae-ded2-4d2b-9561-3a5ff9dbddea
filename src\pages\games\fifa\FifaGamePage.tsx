import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Users, Trophy, Gamepad2, Star, Wallet, ChevronRight, Target, Timer, Settings } from 'lucide-react';
import CreateRoomModal from '../../../components/CreateRoomModal';

// Mock de desafios para FIFA 24
const fifaRooms = [
  {
    id: '1',
    name: 'Ultimate Team - Div 1',
    type: 'tournament',
    entryFee: 50,
    prizePool: 500,
    players: 6,
    maxPlayers: 16,
    mode: 'FUT Champions',
    map: 'Ultimate Team',
    status: 'waiting',
    startTime: '2023-11-18T20:00:00',
    format: 'Eliminação Simples',
    description: 'Torneio de FIFA 24 Ultimate Team para jogadores de divisão 1. Prêmios em dinheiro e FIFA Points.',
    host: {
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
    }
  },
  {
    id: '2',
    name: 'Amistosos 1v1',
    type: '1v1',
    entryFee: 15,
    prizePool: 30,
    players: 1,
    maxPlayers: 2,
    mode: 'Amistosos',
    map: 'Real Madrid vs Barcelona',
    status: 'waiting',
    skillLevel: 'Intermediário',
    platform: 'PlayStation 5',
    region: 'Brasil',
    host: {
      name: 'Maria Costa',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
    }
  },
  {
    id: '3',
    name: 'Copa América FIFA 24',
    type: 'tournament',
    entryFee: 30,
    prizePool: 600,
    players: 10,
    maxPlayers: 32,
    mode: 'Seleções',
    map: 'Seleções Sul-Americanas',
    status: 'waiting',
    startTime: '2023-11-25T14:00:00',
    format: 'Grupos + Eliminação',
    description: 'Torneio temático com seleções da América do Sul. Transmissão ao vivo no Twitch.',
    host: {
      name: 'Roberto Carlos',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
    }
  },
  {
    id: '4',
    name: 'Desafio Pro Clubs',
    type: '1v1',
    entryFee: 20,
    prizePool: 40,
    players: 1,
    maxPlayers: 2,
    mode: 'Pro Clubs',
    map: 'Estádio Personalizado',
    status: 'waiting',
    skillLevel: 'Avançado',
    platform: 'Xbox Series X',
    region: 'Brasil',
    host: {
      name: 'Fernando Meirelles',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
    }
  }
];

export default function FifaGamePage() {
  const { gameId } = useParams();
  const navigate = useNavigate();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [challengeFilter, setChallengeFilter] = useState<'all' | '1v1' | 'tournament'>('all');

  const game = {
    id: '2',
    title: 'FIFA 24',
    description: 'A mais recente edição do simulador de futebol mais popular do mundo',
    imageUrl: 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80',
    currentPlayers: 25000,
    rating: 4.7,
    genre: 'Esporte',
    releaseDate: '2023',
    publisher: 'EA Sports',
    features: ['Times', 'Competitivo', 'Ultimate Team', 'Carreira']
  };

  return (
    <div className="space-y-6">
      {/* Game Banner */}
      <div className="relative h-[300px] rounded-xl overflow-hidden">
        <img
          src={game.imageUrl}
          alt={game.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent">
          <div className="absolute bottom-0 left-0 right-0 p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-white mb-2">{game.title}</h1>
                <div className="flex items-center gap-4 text-gray-300">
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="bg-gradient-to-r from-rose-400 to-pink-400 text-white font-bold px-8 py-3 rounded-xl flex items-center gap-2 hover:opacity-90"
                  >
                    <Gamepad2 className="w-5 h-5" />
                    Jogar 1v1
                  </button>
                  <span className="flex items-center gap-1">
                    <Users className="w-5 h-5" />
                    {game.currentPlayers.toLocaleString()} online
                  </span>
                  <span className="flex items-center gap-1">
                    <Star className="w-5 h-5 text-yellow-400" />
                    {game.rating} / 5
                  </span>
                  <span>{game.genre}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Game Info */}
      <div className="bg-gray-800 rounded-xl p-6">
        <h2 className="text-white text-xl font-bold mb-4">Sobre o Jogo</h2>
        <p className="text-gray-400">{game.description}</p>

        <div className="grid grid-cols-2 gap-4 mt-6">
          <div>
            <h3 className="text-gray-400 text-sm mb-2">Lançamento</h3>
            <p className="text-white">{game.releaseDate}</p>
          </div>
          <div>
            <h3 className="text-gray-400 text-sm mb-2">Desenvolvedor</h3>
            <p className="text-white">{game.publisher}</p>
          </div>
        </div>

        <div className="mt-6">
          <h3 className="text-gray-400 text-sm mb-2">Características</h3>
          <div className="flex flex-wrap gap-2">
            {game.features.map((feature, index) => (
              <span
                key={index}
                className="bg-gray-700 text-white px-3 py-1 rounded-full text-sm"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Desafios Disponíveis */}
      <div className="bg-gray-800 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-white text-xl font-bold">Desafios Disponíveis</h2>
          <div className="flex gap-2">
            <button
              onClick={() => setChallengeFilter('all')}
              className={`px-3 py-2 rounded-lg text-sm ${
                challengeFilter === 'all'
                  ? 'bg-gray-700 text-white'
                  : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'}`}
            >
              Todos
            </button>
            <button
              onClick={() => setChallengeFilter('1v1')}
              className={`px-3 py-2 rounded-lg text-sm ${
                challengeFilter === '1v1'
                  ? 'bg-rose-400/20 text-rose-400'
                  : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'}`}
            >
              1v1
            </button>
            <button
              onClick={() => setChallengeFilter('tournament')}
              className={`px-3 py-2 rounded-lg text-sm ${
                challengeFilter === 'tournament'
                  ? 'bg-purple-500/20 text-purple-400'
                  : 'bg-gray-800/50 text-gray-400 hover:bg-gray-700/50'}`}
            >
              Torneios
            </button>
          </div>
        </div>

        <div className="grid gap-4">
          {fifaRooms
            .filter(room => challengeFilter === 'all' || room.type === challengeFilter)
            .map(room => (
              <div
                key={room.id}
                className="bg-gray-700 rounded-xl p-4 hover:bg-gray-600 cursor-pointer"
                onClick={() => {
                  if (room.type === 'tournament') {
                    // Redirecionar para a página de detalhes do torneio
                    navigate(`/tournaments/${room.id}`);
                  } else {
                    // Redirecionar para a sala 1v1 padrão
                    navigate(`/games/2/rooms/${room.id}`);
                  }
                }}
              >
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-white font-semibold">{room.name}</h3>
                      <span className={`text-xs px-2 py-0.5 rounded ${
                        room.type === 'tournament'
                          ? 'bg-purple-500/20 text-purple-400'
                          : 'bg-rose-400/20 text-rose-400'
                      }`}>
                        {room.type === 'tournament' ? 'Desafio Torneio' : 'Desafio 1x1'}
                      </span>
                    </div>

                    {/* Informações comuns */}
                    <div className="flex flex-wrap items-center gap-x-3 gap-y-1 text-sm text-gray-400 mb-2">
                      <span>{room.mode}</span>
                      <span>•</span>
                      <span>{room.map}</span>
                      <span>•</span>
                      <span className="flex items-center gap-1">
                        <Wallet className="w-4 h-4 text-yellow-400" />
                        R$ {room.entryFee}
                      </span>
                      <span>•</span>
                      <span className="flex items-center gap-1">
                        <Trophy className="w-4 h-4 text-green-400" />
                        R$ {room.prizePool}
                      </span>
                    </div>

                    {/* Informações específicas por tipo */}
                    {room.type === 'tournament' ? (
                      <div className="flex flex-wrap items-center gap-x-3 gap-y-1 text-xs text-gray-400">
                        <span className="flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          {room.players}/{room.maxPlayers} jogadores
                        </span>
                        <span>•</span>
                        <span className="flex items-center gap-1">
                          <Timer className="w-3 h-3" />
                          {new Date(room.startTime).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' })}
                        </span>
                        <span>•</span>
                        <span className="flex items-center gap-1">
                          <Settings className="w-3 h-3" />
                          {room.format}
                        </span>
                      </div>
                    ) : (
                      <div className="flex flex-wrap items-center gap-x-3 gap-y-1 text-xs text-gray-400">
                        <span className="flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          {room.players}/{room.maxPlayers} jogadores
                        </span>
                        <span>•</span>
                        <span className="flex items-center gap-1">
                          <Target className="w-3 h-3" />
                          Nível: {room.skillLevel}
                        </span>
                        <span>•</span>
                        <span className="flex items-center gap-1">
                          <Gamepad2 className="w-3 h-3" />
                          {room.platform}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2 sm:gap-4">
                    <div className="text-right hidden md:block">
                      <p className="text-white text-sm">{room.host.name}</p>
                      <p className="text-gray-400 text-xs">Anfitrião</p>
                    </div>
                    <img
                      src={room.host.avatar}
                      alt={room.host.name}
                      className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover"
                    />
                    <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* Create Room Modal */}
      {showCreateModal && (
        <CreateRoomModal
          onClose={() => setShowCreateModal(false)}
          onCreate={(roomData) => {
            console.log('Room created:', roomData);
            setShowCreateModal(false);
            navigate(`/games/2/rooms/new`);
          }}
          games={[
            { id: '2', title: 'FIFA 24' }
          ]}
        />
      )}
    </div>
  );
}
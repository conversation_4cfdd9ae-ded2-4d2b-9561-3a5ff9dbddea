import React, { useState, useEffect } from 'react';
import { User, X } from 'lucide-react';
import { useUser } from '../contexts/UserContext';

interface UserNameModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function UserNameModal({ isOpen, onClose }: UserNameModalProps) {
  const { setUserName, isLoggedIn } = useUser();
  const [name, setName] = useState('');
  const [error, setError] = useState('');

  // Fechar o modal se o usuário já estiver logado
  useEffect(() => {
    if (isLoggedIn) {
      onClose();
    }
  }, [isLoggedIn, onClose]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      setError('Por favor, digite seu nome');
      return;
    }
    
    setUserName(name.trim());
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-xl p-6 w-full max-w-md border border-white/10 shadow-xl animate-fadeIn">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-white">Bem-vindo à Playstrike</h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <p className="text-gray-300 mb-6">
          Para começar, precisamos saber como você quer ser chamado nas salas de jogos.
        </p>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="username" className="block text-gray-300 mb-2">
              Seu nome de jogador
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User className="w-5 h-5 text-gray-400" />
              </div>
              <input
                type="text"
                id="username"
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                  setError('');
                }}
                placeholder="Digite seu nome"
                className="bg-gray-700 text-white w-full pl-10 pr-4 py-3 rounded-lg border border-gray-600 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500 focus:outline-none"
                autoFocus
              />
            </div>
            {error && <p className="text-rose-400 text-sm mt-1">{error}</p>}
          </div>
          
          <button
            type="submit"
            className="w-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white font-bold py-3 px-4 rounded-lg hover:opacity-90 transition-opacity shadow-lg"
          >
            Continuar
          </button>
          
          <p className="text-gray-400 text-sm text-center">
            Seu nome será salvo localmente e usado para identificá-lo nas salas de jogos.
          </p>
        </form>
      </div>
    </div>
  );
}

import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Rocket } from 'lucide-react';
import GameEndScreen from '../../components/GameEndScreen';

// Game constants
const CANVAS_WIDTH = 800;
const CANVAS_HEIGHT = 600;
const ROCKET_WIDTH = 40;
const ROCKET_HEIGHT = 60;
const GRAVITY = 0.5;
const JUMP_FORCE = -8;
const OBSTACLE_WIDTH = 80;
const OBSTACLE_GAP = 200;
const OBSTACLE_SPEED = 3;
const MIN_OBSTACLE_HEIGHT = 100;

interface GameState {
  rocketY: number;
  rocketVelocity: number;
  rocketRotation: number;
  obstacles: Array<{
    x: number;
    topHeight: number;
    passed: boolean;
  }>;
  score: number;
  isGameOver: boolean;
}

export default function FlapRocket() {
  const navigate = useNavigate();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const contextRef = useRef<CanvasRenderingContext2D | null>(null);
  const animationFrameRef = useRef<number>();
  const rocketImageRef = useRef<HTMLImageElement | null>(null);

  // Obter nome do jogador do cache
  const playerName = localStorage.getItem('playerName') || 'Jogador';
  const gameStateRef = useRef<GameState>({
    rocketY: CANVAS_HEIGHT / 2,
    rocketVelocity: 0,
    rocketRotation: 0,
    obstacles: [],
    score: 0,
    isGameOver: false
  });
  const [score, setScore] = useState(0);
  const [isGameOver, setIsGameOver] = useState(false);

  // Load rocket image
  useEffect(() => {
    const rocketImage = new Image();
    rocketImage.src = 'data:image/svg+xml,' + encodeURIComponent(`
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 60">
        <path d="M20 0 L40 50 L35 60 L20 55 L5 60 L0 50 Z" fill="#FCD34D"/>
        <path d="M15 45 L25 45 L20 60 Z" fill="#F59E0B"/>
        <circle cx="20" cy="25" r="8" fill="#F59E0B"/>
      </svg>
    `);
    rocketImage.onload = () => {
      rocketImageRef.current = rocketImage;
    };
  }, []);

  const createObstacle = () => ({
    x: CANVAS_WIDTH,
    topHeight: Math.random() * (CANVAS_HEIGHT - OBSTACLE_GAP - MIN_OBSTACLE_HEIGHT * 2) + MIN_OBSTACLE_HEIGHT,
    passed: false
  });

  const handleJump = () => {
    if (!gameStateRef.current.isGameOver) {
      gameStateRef.current.rocketVelocity = JUMP_FORCE;
    }
  };

  const checkCollision = () => {
    const { rocketY, obstacles } = gameStateRef.current;
    const rocketBox = {
      x: 100,
      y: rocketY,
      width: ROCKET_WIDTH,
      height: ROCKET_HEIGHT
    };

    // Check canvas boundaries
    if (rocketY < 0 || rocketY + ROCKET_HEIGHT > CANVAS_HEIGHT) {
      return true;
    }

    // Check obstacles
    return obstacles.some(obstacle => {
      const topObstacle = {
        x: obstacle.x,
        y: 0,
        width: OBSTACLE_WIDTH,
        height: obstacle.topHeight
      };

      const bottomObstacle = {
        x: obstacle.x,
        y: obstacle.topHeight + OBSTACLE_GAP,
        width: OBSTACLE_WIDTH,
        height: CANVAS_HEIGHT - (obstacle.topHeight + OBSTACLE_GAP)
      };

      return (
        (rocketBox.x < topObstacle.x + topObstacle.width &&
          rocketBox.x + rocketBox.width > topObstacle.x &&
          rocketBox.y < topObstacle.y + topObstacle.height &&
          rocketBox.y + rocketBox.height > topObstacle.y) ||
        (rocketBox.x < bottomObstacle.x + bottomObstacle.width &&
          rocketBox.x + rocketBox.width > bottomObstacle.x &&
          rocketBox.y < bottomObstacle.y + bottomObstacle.height &&
          rocketBox.y + rocketBox.height > bottomObstacle.y)
      );
    });
  };

  const updateGameState = () => {
    const state = gameStateRef.current;

    // Update rocket
    state.rocketVelocity += GRAVITY;
    state.rocketY += state.rocketVelocity;
    state.rocketRotation = Math.min(Math.max(-45, state.rocketVelocity * 4), 45);

    // Update obstacles and check for score
    state.obstacles = state.obstacles
      .map(obstacle => {
        const newObstacle = {
          ...obstacle,
          x: obstacle.x - OBSTACLE_SPEED
        };

        // Check if obstacle is passed (score point)
        if (!obstacle.passed && newObstacle.x + OBSTACLE_WIDTH < 100) {
          newObstacle.passed = true;
          state.score += 1;
          setScore(state.score);
        }

        return newObstacle;
      })
      .filter(obstacle => obstacle.x + OBSTACLE_WIDTH > 0);

    // Add new obstacle if needed
    if (state.obstacles.length < 3) {
      const lastObstacle = state.obstacles[state.obstacles.length - 1];
      if (!lastObstacle || lastObstacle.x < CANVAS_WIDTH - 300) {
        state.obstacles.push(createObstacle());
      }
    }

    // Check collision
    if (checkCollision()) {
      state.isGameOver = true;
      setIsGameOver(true);

      // Salvar resultado no localStorage
      const gameResult = {
        playerName,
        game: 'flaprocket',
        score: state.score,
        timestamp: Date.now(),
        details: {
          finalScore: state.score,
          obstaclesPassed: state.score
        }
      };

      const savedResults = JSON.parse(localStorage.getItem('gameResults') || '[]');
      savedResults.push(gameResult);
      localStorage.setItem('gameResults', JSON.stringify(savedResults));

      console.log('🎮 Resultado do FlapRocket salvo:', gameResult);

      return false;
    }

    return true;
  };

  const drawGame = () => {
    if (!contextRef.current || !rocketImageRef.current) return;
    const ctx = contextRef.current;
    const state = gameStateRef.current;

    // Clear canvas
    ctx.fillStyle = '#1F2937';
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // Draw stars
    ctx.fillStyle = '#FFFFFF';
    for (let i = 0; i < 50; i++) {
      const x = (Date.now() / 50 + i * 100) % CANVAS_WIDTH;
      const y = (i * 73) % CANVAS_HEIGHT;
      ctx.fillRect(x, y, 2, 2);
    }

    // Draw rocket
    ctx.save();
    ctx.translate(100 + ROCKET_WIDTH / 2, state.rocketY + ROCKET_HEIGHT / 2);
    ctx.rotate((state.rocketRotation * Math.PI) / 180);
    ctx.drawImage(
      rocketImageRef.current,
      -ROCKET_WIDTH / 2,
      -ROCKET_HEIGHT / 2,
      ROCKET_WIDTH,
      ROCKET_HEIGHT
    );
    ctx.restore();

    // Draw obstacles
    ctx.fillStyle = '#4B5563';
    state.obstacles.forEach(obstacle => {
      // Top obstacle
      ctx.fillRect(obstacle.x, 0, OBSTACLE_WIDTH, obstacle.topHeight);
      // Bottom obstacle
      ctx.fillRect(
        obstacle.x,
        obstacle.topHeight + OBSTACLE_GAP,
        OBSTACLE_WIDTH,
        CANVAS_HEIGHT - (obstacle.topHeight + OBSTACLE_GAP)
      );
    });
  };

  const gameLoop = () => {
    if (updateGameState()) {
      drawGame();
      animationFrameRef.current = requestAnimationFrame(gameLoop);
    }
  };

  const startGame = () => {
    if (!canvasRef.current || !contextRef.current) return;

    // Reset game state
    gameStateRef.current = {
      rocketY: CANVAS_HEIGHT / 2,
      rocketVelocity: 0,
      rocketRotation: 0,
      obstacles: [createObstacle()],
      score: 0,
      isGameOver: false
    };

    setScore(0);
    setIsGameOver(false);

    // Start game loop
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    animationFrameRef.current = requestAnimationFrame(gameLoop);
  };

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    if (!context) return;

    contextRef.current = context;
    startGame();

    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        e.preventDefault();
        handleJump();
      }
    };

    window.addEventListener('keydown', handleKeyPress);

    return () => {
      window.removeEventListener('keydown', handleKeyPress);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-900 p-6 flex flex-col items-center">
      <div className="w-full max-w-4xl mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Rocket className="w-6 h-6 text-yellow-400" />
            <h1 className="text-xl font-bold text-white">FlapRocket</h1>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-400">Pontuação</p>
            <p className="text-lg font-bold text-yellow-400">{score}</p>
          </div>
        </div>
      </div>

      <div className="relative">
        <canvas
          ref={canvasRef}
          width={CANVAS_WIDTH}
          height={CANVAS_HEIGHT}
          onClick={handleJump}
          className="bg-gray-800 rounded-lg cursor-pointer"
        />

        {isGameOver && (
          <GameEndScreen
            score={score}
            gameId="flaprocket"
            position={1} // Posição do jogador na partida atual
            topPlayers={[
              {
                id: '1',
                name: playerName,
                avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                score: score,
                position: 1
              },
              {
                id: '2',
                name: 'Ana Silva',
                avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                score: Math.round(score * 0.8),
                position: 2
              },
              {
                id: '3',
                name: 'Lucas Oliveira',
                avatar: 'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                score: Math.round(score * 0.6),
                position: 3
              }
            ]}
            onPlayAgain={startGame}
          />
        )}
      </div>

      <div className="w-full max-w-4xl mt-6">
        <div className="bg-gray-800 rounded-xl p-4">
          <h2 className="text-white font-semibold mb-2">Como Jogar</h2>
          <p className="text-gray-400 text-sm">
            Clique na tela ou pressione espaço para fazer o foguete subir.
            Desvie dos obstáculos e marque pontos! Cada obstáculo ultrapassado vale 1 ponto.
          </p>
        </div>
      </div>
    </div>
  );
}
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import GameCard from '../components/GameCard';
import { Rocket, MousePointer, Grid, Type, Target, Trophy, Timer, ChevronDown, Users, Gamepad, Radio } from 'lucide-react';
import { Link } from 'react-router-dom';

interface Game {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  rating: number;
  players?: number;
  type: 'realtime' | 'continuous';
  isRecommended?: boolean;
  icon: typeof Rocket;
  prizePool?: number;
  entryFee?: number;
  weeklyRanking?: Array<{
    name: string;
    avatar: string;
    score: number;
  }>;
}

export default function HomePage() {
  const navigate = useNavigate();
  const [showRanking, setShowRanking] = useState(false);

  const games: Game[] = [
    {
      id: 'flaprocket',
      title: 'FLAPROCKET',
      description: 'Controle seu foguete e desvie dos obstáculos',
      variants: ['continuous', 'realtime'],
      imageUrl: 'https://images.unsplash.com/photo-1516849841032-87cbac4d88f7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
      rating: 4.8,
      players: 768,
      type: 'continuous',
      prizePool: 2450,
      entryFee: 1,
      weeklyRanking: [
        {
          name: "Ana Silva",
          avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2840
        },
        {
          name: "Lucas Oliveira",
          avatar: "https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2750
        },
        {
          name: "Maria Santos",
          avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2680
        },
        {
          name: "Pedro Costa",
          avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2590
        },
        {
          name: "Julia Lima",
          avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2520
        },
        {
          name: "Rafael Silva",
          avatar: "https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2480
        },
        {
          name: "Carla Souza",
          avatar: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2430
        },
        {
          name: "Bruno Santos",
          avatar: "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2380
        },
        {
          name: "Mariana Costa",
          avatar: "https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2340
        },
        {
          name: "Thiago Lima",
          avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
          score: 2290
        }
      ],
      icon: Rocket
    },
    {
      id: 'reaction',
      title: 'REAÇÃO RÁPIDA',
      description: 'Teste seus reflexos ao máximo',
      imageUrl: 'https://images.unsplash.com/photo-1550745165-9bc0b252726f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      rating: 4.5,
      type: 'realtime',
      icon: MousePointer
    },
    {
      id: 'colormatch',
      title: 'COLOR MATCH',
      description: 'Encontre os pares de cores antes que o tempo acabe',
      imageUrl: 'https://images.unsplash.com/photo-1513542789411-b6a5d4f31634?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      rating: 4.7,
      players: 432,
      type: 'realtime',
      icon: Grid
    },
    {
      id: 'wordscramble',
      title: 'WORD SCRAMBLE',
      description: 'Descubra as palavras embaralhadas',
      imageUrl: 'https://images.unsplash.com/photo-1544396821-4dd40b938ad3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      rating: 4.6,
      players: 523,
      type: 'realtime',
      icon: Type
    },
    {
      id: 'stickmanarcher',
      title: 'STICKMAN ARCHER',
      description: 'Teste sua precisão com arco e flecha',
      imageUrl: 'https://images.unsplash.com/photo-1511355624756-6a4d32a79e8c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      rating: 4.9,
      players: 645,
      type: 'continuous',
      icon: Target
    }
  ];

  const handleGameClick = (game: Game) => {
    if (game.type === 'continuous') {
      // Show confirmation modal for continuous games
      if (window.confirm(`Confirmar entrada de R$ ${game.entryFee?.toFixed(2)} para participar?`)) {
        navigate(`/games/${game.id}/rooms/dev/play`);
      }
    } else {
      navigate(`/games/${game.id}`);
    }
  };

  const handleDevStart = (gameId: string) => {
    navigate(`/games/${gameId}/rooms/dev/play`);
  };

  const continuousGames = games.filter(game => game.type === 'continuous');
  const realtimeGames = games.filter(game => game.type === 'realtime');
  const featuredGame = continuousGames[0];

  return (
    <div className="space-y-6">
      {/* Hero Banner */}
      <div className="relative h-[250px] sm:h-[300px] md:h-[400px] rounded-xl sm:rounded-2xl overflow-hidden">
        <img
          src="https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=2000&q=80"
          alt="Gaming Competition"
          className="absolute inset-0 w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent">
          <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 md:p-8">
            <div className="max-w-4xl">
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-1 sm:mb-2">Competições em Tempo Real</h1>
              <p className="text-base sm:text-lg md:text-xl text-gray-300 mb-3 sm:mb-6">Participe de torneios, desafie amigos e ganhe prêmios</p>
              <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-gray-300 text-sm sm:text-base">
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  <span>2.4K jogadores online</span>
                </div>
                <div className="flex items-center gap-2">
                  <Trophy className="w-5 h-5 text-yellow-400" />
                  <span>R$ 50K em prêmios</span>
                </div>
                <div className="flex items-center gap-2">
                  <Gamepad className="w-5 h-5 text-rose-400" />
                  <span>156 partidas ativas</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Continuous Challenges */}
      <section className="space-y-3">
        <h2 className="text-white text-base sm:text-lg font-bold flex items-center gap-2">
          <Trophy className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" />
          <span className="hidden xs:inline">DESAFIOS EM TEMPO CONTÍNUO</span>
          <span className="xs:hidden">DESAFIOS CONTÍNUOS</span>
        </h2>
        <p className="text-gray-400 text-xs sm:text-sm">Jogue quando quiser e concorra a prêmios semanais</p>

        <div>
          <GameCard
            title={featuredGame.title}
            rating={featuredGame.rating}
            players={featuredGame.players}
            isGameOfTheWeek={true}
            prizePool={featuredGame.prizePool}
            entryFee={featuredGame.entryFee}
            imageUrl={featuredGame.imageUrl}
            onClick={() => handleGameClick(featuredGame)}
          />
        </div>

        {/* Weekly Ranking */}
        <div className="bg-gray-800 rounded-xl overflow-hidden">
          <button
            onClick={() => setShowRanking(!showRanking)}
            className="w-full p-4 flex items-center justify-between hover:bg-gray-700"
          >
            <div className="flex items-center gap-1 sm:gap-2">
              <Trophy className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" />
              <h3 className="text-white font-bold text-sm sm:text-base">Ranking Semanal</h3>
            </div>
            <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${showRanking ? 'rotate-180' : ''}`} />
          </button>

          {showRanking && (
            <div className="p-4 pt-0 space-y-2 animate-fadeIn">
              {featuredGame.weeklyRanking?.map((player, index) => (
                <div key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700">
                  <span className={`w-6 text-center font-bold ${
                    index === 0 ? 'text-yellow-400' :
                    index === 1 ? 'text-gray-300' :
                    index === 2 ? 'text-yellow-700' :
                    'text-gray-500'
                  }`}>
                    {index + 1}º
                  </span>
                  <img
                    src={player.avatar}
                    alt={player.name}
                    className={`w-8 h-8 rounded-full object-cover ${
                      index === 0 ? 'border-2 border-yellow-400' : ''
                    }`}
                  />
                  <div className="flex-1">
                    <p className="text-white font-semibold">{player.name}</p>
                    <p className="text-sm text-gray-400">{player.score} pts</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex gap-2 mt-3">
          <button
            onClick={() => handleGameClick(featuredGame)}
            className="flex-1 bg-yellow-400 text-black font-bold py-2 sm:py-3 rounded-lg flex items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm"
          >
            <featuredGame.icon className="w-5 h-5" />
            JOGAR AGORA • R$ {featuredGame.entryFee?.toFixed(2)}
          </button>
          <button
            onClick={() => handleDevStart(featuredGame.id)}
            className="bg-gray-800 text-xs text-gray-400 px-3 rounded-lg hover:bg-gray-700"
          >
            start as dev
          </button>
        </div>
      </section>

      {/* Real-time Challenges */}
      <section>
        <div className="mb-3">
          <h2 className="text-white text-base sm:text-lg font-bold flex items-center gap-2">
            <Timer className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" />
            <span className="hidden xs:inline">DESAFIOS EM TEMPO REAL</span>
            <span className="xs:hidden">DESAFIOS REAL-TIME</span>
          </h2>
          <p className="text-gray-400 text-xs sm:text-sm">Partidas rápidas contra outros jogadores</p>
        </div>
        <div className="grid grid-cols-1 xs:grid-cols-2 gap-3">
          {realtimeGames.map((game) => (
            <div key={game.id} className="space-y-2">
              <button
                onClick={() => handleGameClick(game)}
                className="relative group w-full"
              >
                <GameCard
                  title={game.title}
                  rating={game.rating}
                  imageUrl={game.imageUrl}
                />
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="text-center">
                    <game.icon className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                    <p className="text-white text-sm px-2">{game.description}</p>
                  </div>
                </div>
              </button>
              <button
                onClick={() => handleDevStart(game.id)}
                className="w-full bg-gray-800 text-xs text-gray-400 py-1 rounded-lg hover:bg-gray-700"
              >
                start as dev
              </button>
            </div>
          ))}
        </div>
      </section>

      {/* All Continuous Challenges */}
      <section>
        <h2 className="text-white text-base sm:text-lg font-bold mb-3">TODOS OS DESAFIOS CONTÍNUOS</h2>
        <div className="grid grid-cols-1 xs:grid-cols-2 gap-3">
          {continuousGames.map((game) => (
            <div key={`quick-${game.id}`} className="space-y-2">
              <button
                onClick={() => handleGameClick(game)}
                className="relative w-full"
              >
                <GameCard
                  title={game.title}
                  rating={game.rating}
                  imageUrl={game.imageUrl}
                />
              </button>
              <button
                onClick={() => handleDevStart(game.id)}
                className="w-full bg-gray-800 text-xs text-gray-400 py-1 rounded-lg hover:bg-gray-700"
              >
                start as dev
              </button>
            </div>
          ))}
        </div>
      </section>

      {/* Earn More */}
      <section>
        <h2 className="text-white text-base sm:text-lg font-bold mb-3">GANHE MAIS!</h2>
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl p-4">
          <h3 className="text-white text-xl font-bold mb-2">2X MAIS PONTOS</h3>
          <p className="text-blue-100">Jogos que te dão o dobro de pontos</p>
        </div>
      </section>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 xs:grid-cols-2 gap-4">
        <Link
          to="/internal-games"
          className="bg-gradient-to-br from-rose-400 via-salmon-400 to-pink-400 rounded-2xl p-8 text-center hover:scale-[1.02] hover:shadow-xl transition-all duration-300 shadow-lg"
        >
          <div className="bg-white/20 p-4 rounded-xl inline-block mb-4">
            <Rocket className="w-12 h-12 text-white" />
          </div>
          <h3 className="text-black font-bold mb-1">Jogos Internos</h3>
          <p className="text-black/80 text-sm">
            Jogue nossos jogos exclusivos e ganhe prêmios
          </p>
        </Link>
        <Link
          to="/streams"
          className="bg-gradient-to-br from-fuchsia-500 via-magenta-500 to-rose-500 rounded-2xl p-8 text-center hover:scale-[1.02] hover:shadow-xl transition-all duration-300 shadow-lg"
        >
          <div className="bg-white/20 p-4 rounded-xl inline-block mb-4">
            <Radio className="w-12 h-12 text-white" />
          </div>
          <h3 className="text-white font-bold mb-1">Streams</h3>
          <p className="text-white/80 text-sm">
            Assista aos melhores jogadores e torneios
          </p>
        </Link>
      </div>
    </div>
  );
}